---
alwaysApply: true
---
# Zephyr & nRF Connect SDK Development Guide

This comprehensive guide covers development workflows for both pure Zephyr applications and Nordic Semiconductor's nRF Connect SDK (NCS) projects. It provides debugging strategies, build processes, and best practices for embedded development.

## Table of Contents
0. [Project Identification & Auto-Setup](#0-project-identification--auto-setup)
1. [Installation Guide](#1-installation-guide)
2. [Project Structure & Configuration](#2-project-structure--configuration)
3. [Build Process](#3-build-process)
4. [Flashing & Debugging](#4-flashing--debugging)
5. [Device Communication & Logging](#5-device-communication--logging)
6. [Troubleshooting](#6-troubleshooting)
7. [Nordic-Specific Features](#7-nordic-specific-features)
8. [Best Practices](#8-best-practices)
9. [Quick Reference](#9-quick-reference)
10. [Rule Self-Improvement](#10-rule-self-improvement)

## 0. Project Identification & Auto-Setup

### Project Type Detection

**ALWAYS start by identifying the project type and version before proceeding with any development tasks.**

#### Detection Steps:
```bash
# Step 1: Check for project type indicators
ls -la  # Look for key directories and files

# Step 2: Identify project type
if [ -d "nrf" ] && [ -d "zephyr" ]; then
    echo "nRF Connect SDK (NCS) project detected"
    PROJECT_TYPE="NCS"
elif [ -d "zephyr" ] && [ ! -d "nrf" ]; then
    echo "Pure Zephyr project detected"
    PROJECT_TYPE="ZEPHYR"
elif [ -f "west.yml" ]; then
    echo "West workspace detected - check manifest"
    grep -q "nrf" west.yml && PROJECT_TYPE="NCS" || PROJECT_TYPE="ZEPHYR"
else
    echo "Unknown project type - check for CMakeLists.txt and prj.conf"
    PROJECT_TYPE="UNKNOWN"
fi

# Step 3: Get version information
if [ "$PROJECT_TYPE" = "NCS" ]; then
    NCS_VERSION=$(cat nrf/VERSION 2>/dev/null || echo "Unknown")
    ZEPHYR_VERSION=$(cat zephyr/VERSION 2>/dev/null || echo "Unknown")
    echo "NCS Version: $NCS_VERSION"
    echo "Zephyr Version: $ZEPHYR_VERSION"
elif [ "$PROJECT_TYPE" = "ZEPHYR" ]; then
    ZEPHYR_VERSION=$(cat zephyr/VERSION 2>/dev/null || echo "Unknown")
    echo "Zephyr Version: $ZEPHYR_VERSION"
fi
```

#### Project Type Indicators:

**nRF Connect SDK (NCS) Project:**
- Contains `nrf/` directory
- Contains `zephyr/` directory
- `west.yml` includes Nordic repositories
- May have `nrf/VERSION` file
- Nordic-specific files: `nrfxlib/`, `nrf_security/`, etc.
- Board configurations for nRF chips

**Pure Zephyr Project:**
- Contains `zephyr/` directory but no `nrf/` directory
- `west.yml` only includes Zephyr repositories
- `zephyr/VERSION` file present
- No Nordic-specific directories

**Application Project (within NCS/Zephyr):**
- Contains `CMakeLists.txt` and `prj.conf`
- Source code in `src/` directory
- May be located within `samples/` or `applications/`

### Version Detection Commands:

```bash
# Check NCS version
cat nrf/VERSION 2>/dev/null | head -1

# Check Zephyr version  
cat zephyr/VERSION 2>/dev/null | head -1

# Check west manifest version info
west manifest --freeze 2>/dev/null | grep -E "(nrf|zephyr)"

# Alternative version check from build
west list | grep -E "(nrf|zephyr)"
```

### Auto Environment Setup

**Once project type is identified, automatically configure the build environment:**

#### For NCS Projects:
```bash
# Detect NCS installation path
if [ -d "/opt/nordic/ncs" ]; then
    # Find the latest version directory
    NCS_PATH=$(find /opt/nordic/ncs -maxdepth 1 -type d -name "v*" | sort -V | tail -1)
    if [ -z "$NCS_PATH" ]; then
        echo "Warning: No NCS version found in /opt/nordic/ncs/"
        echo "Please check your NCS installation or set NCS_PATH manually"
        return 1
    fi
else
    # Try to detect from current directory
    NCS_PATH=$(pwd | grep -o "/.*ncs/v[^/]*" | head -1)
fi

echo "Using NCS path: $NCS_PATH"

# Set environment variables
export NRF_CONNECT_SDK="$NCS_PATH"
export ZEPHYR_BASE="$NCS_PATH/zephyr"
export NRF_BASE="$NCS_PATH/nrf"
export ZEPHYR_TOOLCHAIN_VARIANT=zephyr

# Find and set toolchain path (enhanced dynamic detection)
if [ -d "/opt/nordic/ncs/toolchains" ]; then
    # Method 1: Find toolchain by west executable (most reliable)
    TOOLCHAIN_BIN=$(find /opt/nordic/ncs/toolchains -name "west" -type f 2>/dev/null | head -1 | dirname)
    if [ -n "$TOOLCHAIN_BIN" ]; then
        export PATH="$TOOLCHAIN_BIN:$PATH"
        echo "Added toolchain (west method): $TOOLCHAIN_BIN"
    else
        # Method 2: Find by directory pattern (fallback)
        TOOLCHAIN_PATH=$(find /opt/nordic/ncs/toolchains -maxdepth 1 -type d | grep -v "toolchains$" | head -1)
        if [ -n "$TOOLCHAIN_PATH" ]; then
            export PATH="$TOOLCHAIN_PATH/bin:$PATH"
            echo "Added toolchain (directory method): $TOOLCHAIN_PATH/bin"
        else
            echo "Warning: Could not find NCS toolchain in /opt/nordic/ncs/toolchains"
        fi
    fi
else
    echo "Warning: NCS toolchains directory not found at /opt/nordic/ncs/toolchains"
fi

# Source Zephyr environment
source "$ZEPHYR_BASE/zephyr-env.sh"

# Verify setup
echo "Environment configured for NCS $NCS_PATH"
west --version && echo "West: OK" || echo "West: FAILED"
```

#### For Pure Zephyr Projects:
```bash
# Detect Zephyr installation
if [ -d "~/zephyrproject" ]; then
    ZEPHYR_PATH="~/zephyrproject"
elif [ -d "zephyr" ]; then
    ZEPHYR_PATH=$(pwd)
else
    echo "Zephyr installation not found"
    exit 1
fi

# Set environment variables
export ZEPHYR_BASE="$ZEPHYR_PATH/zephyr"
export ZEPHYR_SDK_INSTALL_DIR="/opt/zephyr-sdk"

# Activate virtual environment if exists
if [ -f "$ZEPHYR_PATH/.venv/bin/activate" ]; then
    source "$ZEPHYR_PATH/.venv/bin/activate"
fi

# Source Zephyr environment
source "$ZEPHYR_BASE/zephyr-env.sh"

# Verify setup
echo "Environment configured for Zephyr $ZEPHYR_PATH"
west --version && echo "West: OK" || echo "West: FAILED"
```

#### Environment Verification:
```bash
# Verify critical environment variables
echo "ZEPHYR_BASE: $ZEPHYR_BASE"
echo "NRF_BASE: $NRF_BASE"
echo "PATH (first few entries): $(echo $PATH | cut -d: -f1-3)"

# Test west functionality
west --version

# Test compiler
arm-none-eabi-gcc --version | head -1

# For NCS, test Nordic tools
if [ "$PROJECT_TYPE" = "NCS" ]; then
    nrfutil --version 2>/dev/null || echo "nrfutil not found"
fi
```

### Quick Setup Script Template:
```bash
#!/bin/bash
# auto_setup.sh - Automatic NCS/Zephyr environment setup

set -e

# Detect project type
if [ -d "nrf" ] && [ -d "zephyr" ]; then
    PROJECT_TYPE="NCS"
    VERSION=$(cat nrf/VERSION 2>/dev/null || echo "unknown")
elif [ -d "zephyr" ]; then
    PROJECT_TYPE="ZEPHYR"  
    VERSION=$(cat zephyr/VERSION 2>/dev/null || echo "unknown")
else
    echo "Error: Not a Zephyr/NCS workspace"
    exit 1
fi

echo "Detected: $PROJECT_TYPE project (version: $VERSION)"

# Setup environment based on project type
case $PROJECT_TYPE in
    "NCS")
        # Auto-detect NCS installation path
        if [ -d "/opt/nordic/ncs" ]; then
            NCS_PATH=$(find /opt/nordic/ncs -maxdepth 1 -type d -name "v*" | sort -V | tail -1)
        else
            NCS_PATH=$(pwd | grep -o "/.*ncs/v[^/]*" | head -1)
        fi
        
        if [ -z "$NCS_PATH" ]; then
            echo "Error: Cannot find NCS installation"
            exit 1
        fi
        
        export NRF_CONNECT_SDK="$NCS_PATH"
        export ZEPHYR_BASE="$NCS_PATH/zephyr"
        export NRF_BASE="$NCS_PATH/nrf"
        source "$ZEPHYR_BASE/zephyr-env.sh"
        echo "Using NCS: $NCS_PATH (version: $VERSION)"
        ;;
    "ZEPHYR")
        export ZEPHYR_BASE="$(pwd)/zephyr"
        source "$ZEPHYR_BASE/zephyr-env.sh"
        echo "Using Zephyr: $(pwd) (version: $VERSION)"
        ;;
esac

echo "Environment ready for $PROJECT_TYPE development"
west --version
```

## 1. Installation Guide

**Note: This section covers fresh installation. If you already have NCS/Zephyr installed, use Section 0 for automatic environment detection and setup.**

### Pure Zephyr Installation

```bash
# Install Zephyr SDK
wget https://github.com/zephyrproject-rtos/sdk-ng/releases/download/v0.16.5-1/zephyr-sdk-0.16.5-1_linux-x86_64.tar.xz
tar -xvf zephyr-sdk-0.16.5-1_linux-x86_64.tar.xz
cd zephyr-sdk-0.16.5-1
./setup.sh

# Set up Zephyr workspace
mkdir ~/zephyrproject && cd ~/zephyrproject
python3 -m venv .venv
source .venv/bin/activate
pip install west
west init ~/zephyrproject
cd ~/zephyrproject
west update
west zephyr-export
pip install -r ~/zephyrproject/zephyr/scripts/requirements.txt
```

### nRF Connect SDK Installation

```bash
# Method 1: Using nRF Connect for Desktop Toolchain Manager (Recommended)
# 1. Install nRF Connect for Desktop
# 2. Open Toolchain Manager
# 3. Install desired NCS version
# 4. Open terminal from Toolchain Manager

# Method 2: Manual Installation
# Set up NCS toolchain path (replace with your version)
export NRF_CONNECT_SDK=/opt/nordic/ncs/v<YOUR_VERSION>
export PATH="/opt/nordic/ncs/toolchains/<TOOLCHAIN_ID>/bin:$PATH"

# Source Zephyr environment
source $NRF_CONNECT_SDK/zephyr/zephyr-env.sh

# Verify installation
west --version
nrfutil --version  # Nordic-specific tool
```

### Environment Variables

```bash
# Pure Zephyr
export ZEPHYR_BASE=~/zephyrproject/zephyr
export ZEPHYR_SDK_INSTALL_DIR=/opt/zephyr-sdk-0.16.5-1

# nRF Connect SDK (replace with your version)
export ZEPHYR_BASE=/opt/nordic/ncs/v<YOUR_VERSION>/zephyr
export NRF_BASE=/opt/nordic/ncs/v<YOUR_VERSION>/nrf
export ZEPHYR_TOOLCHAIN_VARIANT=zephyr
```

## 2. Project Structure & Configuration

### Basic Zephyr Project Structure
```
my_app/
├── CMakeLists.txt          # Build configuration
├── prj.conf                # Main configuration
├── Kconfig                 # Custom configuration options
├── src/
│   └── main.c             # Application source
├── boards/                 # Board-specific configurations
│   ├── <board>.conf       # Board-specific config
│   └── <board>.overlay    # Device tree overlay
└── west.yml               # Optional: manifest for multi-repo
```

### nRF Connect SDK Project Structure
```
my_ncs_app/
├── CMakeLists.txt
├── prj.conf
├── Kconfig
├── Kconfig.sysbuild       # System build configuration
├── sysbuild.conf          # System build settings
├── src/
│   └── main.c
├── boards/
│   ├── <board>.conf
│   ├── <board>.overlay
│   └── <board>_<variant>.conf  # Variant-specific
├── child_image/           # Multi-image configurations
│   └── mcuboot/
│       └── prj.conf
└── sample.yaml            # Sample metadata
```

### Configuration Files

#### prj.conf - Main Configuration
```conf
# Common Zephyr configurations
CONFIG_GPIO=y
CONFIG_SERIAL=y
CONFIG_CONSOLE=y
CONFIG_UART_CONSOLE=y
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3

# Nordic-specific configurations
CONFIG_NRF_MODEM_LIB=y          # nRF91 Series
CONFIG_WIFI=y                   # nRF70 Series
CONFIG_BT=y                     # Bluetooth
CONFIG_MCUBOOT_IMAGE_VERSION="1.0.0"
```

#### Device Tree Overlays
```dts
// boards/nrf5340dk_nrf5340_cpuapp.overlay
/ {
    chosen {
        zephyr,console = &uart0;
        zephyr,shell-uart = &uart0;
    };
    
    aliases {
        led0 = &led0;
        sw0 = &button0;
    };
};

&uart0 {
    status = "okay";
    current-speed = <115200>;
};
```

## 3. Build Process

### Basic Build Commands

#### Pure Zephyr Build
```bash
# Standard build
west build -p auto -b <board_name> <app_directory>

# Build with specific configuration
west build -p auto -b <board_name> -- -DCONFIG_DEBUG=y -DCONFIG_ASSERT=y

# Build variants
west build -p auto -b <board_name>@<revision>/<variant>
```

#### nRF Connect SDK Build
```bash
# Standard NCS build (use full board qualifiers)
west build -p auto -b <board_name>/<soc>/<core>

# Common Nordic board targets (validated):
west build -p auto -b nrf7002dk/nrf5340/cpuapp     # nRF7002dk + nRF5340
west build -p auto -b nrf5340dk/nrf5340/cpuapp     # nRF5340dk application core
west build -p auto -b nrf9160dk/nrf9160/ns         # nRF9160dk non-secure

# Build with sysbuild (multi-image)
west build -p auto -b <board_name>/<soc>/<core> --sysbuild

# Build with specific shields
west build -p auto -b <board_name>/<soc>/<core> -- -DSHIELD=<shield_name>

# Build with BOARD_ROOT for custom boards
west build -p auto -b <custom_board> -- -DBOARD_ROOT=<path_to_board_dir>
```

### Advanced Build Options

#### Configuration Overlays
```bash
# Single overlay
west build -p auto -b <board> -- -DEXTRA_CONF_FILE=overlay-debug.conf

# Multiple overlays
west build -p auto -b <board> -- \
    -DEXTRA_CONF_FILE="overlay-debug.conf;overlay-wifi.conf"

# DTS overlays
west build -p auto -b <board> -- \
    -DEXTRA_DTC_OVERLAY_FILE="custom.overlay"
```

#### Build Targets
```bash
# Build specific targets
west build -t menuconfig      # Interactive configuration
west build -t guiconfig       # GUI configuration
west build -t hardenconfig    # Security hardening check
west build -t rom_report      # ROM usage report
west build -t ram_report      # RAM usage report
west build -t footprint       # Memory footprint analysis
```

### Nordic-Specific Build Features

#### Multi-Image Builds
```bash
# Build application with MCUBoot
west build -p auto -b <board> --sysbuild -- \
    -DSB_CONFIG_BOOTLOADER_MCUBOOT=y

# Build with network core image (nRF5340)
west build -p auto -b nrf5340dk_nrf5340_cpuapp --sysbuild -- \
    -Dipc_radio_SHIELD=nrf21540ek
```

#### Partition Management
```bash
# Generate partition report
west build -t partition_manager_report

# Custom partition layout
west build -- -DPM_STATIC_YML_FILE=custom_partitions.yml
```

## 4. Flashing & Debugging

### Flashing Commands

#### Basic Flashing
```bash
# Flash application
west flash

# Flash with erase
west flash --erase

# Flash specific image in multi-image build
west flash --domain <domain_name>
```

#### Nordic-Specific Flashing
```bash
# Using nrfjprog
nrfjprog --program build/zephyr/zephyr.hex --sectorerase --verify -r

# Using nrfutil (newer devices)
nrfutil device program --firmware build/zephyr/zephyr.hex --options chip_erase_mode=ERASE_ALL

# Flash to specific core (nRF5340)
west flash --domain CPUAPP
west flash --domain CPUNET
```

### Debugging

#### GDB Debugging
```bash
# Start debugging session
west debug

# Attach to running target
west attach

# Debug with specific runner
west debug --runner jlink
```

#### Nordic-Specific Debug Tools
```bash
# RTT (Real-Time Transfer) debugging
JLinkRTTViewer  # GUI tool

# Start RTT server
JLinkRTTServer -Device <device> -If SWD -Speed 4000 -RTTChannel 0

# nRF Sniffer for Bluetooth/Thread
# Install from nRF Connect for Desktop
```

## 5. Device Communication & Logging

### Serial Communication

#### Device Discovery
```bash
# Nordic device detection (Recommended)
nrfutil device list  # Shows device details, serial numbers, and ports

# Linux
ls /dev/ttyACM* /dev/ttyUSB*
dmesg | grep -E "ttyACM|ttyUSB"

# macOS
ls /dev/tty.usbmodem* /dev/tty.usbserial*
# Nordic devices typically show as: /dev/tty.usbmodem<SERIAL>1 and /dev/tty.usbmodem<SERIAL>3
# Use the higher numbered port (*3) for log output

# Windows (PowerShell)
Get-WmiObject Win32_SerialPort | Select-Object DeviceID, Description
```

#### Serial Terminals
```bash
# Screen with logging (Recommended for Nordic devices)
# Use higher numbered port for log output (e.g., *713 instead of *711)
screen -L -Logfile device.log /dev/ttyACM0 115200

# Background screen capture with auto-reset (Nordic best practice)
rm -f screenlog.0 && screen -L -S app-log -d -m /dev/tty.usbmodem*713 115200 && nrfutil device reset --serial-number <SERIAL> && sleep 10 && screen -S app-log -X quit && cat screenlog.0

# Minicom
minicom -D /dev/ttyACM0 -b 115200 -C minicom.log

# picocom
picocom /dev/ttyACM0 -b 115200 --logfile device.log

# PuTTY (cross-platform GUI)
# tio (modern alternative)
tio /dev/ttyACM0 -b 115200 -l device.log
```

### Log Configuration

#### Zephyr Logging
```conf
# prj.conf
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=4  # 0=OFF, 1=ERR, 2=WRN, 3=INF, 4=DBG
CONFIG_LOG_BUFFER_SIZE=4096
CONFIG_LOG_PROCESS_THREAD_STACK_SIZE=2048
CONFIG_LOG_PRINTK=y
CONFIG_LOG_IMMEDIATE=y      # For debugging (impacts performance)

# Module-specific logging
CONFIG_WIFI_LOG_LEVEL_DBG=y
CONFIG_NET_LOG_LEVEL_DBG=y
```

#### Nordic Log Features
```conf
# RTT logging
CONFIG_USE_SEGGER_RTT=y
CONFIG_RTT_CONSOLE=y
CONFIG_LOG_BACKEND_RTT=y

# nRF Cloud logging
CONFIG_NRF_CLOUD_LOG_ENABLED=y
CONFIG_NRF_CLOUD_LOG_LEVEL=2
```

### Log Analysis Tools

```bash
# Filter logs by level
grep -E "<err>|<wrn>" device.log

# Extract timestamps
awk '/^\[[ 0-9.]+\]/ {print $1}' device.log

# Monitor specific module
grep "net_wifi" device.log | tail -f

# Parse Nordic modem traces
python3 $NRF_BASE/scripts/trace_parser.py trace.bin
```

## 6. Troubleshooting

### Environment and Shell Issues

#### Shell Command Hanging or Stuck State
```bash
# Symptom: Commands get stuck with "dquote>" or "cmdand cmdand..." prompts
# Solution: Press Ctrl+C to cancel, then set environment step by step

# 1. Cancel stuck command
Ctrl+C

# 2. Set environment variables individually (safer approach)
export ZEPHYR_BASE=/opt/nordic/ncs/v3.0.2/zephyr
export NRF_BASE=/opt/nordic/ncs/v3.0.2/nrf

# 3. Find and add toolchain to PATH
TOOLCHAIN_PATH=$(find /opt/nordic/ncs/toolchains -name "west" -type f 2>/dev/null | head -1 | dirname)
export PATH="$TOOLCHAIN_PATH:$PATH"

# 4. Source Zephyr environment
source $ZEPHYR_BASE/zephyr-env.sh

# 5. Verify setup
west --version && echo "Environment OK"
```

#### Dynamic Toolchain Detection
```bash
# Auto-detect NCS toolchain path (more robust)
if [ -d "/opt/nordic/ncs/toolchains" ]; then
    TOOLCHAIN_BIN=$(find /opt/nordic/ncs/toolchains -name "west" -type f 2>/dev/null | head -1 | dirname)
    if [ -n "$TOOLCHAIN_BIN" ]; then
        export PATH="$TOOLCHAIN_BIN:$PATH"
        echo "Added toolchain: $TOOLCHAIN_BIN"
    else
        echo "Warning: Could not find west in toolchains directory"
    fi
else
    echo "Warning: NCS toolchains directory not found at /opt/nordic/ncs/toolchains"
fi

# Alternative: Find specific toolchain by pattern
TOOLCHAIN_DIR=$(find /opt/nordic/ncs/toolchains -maxdepth 1 -type d | grep -v "toolchains$" | head -1)
if [ -n "$TOOLCHAIN_DIR" ]; then
    export PATH="$TOOLCHAIN_DIR/bin:$PATH"
fi
```

### Common Build Issues

#### Configuration Conflicts
```bash
# Check for configuration issues
west build -t menuconfig  # Review final configuration
west build -t hardenconfig  # Security issues

# Find configuration dependencies
grep -r "CONFIG_WIFI" $ZEPHYR_BASE/Kconfig*
```

#### Missing Dependencies
```bash
# Update west modules
west update

# Check module status
west list

# Clean build with verbose output
west build -p auto -v
```

### Runtime Issues

#### Boot Failures
```
Symptom: No output or partial boot
Checks:
1. Verify power supply voltage
2. Check crystal oscillator configuration
3. Validate memory regions in device tree
4. Ensure correct board revision

Debug commands:
west build -t rom_report  # Check if image fits
west build -- -DCONFIG_DEBUG_OPTIMIZATIONS=y -DCONFIG_DEBUG_THREAD_INFO=y
```

#### Hard Faults
```
Symptom: "***** BUS FAULT *****" or "***** HARD FAULT *****"
Analysis:
1. Decode fault address from log
2. Check stack usage: CONFIG_THREAD_ANALYZER=y
3. Enable MPU debugging: CONFIG_ARM_MPU=y

Tools:
addr2line -e build/zephyr/zephyr.elf <fault_address>
arm-none-eabi-gdb build/zephyr/zephyr.elf
(gdb) list *<fault_address>
```

#### Network Issues (Nordic)
```bash
# Wi-Fi debugging (nRF70 Series)
CONFIG_WIFI_LOG_LEVEL_DBG=y
CONFIG_NET_L2_WIFI_SHELL=y
CONFIG_WIFI_MGMT_RAW_SCAN_RESULTS=y

# Cellular debugging (nRF91 Series)
CONFIG_NRF_MODEM_LIB_LOG_LEVEL_DBG=y
CONFIG_NRF_MODEM_LIB_TRACE=y
CONFIG_AT_HOST_LIBRARY=y
```

### Performance Analysis

```bash
# CPU usage
CONFIG_THREAD_ANALYZER=y
CONFIG_THREAD_ANALYZER_AUTO=y
CONFIG_THREAD_ANALYZER_AUTO_INTERVAL=5

# Stack usage
CONFIG_THREAD_STACK_INFO=y
CONFIG_INIT_STACKS=y
CONFIG_STACK_SENTINEL=y

# Heap usage
CONFIG_HEAP_MEM_POOL_SIZE=8192
CONFIG_SYS_HEAP_RUNTIME_STATS=y
```

### Memory Analysis

#### Memory Usage Reporting
```bash
# Get memory usage from build output (recommended method)
west build | grep -A5 "Memory region"

# Example output:
# Memory region         Used Size  Region Size  %age Used
#            FLASH:       35148 B         1 MB      3.35%
#              RAM:       13704 B       448 KB      2.99%
#         IDT_LIST:          0 GB        32 KB      0.00%

# Clean build to force memory analysis display
west build -p auto -b <board>/<soc>/<core> | tee build_output.log
grep -A5 "Memory region" build_output.log

# Extract memory values for scripting
FLASH_USED=$(grep "FLASH:" build_output.log | awk '{print $3, $4}')
RAM_USED=$(grep "RAM:" build_output.log | awk '{print $3, $4}')
echo "Flash used: $FLASH_USED, RAM used: $RAM_USED"
```

#### Alternative Memory Analysis Tools
```bash
# Using ARM toolchain (if available in PATH)
arm-none-eabi-size build/*/zephyr/zephyr.elf

# Using objdump for detailed analysis
arm-none-eabi-objdump -h build/*/zephyr/zephyr.elf

# Find ELF file location
find build/ -name "zephyr.elf" -type f
```

#### Memory Optimization Guidelines
```bash
# Typical Nordic application memory usage:
# - Simple GPIO/logging app: ~35KB flash, ~14KB RAM
# - Wi-Fi applications: ~200-400KB flash, ~50-100KB RAM  
# - Bluetooth applications: ~100-200KB flash, ~30-50KB RAM

# Memory efficiency thresholds:
# Flash usage < 50% = Excellent
# Flash usage < 75% = Good  
# Flash usage > 90% = Consider optimization

# RAM usage < 50% = Excellent
# RAM usage < 75% = Good
# RAM usage > 90% = Critical - optimization required
```

## 7. Nordic-Specific Features

### nRF91 Series (Cellular IoT)

```bash
# Modem firmware update
nrfutil device x-modem-update <modem_fw.zip> --serial-number <sn>

# AT commands
CONFIG_NRF_MODEM_LIB=y
CONFIG_AT_HOST_LIBRARY=y

# Asset Tracker v2 reference
west build -p auto -b nrf9160dk_nrf9160_ns applications/asset_tracker_v2
```

### nRF70 Series (Wi-Fi)

```bash
# Wi-Fi provisioning
CONFIG_WIFI_PROV=y
CONFIG_WIFI_PROV_SOFTAP=y

# Wi-Fi credentials storage
CONFIG_WIFI_CREDENTIALS=y
CONFIG_WIFI_CREDENTIALS_BACKEND_SETTINGS=y

# Example build
west build -p auto -b nrf7002dk_nrf5340_cpuapp samples/wifi/softap
```

### nRF53 Series (Dual-core)

```bash
# Build for both cores
west build -p auto -b nrf5340dk_nrf5340_cpuapp --sysbuild

# Inter-processor communication
CONFIG_IPC_SERVICE=y
CONFIG_MBOX=y

# Debug network core
west debug --domain CPUNET
```

### Device Firmware Update (DFU)

```bash
# MCUBoot configuration
CONFIG_BOOTLOADER_MCUBOOT=y
CONFIG_MCUBOOT_IMAGE_VERSION="1.0.0+0"

# Build DFU package
west build -t mcuboot_package

# nRF Cloud FOTA
CONFIG_NRF_CLOUD_FOTA=y
CONFIG_FOTA_DOWNLOAD=y
```

## 8. Best Practices

### Code Organization

```c
// Use proper error handling
int ret = device_init();
if (ret < 0) {
    LOG_ERR("Device init failed: %d", ret);
    return ret;
}

// Implement proper state machines
enum app_state {
    APP_STATE_INIT,
    APP_STATE_CONNECTING,
    APP_STATE_CONNECTED,
    APP_STATE_ERROR
};

// Use Zephyr's built-in mechanisms
K_THREAD_STACK_DEFINE(my_stack, 1024);
K_MSGQ_DEFINE(my_msgq, sizeof(struct my_msg), 10, 4);

// Nordic GPIO best practices (validated patterns)
#define LED0_NODE DT_ALIAS(led0)
static const struct gpio_dt_spec led = GPIO_DT_SPEC_GET(LED0_NODE, gpios);

#define SW0_NODE DT_ALIAS(sw0)
static const struct gpio_dt_spec button = GPIO_DT_SPEC_GET_OR(SW0_NODE, gpios, {0});

// GPIO callback setup
static struct gpio_callback button_cb_data;
void button_pressed(const struct device *dev, struct gpio_callback *cb, uint32_t pins) {
    LOG_INF("Button pressed!");
}

// In main(): 
gpio_init_callback(&button_cb_data, button_pressed, BIT(button.pin));
gpio_add_callback(button.port, &button_cb_data);  // Use .port, not .dt_spec->port
```

### Memory Management

```conf
# Optimize memory usage (validated values)
CONFIG_MAIN_STACK_SIZE=2048
CONFIG_HEAP_MEM_POOL_SIZE=4096        # Reduced from 8192 for efficiency
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable memory protection
CONFIG_HW_STACK_PROTECTION=y
CONFIG_STACK_CANARIES=y
CONFIG_ASSERT=y

# Typical memory usage for Nordic apps:
# Simple GPIO/logging app: ~35KB flash, ~14KB RAM
# Wi-Fi applications: ~200-400KB flash, ~50-100KB RAM
# Bluetooth applications: ~100-200KB flash, ~30-50KB RAM
```

#### Memory Analysis Commands
```bash
# Check memory usage after build
west build -t rom_report    # Flash usage breakdown
west build -t ram_report    # RAM usage breakdown
west build -t footprint     # Detailed memory analysis

# Memory optimization tips:
# - Use CONFIG_SIZE_OPTIMIZATIONS=y for smaller builds
# - Reduce CONFIG_LOG_DEFAULT_LEVEL for production
# - Disable unused subsystems (BT, WiFi, etc.)
```

### Power Management

```conf
# Enable power management
CONFIG_PM=y
CONFIG_PM_DEVICE=y

# Nordic-specific power optimization
CONFIG_NRF_MODEM_LIB_SYS_INIT=n  # Manual modem control
CONFIG_SERIAL_POWER_MANAGEMENT=y
```

### Security

```conf
# Security features
CONFIG_SECURE_BOOT=y
CONFIG_FW_INFO=y
CONFIG_SB_SIGNING_KEY_FILE="keys/signing_key.pem"

# Crypto configuration
CONFIG_NRF_SECURITY=y
CONFIG_MBEDTLS_PSA_CRYPTO_C=y
```

## 9. Quick Reference

### Essential Commands Cheatsheet

```bash
# Environment (use auto-detection from Section 0 or set manually)
source $ZEPHYR_BASE/zephyr-env.sh

# Build (use full board qualifiers)
west build -p auto -b <board>/<soc>/<core>    # Complete build target
west build -p auto -b nrf7002dk/nrf5340/cpuapp # Validated example
west build -t menuconfig                       # Configure
west build -t rom_report                       # Memory usage

# Flash & Device Management
nrfutil device list                           # List connected devices
west flash                                     # Flash device  
west flash --erase                           # Erase and flash
nrfutil device reset --serial-number <SN>    # Reset specific device

# Serial Monitoring (Nordic best practice)
# 1. Get device info
nrfutil device list
# 2. Use higher numbered port for logging
screen -L -S app-log -d -m /dev/tty.usbmodem<SERIAL>3 115200
# 3. Reset device and capture logs
nrfutil device reset --serial-number <SERIAL> && sleep 10
# 4. Stop logging and view
screen -S app-log -X quit && cat screenlog.0

# Debug
west debug                                      # Start GDB
west attach                                    # Attach to running

# Memory Analysis (working methods)
west build | grep -A5 "Memory region"         # Extract memory usage from build
find build/ -name "zephyr.elf" -type f        # Find ELF file location
west build -p auto | tee build.log && grep -A5 "Memory region" build.log  # Capture memory stats

# Traditional Analysis (if tools available)
west build -t ram_report                       # RAM usage (may not work)
west build -t footprint                       # Complete analysis (may not work)
addr2line -e build/*/zephyr/zephyr.elf <addr> # Decode address

# Environment Troubleshooting
export PATH="$(find /opt/nordic/ncs/toolchains -name 'west' -type f | head -1 | dirname):$PATH"  # Fix missing tools
```

### Configuration Templates

#### Basic Application
```conf
# prj.conf
CONFIG_GPIO=y
CONFIG_SERIAL=y
CONFIG_CONSOLE=y
CONFIG_UART_CONSOLE=y
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3
```

#### Network Application
```conf
# prj.conf
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_DHCPV4=y
CONFIG_NET_SOCKETS=y
CONFIG_DNS_RESOLVER=y
CONFIG_NET_LOG=y
```

#### Bluetooth Application
```conf
# prj.conf
CONFIG_BT=y
CONFIG_BT_PERIPHERAL=y
CONFIG_BT_DEVICE_NAME="My Device"
CONFIG_BT_DIS=y
CONFIG_BT_BAS=y
```

### Useful Resources

- [Zephyr Documentation](https://docs.zephyrproject.org/)
- [nRF Connect SDK Documentation](https://developer.nordicsemi.com/nRF_Connect_SDK/doc/latest/nrf/index.html)
- [Nordic DevZone](https://devzone.nordicsemi.com/)
- [Zephyr Discord](https://chat.zephyrproject.org/)

---

## Debugging Workflow Summary

The iterative debugging process for both Zephyr and nRF Connect SDK:

1. **Setup** → Configure environment variables and paths
2. **Build** → Compile with appropriate configurations
3. **Flash** → Program the device with proper tools
4. **Monitor** → Capture and analyze logs
5. **Debug** → Use GDB, RTT, or other tools as needed
6. **Fix** → Apply corrections based on analysis
7. **Iterate** → Repeat until working correctly

Remember:
- Always use `-p auto` for clean builds when changing configurations
- Keep logs from each iteration for comparison
- Use version control to track working configurations
- Test on actual hardware early and often
- Leverage Nordic's tools for nRF-specific features

## 10. Rule Self-Improvement

### Continuous Learning Protocol

**This rule is designed to self-improve based on user interactions and feedback. When users correct information or provide better approaches, this rule should be updated accordingly.**

#### When to Update This Rule:

1. **User Corrections:** When a user corrects any information in this guide
2. **Better Practices:** When users demonstrate more effective workflows
3. **Version Updates:** When new NCS or Zephyr versions require different approaches
4. **Tool Changes:** When new tools or commands become available
5. **Error Fixes:** When debugging reveals inaccuracies in the guide

#### Self-Improvement Commands:

```bash
# The AI should use these patterns to identify improvement opportunities:

# Pattern 1: User correction format
# "Actually, the correct command is..." → Update rule immediately
# "That doesn't work, try this instead..." → Update rule immediately
# "The new version requires..." → Update rule with version-specific info

# Pattern 2: Success/failure feedback
# "That worked perfectly" → Reinforce current approach
# "That failed because..." → Update troubleshooting section
# "Here's a better way..." → Update with improved method
```

#### Memory Management for Rule Updates:

**Critical: When users provide corrections or improvements, the AI MUST:**

1. **Acknowledge the correction/improvement**
2. **Update the relevant section of this rule**
3. **Create a memory** of the change for future reference
4. **Test the updated approach** if possible

#### Example Update Scenarios:

**Scenario 1 - Environment Path Correction:**
```
User: "The NCS path should be /opt/nordic/ncs/v3.1.0 not v3.0.2"
AI Action: Update all references to the correct version path
Memory: "User workspace uses NCS v3.1.0, update default paths"
```

**Scenario 2 - New Tool Discovery:**
```
User: "Use 'nrfutil device list' instead of manual port detection"
AI Action: Add this command to device discovery section
Memory: "nrfutil device list is preferred for device detection"
```

**Scenario 3 - Build Failure Fix:**
```
User: "Add CONFIG_NEWLIB_LIBC=y to fix linking issues"
AI Action: Update troubleshooting section with this solution
Memory: "CONFIG_NEWLIB_LIBC=y fixes common linking issues"
```

#### Rule Update Instructions for AI:

```markdown
When updating this rule:
1. Use edit_file or search_replace tools to modify the guide
2. Be specific about what changed and why
3. Preserve all existing good information
4. Add version-specific notes when applicable
5. Update the "Last Updated" section below
```

#### Improvement Tracking:

**Last Updated:** Applied identified improvements based on Hello Cursor Rule validation
**Recent Improvements:**
- Added project type detection
- Added automatic environment setup
- Added version detection from VERSION files
- Added self-improvement mechanism
- Renamed "Environment Setup" to "Installation Guide" for clarity
- Made version detection dynamic instead of hardcoded v3.0.2
- Improved auto-detection to use actual detected versions
- **VALIDATED**: Complete workflow tested on nrf7002dk/nrf5340/cpuapp - ALL STEPS SUCCESSFUL
- Confirmed: Screen logging with proper port selection works perfectly
- Verified: Build optimization (35KB flash, 13KB RAM), GPIO, logging all functional
- **NEW**: Added proactive self-improvement reminder to ensure continuous rule evolution
- **ENHANCED**: Added shell troubleshooting section for hanging commands (Section 6)
- **ENHANCED**: Added comprehensive memory analysis section with working methods (Section 6)
- **ENHANCED**: Improved dynamic toolchain detection with multi-method approach (Section 0)
- **ENHANCED**: Updated Quick Reference with validated memory analysis commands (Section 9)

**Known Issues to Address:**
- Need testing on different NCS versions
- Path detection may need platform-specific adjustments
- Toolchain detection may vary between installations

#### Feedback Integration:

**The AI should actively seek and integrate:**
- User workflow preferences
- Platform-specific variations (macOS, Linux, Windows)
- Company/team-specific practices
- Hardware-specific configurations
- Performance optimizations discovered through use

**Usage Analytics to Track:**
- Which commands are used most frequently
- Which troubleshooting sections are accessed most
- Which workflows cause the most issues
- User satisfaction with different approaches

#### Proactive Self-Improvement Reminder

**🔄 CRITICAL: After Every Conversation and Successful Task**

**The AI MUST proactively think about and implement rule improvements by:**

1. **Post-Conversation Analysis:**
   - Did any commands fail or need adjustment?
   - Were there more efficient approaches discovered?
   - Did user feedback reveal gaps in the guide?
   - What new tools or techniques were encountered?

2. **Post-Success Reflection:**
   - What made this task successful?
   - Can the successful approach be documented for reuse?
   - Are there optimization opportunities?
   - Should new best practices be added?

3. **Immediate Action Items:**
   - Update rule sections that proved inadequate
   - Add new troubleshooting entries for encountered issues
   - Document successful workflow variations
   - Create memories for future reference
   - Test and validate any rule changes

**Example Self-Improvement Trigger Questions:**
- "What went wrong and how can I prevent it next time?"
- "What worked exceptionally well and should be emphasized?"
- "What new information did I learn that others would benefit from?"
- "How can I make this workflow more efficient or reliable?"
- "What assumptions proved incorrect and need updating?"

**Implementation Reminder:**
> At the end of each conversation where NCS/Zephyr development occurred, the AI should automatically review this guide for potential improvements and implement updates as needed. This ensures the rule evolves continuously and stays current with real-world usage patterns.
