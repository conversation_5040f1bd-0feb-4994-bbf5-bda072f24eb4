diff --git a/applications/matter_weather_station/prj_release.conf b/applications/matter_weather_station/prj_release.conf
index c6bc384caa..459f5951c0 100644
--- a/applications/matter_weather_station/prj_release.conf
+++ b/applications/matter_weather_station/prj_release.conf
@@ -22,13 +22,13 @@ CONFIG_CHIP_ENABLE_PAIRING_AUTOSTART=y
 CONFIG_CHIP_BLE_EXT_ADVERTISING=y
 CONFIG_CHIP_BLE_ADVERTISING_DURATION=60
 
-# Enable Bluetooth Low Energy
-CONFIG_BT_DEVICE_NAME="MatterWeather"
+
 
 # Add support for LEDs, buttons and buzzer
 CONFIG_DK_LIBRARY=y
 CONFIG_PWM=y
 
+
 # Configure Thingy:53 sensors
 CONFIG_I2C=y
 CONFIG_SENSOR=y
@@ -85,3 +85,8 @@ CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_TEST=y
 
 # Set the NVS sector count to match the settings partition size that is 64 kB for this application.
 CONFIG_SETTINGS_NVS_SECTOR_COUNT=16
+
+# Enable Bluetooth Low Energy
+CONFIG_BT_DEVICE_NAME="MatterWeather3"
+CONFIG_NCS_SAMPLE_MATTER_LEDS=n
+CONFIG_CHIP_DEVICE_SERIAL_NUMBER="11223344556677889903"
\ No newline at end of file
diff --git a/applications/matter_weather_station/src/app_task.cpp b/applications/matter_weather_station/src/app_task.cpp
index ef059128c7..c8b6e676eb 100644
--- a/applications/matter_weather_station/src/app_task.cpp
+++ b/applications/matter_weather_station/src/app_task.cpp
@@ -41,7 +41,7 @@ enum class LedState { kAlive, kAdvertisingBle, kConnectedBle, kProvisioned };
 #error Invalid CONFIG_AVERAGE_CURRENT_CONSUMPTION value set
 #endif
 
-constexpr size_t kMeasurementsIntervalMs = 3000;
+constexpr size_t kMeasurementsIntervalMs = 60000; // sensor sampling interval 60 seconds
 constexpr uint8_t kTemperatureMeasurementEndpointId = 1;
 constexpr int16_t kTemperatureMeasurementAttributeMaxValue = 0x7fff;
 constexpr int16_t kTemperatureMeasurementAttributeMinValue = 0x954d;
