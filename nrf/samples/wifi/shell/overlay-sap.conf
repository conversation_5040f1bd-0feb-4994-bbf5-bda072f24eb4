CONFIG_NRF70_AP_MODE=y
CONFIG_WIFI_NM_WPA_SUPPLICANT_AP=y

CONFIG_NET_ZPERF=y
CONFIG_ZVFS_OPEN_MAX=16
CONFIG_ZVFS_POLL_MAX=16
# Optimized networking settings for performance
CONFIG_NET_PKT_RX_COUNT=10
CONFIG_NET_PKT_TX_COUNT=10
CONFIG_NET_BUF_RX_COUNT=10
CONFIG_NET_BUF_TX_COUNT=20
CONFIG_HEAP_MEM_POOL_SIZE=43000
CONFIG_NRF_WIFI_CTRL_HEAP_SIZE=20000
CONFIG_NRF_WIFI_DATA_HEAP_SIZE=147000
CONFIG_NRF70_QSPI_LOW_POWER=n
CONFIG_NET_ZPERF_MAX_PACKET_SIZE=1500

# Necessary for zperf_tcp_receiver.c
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_INIT_TIMEOUT=0

# Consumes more memory
CONFIG_WIFI_CREDENTIALS=n
CONFIG_FLASH=n
CONFIG_NVS=n
CONFIG_SETTINGS=n

# IP is mandatory for successful STA connection
CONFIG_NET_CONFIG_MY_IPV4_ADDR="***********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
CONFIG_NET_CONFIG_MY_IPV4_GW="***********"

# Disable DHCPv4 client
CONFIG_NET_DHCPV4=n
# Enable DHCPv4 server
CONFIG_NET_DHCPV4_SERVER=y

# Temporarily enable FS support so that linking succeeds.
CONFIG_FILE_SYSTEM=y

CONFIG_MBEDTLS_HEAP_SIZE=1024
