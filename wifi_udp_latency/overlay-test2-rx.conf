#
# Configuration overlay for Test 2 RX Device
# RX device acts as <PERSON><PERSON> and receives UDP packets
#

# Device role and test mode
CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX=y
CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP=y

# SoftAP configuration
CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID="wifi-latency-test"
CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK="testpass123"


# Test parameters
CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345

# Enable AP mode support
CONFIG_NRF70_AP_MODE=y
CONFIG_WIFI_NM_WPA_SUPPLICANT_AP=y

# DHCP server for SoftAP clients
CONFIG_NET_DHCPV4_SERVER=y

# Optimize for RX operations and AP mode
CONFIG_MAIN_STACK_SIZE=8192
CONFIG_NET_RX_STACK_SIZE=4096
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=6144

# Network configuration for AP mode
CONFIG_NET_CONFIG_AUTO_INIT=n 