#
# Configuration overlay for Test 2 RX Device
# RX device acts as Soft<PERSON> and receives UDP packets
#

# Device role and test mode
CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX=y
CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP=y

# SoftAP configuration
CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID="wifi-latency-test"
CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK="testpass123"

# Test parameters
CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345

# Enable AP mode support - CRITICAL for SoftAP functionality
CONFIG_NRF70_AP_MODE=y
CONFIG_WIFI_NM_WPA_SUPPLICANT_AP=y

# Required for SoftAP - missing in original config
CONFIG_WIFI_READY_LIB=y
CONFIG_FILE_SYSTEM=y

# DHCP server for SoftAP clients
CONFIG_NET_DHCPV4_SERVER=y

# Network configuration for AP mode
CONFIG_NET_CONFIG_AUTO_INIT=n
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_INIT_TIMEOUT=0

# Static IP configuration for SoftAP
CONFIG_NET_CONFIG_MY_IPV4_ADDR="***********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
CONFIG_NET_CONFIG_MY_IPV4_GW="***********"

# Optimize for RX operations and AP mode
CONFIG_MAIN_STACK_SIZE=8192
CONFIG_NET_RX_STACK_SIZE=4096
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=6144
CONFIG_NET_TX_STACK_SIZE=4096

# Additional memory optimizations for SoftAP
CONFIG_MBEDTLS_HEAP_SIZE=1024
CONFIG_NET_MGMT_EVENT_QUEUE_TIMEOUT=5000

# Entropy support for crypto operations
CONFIG_ENTROPY_GENERATOR=y