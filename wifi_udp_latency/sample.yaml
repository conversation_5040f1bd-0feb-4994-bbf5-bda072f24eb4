sample:
  description: Wi-Fi UDP Latency Test application for nRF7002 DK
  name: Wi-Fi UDP Latency Test
tests:
  # Test 1: TX Device for external AP scenario
  sample.wifi_udp_latency.test1_tx:
    sysbuild: true
    build_only: true
    integration_platforms:
      - nrf7002dk/nrf5340/cpuapp
    platform_allow:
      - nrf7002dk/nrf5340/cpuapp
    tags:
      - ci_build
      - sysbuild
      - ci_samples_wifi
      - latency_test
    extra_args: EXTRA_CONF_FILE=overlay-test1-tx.conf

  # Test 1: RX Device for external AP scenario  
  sample.wifi_udp_latency.test1_rx:
    sysbuild: true
    build_only: true
    integration_platforms:
      - nrf7002dk/nrf5340/cpuapp
    platform_allow:
      - nrf7002dk/nrf5340/cpuapp
    tags:
      - ci_build
      - sysbuild
      - ci_samples_wifi
      - latency_test
    extra_args: EXTRA_CONF_FILE=overlay-test1-rx.conf

  # Test 2: TX Device for SoftAP scenario
  sample.wifi_udp_latency.test2_tx:
    sysbuild: true
    build_only: true
    integration_platforms:
      - nrf7002dk/nrf5340/cpuapp
    platform_allow:
      - nrf7002dk/nrf5340/cpuapp
    tags:
      - ci_build
      - sysbuild
      - ci_samples_wifi
      - latency_test
      - softap
    extra_args: EXTRA_CONF_FILE=overlay-test2-tx.conf

  # Test 2: RX Device for SoftAP scenario
  sample.wifi_udp_latency.test2_rx:
    sysbuild: true
    build_only: true
    integration_platforms:
      - nrf7002dk/nrf5340/cpuapp
    platform_allow:
      - nrf7002dk/nrf5340/cpuapp
    tags:
      - ci_build
      - sysbuild
      - ci_samples_wifi
      - latency_test
      - softap
    extra_args: EXTRA_CONF_FILE=overlay-test2-rx.conf 