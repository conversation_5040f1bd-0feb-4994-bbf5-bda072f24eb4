# Wi-Fi UDP Latency Test Application

This application is designed to measure Wi-Fi UDP latency using LED indicators to visualize when packets are transmitted and received. It provides precise timing measurements using external measurement equipment.

## Overview

The application supports two test scenarios:

1. **Test 1**: Both devices connect to an external AP
   - UDP TX Device: station mode + UDP socket TX + LED triggers
   - UDP RX Device: station mode + UDP socket RX + LED triggers

2. **Test 2**: RX device as SoftAP, TX device connects to it
   - UDP TX Device: station mode + UDP socket TX + LED triggers  
   - UDP RX Device: SoftAP mode + UDP socket RX + LED triggers

## Supported Hardware

- **nRF7002 DK** (nrf7002dk/nrf5340/cpuapp) - Primary target platform

## LED Indicators

- **LED1**: TX trigger (flashes when transmitting UDP packets)
- **LED2**: RX trigger (flashes when receiving UDP packets)

*Note: Network connectivity status is indicated through serial log messages only*

## Latency Measurement Setup

This application is designed for precise latency measurement using external measurement equipment:

### PPK2 (Power Profiler Kit II)
- Connect PPK2 to measure current spikes when LEDs flash
- LED1 GPIO spike = TX event timing
- LED2 GPIO spike = RX event timing
- Time difference = Wi-Fi UDP latency

### Oscilloscope Measurement
- **CH1**: Connect to LED1 GPIO (TX device)
- **CH2**: Connect to LED2 GPIO (RX device)
- Measure time difference between CH1 and CH2 rising edges
- This gives you the end-to-end Wi-Fi UDP latency

### GPIO Pins (nRF7002 DK)
- **LED1**: GPIO P0.28 
- **LED2**: GPIO P0.29

## Building and Running

### Prerequisites

- Nordic Connect SDK v3.0.2 or later
- nRF7002 DK development kit
- Access to a Wi-Fi network (for Test 1) or two devices (for Test 2)

### Test 1: Both devices connect to external AP

#### Building TX Device:
```bash
west build -p -b nrf7002dk/nrf5340/cpuapp -- -DEXTRA_CONF_FILE=overlay-test1-tx.conf
```

#### Building RX Device:
```bash
west build -p -b nrf7002dk/nrf5340/cpuapp -- -DEXTRA_CONF_FILE=overlay-test1-rx.conf
```

### Test 2: RX device as SoftAP

#### Building TX Device:
```bash
west build -p -b nrf7002dk/nrf5340/cpuapp -- -DEXTRA_CONF_FILE=overlay-test2-tx.conf
```

#### Building RX Device:
```bash
west build -p -b nrf7002dk/nrf5340/cpuapp -- -DEXTRA_CONF_FILE=overlay-test2-rx.conf
```

### Flashing

```bash
west flash --erase
```

## Configuration

### Wi-Fi Credentials

For Test 1, you need to configure your Wi-Fi network credentials in the overlay files:

Edit `overlay-test1-tx.conf` and `overlay-test1-rx.conf`:
```
CONFIG_WIFI_CREDENTIALS_STATIC_SSID="YourWiFiSSID"
CONFIG_WIFI_CREDENTIALS_STATIC_PASSWORD="YourWiFiPassword"
```

For Test 2, the SoftAP credentials are predefined but can be changed in the overlay files:
```
CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID="wifi-latency-test"
CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK="testpass123"
```

### IP Configuration

#### Test 1 (External AP):
- Configure the target IP in `overlay-test1-tx.conf`:
  ```
  CONFIG_WIFI_UDP_LATENCY_TARGET_IP="*************"
  ```
- Optionally set static IP for RX device in `overlay-test1-rx.conf`

#### Test 2 (SoftAP):
- RX device (SoftAP) uses IP: `***********`
- TX device gets IP from DHCP: `192.168.4.x`
- Target IP is automatically set to `***********`

### Test Parameters

Configurable parameters in overlay files:
- `CONFIG_WIFI_UDP_LATENCY_UDP_PORT`: UDP port (default: 12345)
- `CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS`: Test duration in milliseconds (default: 10000)
- `CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS`: Transmission interval (default: 1000ms)

## Running the Test

### Test 1 Setup:
1. Ensure both devices can access the same Wi-Fi network
2. Flash TX device with test1-tx configuration 
3. Flash RX device with test1-rx configuration
4. Connect measurement equipment to LED GPIOs
5. Power up both devices
6. Observe LED patterns and measure timing

### Test 2 Setup:
1. Flash RX device with test2-rx configuration (SoftAP mode)
2. Flash TX device with test2-tx configuration (station mode)
3. Connect measurement equipment to LED GPIOs
4. Power up RX device first (starts SoftAP)
5. Power up TX device (connects to SoftAP)
6. Observe LED patterns and measure timing

## Expected Behavior

### TX Device:
- Connects to Wi-Fi network (Test 1) or SoftAP (Test 2)
- Sends UDP packets at configured intervals
- LED1 flashes when transmitting packets
- Logs transmission events and timing

### RX Device:
- Connects to Wi-Fi network (Test 1) or creates SoftAP (Test 2)
- Listens for UDP packets on configured port
- LED2 flashes when receiving packets
- Logs reception events and timing

### Latency Measurement:
- Time between LED1 flash (TX) and LED2 flash (RX) = Wi-Fi UDP latency
- Use PPK2 or oscilloscope for precise GPIO timing measurement

## Troubleshooting

### Build Issues:
- Ensure NCS v3.0.2 or later is installed
- Check that all required Kconfig symbols are enabled
- Verify board configuration files are present

### Connection Issues:
- Verify Wi-Fi credentials are correct
- Check network connectivity and IP configuration
- Monitor serial logs for error messages
- Ensure devices are in range of Wi-Fi network/each other

### Measurement Issues:
- Verify GPIO connections to measurement equipment
- Check that LEDs are properly configured and working
- Ensure measurement equipment trigger levels are set correctly
- Monitor serial logs for timing information

## Implementation Details

The application uses:
- **Zephyr's Wi-Fi management APIs** for network connectivity
- **BSD sockets** for UDP communication  
- **GPIO APIs** for LED control and timing triggers
- **Modular design** with separate utility modules for each function

### Key Files:
- `src/main.c` - Main application logic and test orchestration
- `src/wifi_utils.c/h` - Wi-Fi connection and management
- `src/udp_utils.c/h` - UDP socket communication
- `src/led_utils.c/h` - LED control and timing triggers

## License

Copyright (c) 2025 Nordic Semiconductor ASA
SPDX-License-Identifier: LicenseRef-Nordic-5-Clause 