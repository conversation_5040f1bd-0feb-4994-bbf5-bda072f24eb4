# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: sysbuild_toplevel
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /opt/homebrew/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /opt/homebrew/bin/ninja -t targets
  description = All primary targets available:

