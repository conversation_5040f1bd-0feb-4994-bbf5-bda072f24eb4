# This is the CMakeCache file.
# For build in directory: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Application Binary Directory
APPLICATION_BINARY_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build

//Application Source Directory
APPLICATION_SOURCE_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/template

//No help, variable specified on the command line.
APP_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency

//Selected board
BOARD:STRING=nrf7002dk/nrf5340/cpuapp

//Main board directory for board (nrf7002dk)
BOARD_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk

//Support board extensions
BOARD_EXTENSIONS:BOOL=ON

//Selected board
CACHED_BOARD:STRING=nrf7002dk/nrf5340/cpuapp

//Selected shield
CACHED_SHIELD:STRING=

//Selected snippet
CACHED_SNIPPET:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/opt/homebrew/bin/ninja

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=sysbuild_toplevel

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
EXTRA_CONF_FILE:UNINITIALIZED=overlay-test2-rx.conf

//If desired, you can build the application with   SYSbuild configuration
// settings specified in an alternate .conf file using this parameter.
//   These settings will override the settings in the application’s
// SYSBuild config file or its   default .conf file. Multiple files
// may be listed, e.g. SB_CONF_FILE="sys1.conf sys2.conf"
SB_CONF_FILE:STRING=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf

//No help, variable specified on the command line.
WEST_PYTHON:UNINITIALIZED=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12

//Zephyr base
ZEPHYR_BASE:PATH=/opt/nordic/ncs/v3.0.2/zephyr

//Path to merged image in Intel Hex format
ZEPHYR_RUNNER_CONFIG_KERNEL_HEX:STRING=

//The directory containing a CMake configuration file for Zephyr.
Zephyr_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake

//Value Computed by CMake
sysbuild_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild

//Value Computed by CMake
sysbuild_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
sysbuild_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/template

//Value Computed by CMake
sysbuild_toplevel_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build

//Value Computed by CMake
sysbuild_toplevel_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
sysbuild_toplevel_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild


########################
# INTERNAL cache entries
########################

//List of board directories for board (nrf7002dk)
BOARD_DIRECTORIES:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=8
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12][found components: Interpreter ][v3.12.4(3.10)]
//Zephyr hardware model version
HWM:INTERNAL=v2
//Zephyr hardware model
HWMv2:INTERNAL=True
//West
WEST:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12;-m;west
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;<none>;cpython-312-darwin;abi3;/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/Frameworks/Python.framework/Versions/3.12/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=3b5830a455a2fbc00284a66acba9f023
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=
//nRF Connect SDK partition managere controlled hex file
wifi_udp_latency_NCS_RUNNER_HEX:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/merged.hex

