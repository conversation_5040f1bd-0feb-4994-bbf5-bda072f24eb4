cmake:
  board:
    name: nrf7002dk
    path:
     - /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
    qualifiers: nrf5340/cpuapp
    revision: 
  images:
   - name: wifi_udp_latency
     source-dir: /opt/nordic/ncs/v3.0.2/wifi_udp_latency
     type: MAIN
  kconfig:
    files:
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/empty.conf
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf
    user-files:
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf
version: 0.1.0
west:
  command: /opt/nordic/ncs/toolchains/ef4fc6722e/bin/west build -p -b nrf7002dk/nrf5340/cpuapp -- -DEXTRA_CONF_FILE=overlay-test2-rx.conf
  topdir: /opt/nordic/ncs/v3.0.2
  version: 1.2.0
