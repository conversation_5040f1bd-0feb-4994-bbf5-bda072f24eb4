EMPTY_0:
  address: 0xf2000
  end_address: 0xf4000
  placement:
    after:
    - settings_storage
  region: flash_primary
  size: 0x2000
app:
  address: 0x0
  end_address: 0xf0000
  region: flash_primary
  size: 0xf0000
littlefs_storage:
  address: 0xf4000
  end_address: 0xfa000
  placement:
    before:
    - nvs_storage
  region: flash_primary
  size: 0x6000
nvs_storage:
  address: 0xfa000
  end_address: 0x100000
  placement:
    before:
    - end
  region: flash_primary
  size: 0x6000
otp:
  address: 0xff8100
  end_address: 0xff83fc
  region: otp
  size: 0x2fc
rpmsg_nrf53_sram:
  address: 0x20070000
  end_address: 0x20080000
  placement:
    before:
    - end
  region: sram_primary
  size: 0x10000
settings_storage:
  address: 0xf0000
  end_address: 0xf2000
  placement:
    align:
      start: 0x4000
    before:
    - end
  region: flash_primary
  size: 0x2000
sram_primary:
  address: 0x20000000
  end_address: 0x20070000
  region: sram_primary
  size: 0x70000
