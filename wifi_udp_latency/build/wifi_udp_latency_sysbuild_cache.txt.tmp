APPLICATION_BINARY_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
APPLICATION_SOURCE_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/template
APP_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency
BOARD_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
BOARD_DIRECTORIES:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
BOARD_EXTENSIONS:BOOL=ON
CACHED_BOARD:STRING=nrf7002dk/nrf5340/cpuapp
CACHED_SHIELD:STRING=
CACHED_SNIPPET:STRING=
EXTRA_CONF_FILE:UNINITIALIZED=overlay-test2-rx.conf
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12][found components: Interpreter ][v3.12.4(3.10)]
HWM:INTERNAL=v2
HWMv2:INTERNAL=True
SB_CONF_FILE:STRING=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf
WEST:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12;-m;west
WEST_PYTHON:UNINITIALIZED=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12
ZEPHYR_BASE:PATH=/opt/nordic/ncs/v3.0.2/zephyr
Zephyr_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake
_Python3_Compiler_REASON_FAILURE:INTERNAL=
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;<none>;cpython-312-darwin;abi3;/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/Frameworks/Python.framework/Versions/3.12/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=3b5830a455a2fbc00284a66acba9f023
_Python3_NumPy_REASON_FAILURE:INTERNAL=
sysbuild_toplevel_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
sysbuild_toplevel_IS_TOP_LEVEL:STATIC=ON
sysbuild_toplevel_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild
wifi_udp_latency_NCS_RUNNER_HEX:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/merged.hex
BOARD:STRING=nrf7002dk/nrf5340/cpuapp
SYSBUILD_NAME:STRING=wifi_udp_latency
SYSBUILD_MAIN_APP:BOOL=True
