menu "nrf (/opt/nordic/ncs/v3.0.2/nrf)"
osource "/opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.sysbuild"
config ZEPHYR_NRF_MODULE
	bool
	default y
endmenu
config ZEPHYR_HOSTAP_MODULE
   bool
   default y
config ZEPHYR_MCUBOOT_MODULE
   bool
   default y
config ZEPHYR_MBEDTLS_MODULE
   bool
   default y
config ZEPHYR_OBERON_PSA_CRYPTO_MODULE
   bool
   default y
config ZEPHYR_TRUSTED_FIRMWARE_M_MODULE
   bool
   default y
config ZEPHYR_PSA_ARCH_TESTS_MODULE
   bool
   default y
config ZEPHYR_CJSON_MODULE
   bool
   default y
config ZEPHYR_AZURE_SDK_FOR_C_MODULE
   bool
   default y
config ZEPHYR_CIRRUS_LOGIC_MODULE
   bool
   default y
config ZEPHYR_OPENTHREAD_MODULE
   bool
   default y
config ZEPHYR_SUIT_GENERATOR_MODULE
   bool
   default y
config ZEPHYR_SUIT_PROCESSOR_MODULE
   bool
   default y
config ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE
   bool
   default y
config ZEPHYR_COREMARK_MODULE
   bool
   default y
config ZEPHYR_CANOPENNODE_MODULE
   bool
   default y
config ZEPHYR_CHRE_MODULE
   bool
   default y
config ZEPHYR_LZ4_MODULE
   bool
   default y
config ZEPHYR_NANOPB_MODULE
   bool
   default y
config ZEPHYR_TF_M_TESTS_MODULE
   bool
   default y
config ZEPHYR_ZSCILIB_MODULE
   bool
   default y
config ZEPHYR_CMSIS_MODULE
   bool
   default y
config ZEPHYR_CMSIS_DSP_MODULE
   bool
   default y
config ZEPHYR_CMSIS_NN_MODULE
   bool
   default y
config ZEPHYR_FATFS_MODULE
   bool
   default y
config ZEPHYR_HAL_NORDIC_MODULE
   bool
   default y
config ZEPHYR_HAL_NXP_MODULE
   bool
   default y
config ZEPHYR_HAL_ST_MODULE
   bool
   default y
config ZEPHYR_HAL_STM32_MODULE
   bool
   default y
config ZEPHYR_HAL_TDK_MODULE
   bool
   default y
config ZEPHYR_HAL_WURTHELEKTRONIK_MODULE
   bool
   default y
config ZEPHYR_LIBLC3_MODULE
   bool
   default y
config ZEPHYR_LIBMETAL_MODULE
   bool
   default y
config ZEPHYR_LITTLEFS_MODULE
   bool
   default y
config ZEPHYR_LORAMAC_NODE_MODULE
   bool
   default y
config ZEPHYR_LVGL_MODULE
   bool
   default y
config ZEPHYR_MIPI_SYS_T_MODULE
   bool
   default y
config ZEPHYR_NRF_WIFI_MODULE
   bool
   default y
config ZEPHYR_OPEN_AMP_MODULE
   bool
   default y
config ZEPHYR_PERCEPIO_MODULE
   bool
   default y
config ZEPHYR_PICOLIBC_MODULE
   bool
   default y
config ZEPHYR_SEGGER_MODULE
   bool
   default y
config ZEPHYR_TINYCRYPT_MODULE
   bool
   default y
config ZEPHYR_UOSCORE_UEDHOC_MODULE
   bool
   default y
config ZEPHYR_ZCBOR_MODULE
   bool
   default y
config ZEPHYR_NRFXLIB_MODULE
   bool
   default y
config ZEPHYR_NRF_HW_MODELS_MODULE
   bool
   default y
config ZEPHYR_CONNECTEDHOMEIP_MODULE
   bool
   default y
