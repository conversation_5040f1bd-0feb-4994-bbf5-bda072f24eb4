menu "nrf (/opt/nordic/ncs/v3.0.2/nrf)"
osource "/opt/nordic/ncs/v3.0.2/nrf/Kconfig.nrf"
config ZEPHYR_NRF_MODULE
	bool
	default y
endmenu
menu "hostap (/opt/nordic/ncs/v3.0.2/modules/lib/hostap)"
osource "$(ZEPHYR_HOSTAP_KCONFIG)"
config ZEPHYR_HOSTAP_MODULE
	bool
	default y
endmenu
menu "mcuboot (/opt/nordic/ncs/v3.0.2/bootloader/mcuboot)"
osource "$(ZEPHYR_MCUBOOT_KCONFIG)"
config ZEPHYR_MCUBOOT_MODULE
	bool
	default y
endmenu
menu "mbedtls (/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls)"
osource "$(ZEPHYR_MBEDTLS_KCONFIG)"
config ZEPHYR_MBEDTLS_MODULE
	bool
	default y
endmenu
menu "oberon-psa-crypto (/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto)"
osource "/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto/Kconfig.oberon_psa_crypto"
config ZEPHYR_OBERON_PSA_CRYPTO_MODULE
	bool
	default y
endmenu
menu "trusted-firmware-m (/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m)"
osource "$(ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG)"
config ZEPHYR_TRUSTED_FIRMWARE_M_MODULE
	bool
	default y
endmenu
config ZEPHYR_PSA_ARCH_TESTS_MODULE
	bool
	default y
menu "cjson (/opt/nordic/ncs/v3.0.2/modules/lib/cjson)"
osource "$(ZEPHYR_CJSON_KCONFIG)"
config ZEPHYR_CJSON_MODULE
	bool
	default y
endmenu
menu "azure-sdk-for-c (/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c)"
osource "$(ZEPHYR_AZURE_SDK_FOR_C_KCONFIG)"
config ZEPHYR_AZURE_SDK_FOR_C_MODULE
	bool
	default y
endmenu
menu "cirrus-logic (/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic)"
osource "/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic/Kconfig"
config ZEPHYR_CIRRUS_LOGIC_MODULE
	bool
	default y
endmenu
menu "openthread (/opt/nordic/ncs/v3.0.2/modules/lib/openthread)"
osource "$(ZEPHYR_OPENTHREAD_KCONFIG)"
config ZEPHYR_OPENTHREAD_MODULE
	bool
	default y
endmenu
menu "suit-generator (/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator/ncs/Kconfig"
config ZEPHYR_SUIT_GENERATOR_MODULE
	bool
	default y
endmenu
menu "suit-processor (/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor/Kconfig"
config ZEPHYR_SUIT_PROCESSOR_MODULE
	bool
	default y
endmenu
menu "memfault-firmware-sdk (/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk/ports/zephyr/Kconfig"
config ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE
	bool
	default y
endmenu
menu "coremark (/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark)"
osource "$(ZEPHYR_COREMARK_KCONFIG)"
config ZEPHYR_COREMARK_MODULE
	bool
	default y
endmenu
menu "canopennode (/opt/nordic/ncs/v3.0.2/modules/lib/canopennode)"
osource "$(ZEPHYR_CANOPENNODE_KCONFIG)"
config ZEPHYR_CANOPENNODE_MODULE
	bool
	default y
endmenu
menu "chre (/opt/nordic/ncs/v3.0.2/modules/lib/chre)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/chre/platform/zephyr/Kconfig"
config ZEPHYR_CHRE_MODULE
	bool
	default y
endmenu
menu "lz4 (/opt/nordic/ncs/v3.0.2/modules/lib/lz4)"
osource "$(ZEPHYR_LZ4_KCONFIG)"
config ZEPHYR_LZ4_MODULE
	bool
	default y
endmenu
menu "nanopb (/opt/nordic/ncs/v3.0.2/modules/lib/nanopb)"
osource "$(ZEPHYR_NANOPB_KCONFIG)"
config ZEPHYR_NANOPB_MODULE
	bool
	default y
endmenu
config ZEPHYR_TF_M_TESTS_MODULE
	bool
	default y
menu "zscilib (/opt/nordic/ncs/v3.0.2/modules/lib/zscilib)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/zscilib/Kconfig.zscilib"
config ZEPHYR_ZSCILIB_MODULE
	bool
	default y
endmenu
menu "cmsis (/opt/nordic/ncs/v3.0.2/modules/hal/cmsis)"
osource "$(ZEPHYR_CMSIS_KCONFIG)"
config ZEPHYR_CMSIS_MODULE
	bool
	default y
endmenu
menu "cmsis-dsp (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp)"
osource "$(ZEPHYR_CMSIS_DSP_KCONFIG)"
config ZEPHYR_CMSIS_DSP_MODULE
	bool
	default y
endmenu
menu "cmsis-nn (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn)"
osource "$(ZEPHYR_CMSIS_NN_KCONFIG)"
config ZEPHYR_CMSIS_NN_MODULE
	bool
	default y
endmenu
menu "fatfs (/opt/nordic/ncs/v3.0.2/modules/fs/fatfs)"
osource "$(ZEPHYR_FATFS_KCONFIG)"
config ZEPHYR_FATFS_MODULE
	bool
	default y
endmenu
menu "hal_nordic (/opt/nordic/ncs/v3.0.2/modules/hal/nordic)"
osource "$(ZEPHYR_HAL_NORDIC_KCONFIG)"
config ZEPHYR_HAL_NORDIC_MODULE
	bool
	default y

config ZEPHYR_HAL_NORDIC_MODULE_BLOBS
	bool
endmenu
menu "hal_nxp (/opt/nordic/ncs/v3.0.2/modules/hal/nxp)"
osource "$(ZEPHYR_HAL_NXP_KCONFIG)"
config ZEPHYR_HAL_NXP_MODULE
	bool
	default y

config ZEPHYR_HAL_NXP_MODULE_BLOBS
	bool
endmenu
menu "hal_st (/opt/nordic/ncs/v3.0.2/modules/hal/st)"
osource "$(ZEPHYR_HAL_ST_KCONFIG)"
config ZEPHYR_HAL_ST_MODULE
	bool
	default y
endmenu
config ZEPHYR_HAL_STM32_MODULE
	bool
	default y

config ZEPHYR_HAL_STM32_MODULE_BLOBS
	bool
menu "hal_tdk (/opt/nordic/ncs/v3.0.2/modules/hal/tdk)"
osource "$(ZEPHYR_HAL_TDK_KCONFIG)"
config ZEPHYR_HAL_TDK_MODULE
	bool
	default y
endmenu
config ZEPHYR_HAL_WURTHELEKTRONIK_MODULE
	bool
	default y
menu "liblc3 (/opt/nordic/ncs/v3.0.2/modules/lib/liblc3)"
osource "$(ZEPHYR_LIBLC3_KCONFIG)"
config ZEPHYR_LIBLC3_MODULE
	bool
	default y
endmenu
config ZEPHYR_LIBMETAL_MODULE
	bool
	default y
menu "littlefs (/opt/nordic/ncs/v3.0.2/modules/fs/littlefs)"
osource "$(ZEPHYR_LITTLEFS_KCONFIG)"
config ZEPHYR_LITTLEFS_MODULE
	bool
	default y
endmenu
menu "loramac-node (/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node)"
osource "$(ZEPHYR_LORAMAC_NODE_KCONFIG)"
config ZEPHYR_LORAMAC_NODE_MODULE
	bool
	default y
endmenu
menu "lvgl (/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl/zephyr/Kconfig"
config ZEPHYR_LVGL_MODULE
	bool
	default y
endmenu
config ZEPHYR_MIPI_SYS_T_MODULE
	bool
	default y
menu "nrf_wifi (/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi)"
osource "$(ZEPHYR_NRF_WIFI_KCONFIG)"
config ZEPHYR_NRF_WIFI_MODULE
	bool
	default y

config ZEPHYR_NRF_WIFI_MODULE_BLOBS
	bool
endmenu
config ZEPHYR_OPEN_AMP_MODULE
	bool
	default y
menu "percepio (/opt/nordic/ncs/v3.0.2/modules/debug/percepio)"
osource "/opt/nordic/ncs/v3.0.2/modules/debug/percepio/zephyr/Kconfig"
config ZEPHYR_PERCEPIO_MODULE
	bool
	default y
endmenu
menu "picolibc (/opt/nordic/ncs/v3.0.2/modules/lib/picolibc)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/picolibc/zephyr/Kconfig"
config ZEPHYR_PICOLIBC_MODULE
	bool
	default y
endmenu
menu "segger (/opt/nordic/ncs/v3.0.2/modules/debug/segger)"
osource "$(ZEPHYR_SEGGER_KCONFIG)"
config ZEPHYR_SEGGER_MODULE
	bool
	default y
endmenu
config ZEPHYR_TINYCRYPT_MODULE
	bool
	default y
menu "uoscore-uedhoc (/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc)"
osource "$(ZEPHYR_UOSCORE_UEDHOC_KCONFIG)"
config ZEPHYR_UOSCORE_UEDHOC_MODULE
	bool
	default y
endmenu
menu "zcbor (/opt/nordic/ncs/v3.0.2/modules/lib/zcbor)"
osource "$(ZEPHYR_ZCBOR_KCONFIG)"
config ZEPHYR_ZCBOR_MODULE
	bool
	default y
endmenu
menu "nrfxlib (/opt/nordic/ncs/v3.0.2/nrfxlib)"
osource "/opt/nordic/ncs/v3.0.2/nrfxlib/Kconfig.nrfxlib"
config ZEPHYR_NRFXLIB_MODULE
	bool
	default y
endmenu
config ZEPHYR_NRF_HW_MODELS_MODULE
	bool
	default y
menu "connectedhomeip (/opt/nordic/ncs/v3.0.2/modules/lib/matter)"
osource "/opt/nordic/ncs/v3.0.2/modules/lib/matter/config/nrfconnect/chip-module/Kconfig"
config ZEPHYR_CONNECTEDHOMEIP_MODULE
	bool
	default y
endmenu
