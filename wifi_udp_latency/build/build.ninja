# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sysbuild_toplevel
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for boards

build _sysbuild/boards: phony _sysbuild/CMakeFiles/boards


#############################################
# Utility command for shields

build _sysbuild/shields: phony _sysbuild/CMakeFiles/shields


#############################################
# Utility command for snippets

build _sysbuild/snippets: phony _sysbuild/CMakeFiles/snippets


#############################################
# Utility command for sysbuild_menuconfig

build _sysbuild/sysbuild_menuconfig: phony _sysbuild/CMakeFiles/sysbuild_menuconfig


#############################################
# Utility command for sysbuild_guiconfig

build _sysbuild/sysbuild_guiconfig: phony _sysbuild/CMakeFiles/sysbuild_guiconfig


#############################################
# Utility command for config-twister

build _sysbuild/config-twister: phony _sysbuild/CMakeFiles/config-twister


#############################################
# Utility command for build_info_yaml_saved

build _sysbuild/build_info_yaml_saved: phony _sysbuild/CMakeFiles/build_info_yaml_saved


#############################################
# Utility command for wifi_udp_latency_cache

build _sysbuild/wifi_udp_latency_cache: phony


#############################################
# Utility command for wifi_udp_latency_devicetree_target

build _sysbuild/wifi_udp_latency_devicetree_target: phony


#############################################
# Utility command for wifi_udp_latency_extra_byproducts

build _sysbuild/wifi_udp_latency_extra_byproducts: phony _sysbuild/CMakeFiles/wifi_udp_latency_extra_byproducts wifi_udp_latency/zephyr/zephyr.bin wifi_udp_latency/zephyr/zephyr.elf wifi_udp_latency/zephyr/zephyr.hex _sysbuild/sysbuild/images/wifi_udp_latency


#############################################
# Utility command for partition_manager

build _sysbuild/partition_manager: phony


#############################################
# Utility command for merged_hex

build _sysbuild/merged_hex: phony _sysbuild/CMakeFiles/merged_hex merged.hex _sysbuild/sysbuild/images/wifi_udp_latency _sysbuild/wifi_udp_latency_extra_byproducts


#############################################
# Utility command for partition_manager_report

build _sysbuild/partition_manager_report: phony _sysbuild/CMakeFiles/partition_manager_report


#############################################
# Utility command for edit_cache

build _sysbuild/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild/edit_cache: phony _sysbuild/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild/rebuild_cache: phony _sysbuild/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for _sysbuild/CMakeFiles/boards

build _sysbuild/CMakeFiles/boards | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/boards: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 /opt/nordic/ncs/v3.0.2/zephyr/scripts/list_boards.py --board-root=/opt/nordic/ncs/v3.0.2/nrf --board-root=/opt/nordic/ncs/v3.0.2/zephyr --arch-root=/opt/nordic/ncs/v3.0.2/zephyr --soc-root=/opt/nordic/ncs/v3.0.2/nrf --soc-root=/opt/nordic/ncs/v3.0.2/zephyr
  pool = console


#############################################
# Custom command for _sysbuild/CMakeFiles/shields

build _sysbuild/CMakeFiles/shields | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/shields: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/homebrew/bin/cmake -E echo adafruit_2_8_tft_touch_v2 && /opt/homebrew/bin/cmake -E echo adafruit_2_8_tft_touch_v2_nano && /opt/homebrew/bin/cmake -E echo adafruit_adalogger_featherwing && /opt/homebrew/bin/cmake -E echo adafruit_aw9523 && /opt/homebrew/bin/cmake -E echo adafruit_can_picowbell && /opt/homebrew/bin/cmake -E echo adafruit_data_logger && /opt/homebrew/bin/cmake -E echo adafruit_neopixel_grid_bff && /opt/homebrew/bin/cmake -E echo adafruit_neopixel_grid_bff_display && /opt/homebrew/bin/cmake -E echo adafruit_pca9685 && /opt/homebrew/bin/cmake -E echo adafruit_winc1500 && /opt/homebrew/bin/cmake -E echo amg88xx_eval_kit && /opt/homebrew/bin/cmake -E echo amg88xx_grid_eye_eval_shield && /opt/homebrew/bin/cmake -E echo arceli_eth_w5500 && /opt/homebrew/bin/cmake -E echo arduino_uno_click && /opt/homebrew/bin/cmake -E echo atmel_rf2xx && /opt/homebrew/bin/cmake -E echo atmel_rf2xx_arduino && /opt/homebrew/bin/cmake -E echo atmel_rf2xx_legacy && /opt/homebrew/bin/cmake -E echo atmel_rf2xx_mikrobus && /opt/homebrew/bin/cmake -E echo atmel_rf2xx_xplained && /opt/homebrew/bin/cmake -E echo atmel_rf2xx_xpro && /opt/homebrew/bin/cmake -E echo boostxl_ulpsense && /opt/homebrew/bin/cmake -E echo buydisplay_2_8_tft_touch_arduino && /opt/homebrew/bin/cmake -E echo buydisplay_3_5_tft_touch_arduino && /opt/homebrew/bin/cmake -E echo coverage_support && /opt/homebrew/bin/cmake -E echo dac80508_evm && /opt/homebrew/bin/cmake -E echo dfrobot_can_bus_v2_0 && /opt/homebrew/bin/cmake -E echo dvp_fpc24_mt9m114 && /opt/homebrew/bin/cmake -E echo esp_8266 && /opt/homebrew/bin/cmake -E echo esp_8266_arduino && /opt/homebrew/bin/cmake -E echo esp_8266_mikrobus && /opt/homebrew/bin/cmake -E echo eval_adxl362_ardz && /opt/homebrew/bin/cmake -E echo eval_adxl372_ardz && /opt/homebrew/bin/cmake -E echo frdm_cr20a && /opt/homebrew/bin/cmake -E echo frdm_kw41z && /opt/homebrew/bin/cmake -E echo frdm_stbc_agm01 && /opt/homebrew/bin/cmake -E echo ftdi_vm800c && /opt/homebrew/bin/cmake -E echo g1120b0mipi && /opt/homebrew/bin/cmake -E echo inventek_eswifi && /opt/homebrew/bin/cmake -E echo inventek_eswifi_arduino_spi && /opt/homebrew/bin/cmake -E echo inventek_eswifi_arduino_uart && /opt/homebrew/bin/cmake -E echo keyestudio_can_bus_ks0411 && /opt/homebrew/bin/cmake -E echo lcd_par_s035_8080 && /opt/homebrew/bin/cmake -E echo link_board_eth && /opt/homebrew/bin/cmake -E echo lmp90100_evb && /opt/homebrew/bin/cmake -E echo ls013b7dh03 && /opt/homebrew/bin/cmake -E echo m5stack_core2_ext && /opt/homebrew/bin/cmake -E echo max7219_8x8 && /opt/homebrew/bin/cmake -E echo mikroe_accel13_click && /opt/homebrew/bin/cmake -E echo mikroe_adc_click && /opt/homebrew/bin/cmake -E echo mikroe_ble_tiny_click && /opt/homebrew/bin/cmake -E echo mikroe_eth3_click && /opt/homebrew/bin/cmake -E echo mikroe_eth_click && /opt/homebrew/bin/cmake -E echo mikroe_mcp2518fd_click && /opt/homebrew/bin/cmake -E echo mikroe_weather_click_i2c && /opt/homebrew/bin/cmake -E echo mikroe_weather_click_spi && /opt/homebrew/bin/cmake -E echo mikroe_wifi_bt_click && /opt/homebrew/bin/cmake -E echo mikroe_wifi_bt_click_arduino && /opt/homebrew/bin/cmake -E echo mikroe_wifi_bt_click_mikrobus && /opt/homebrew/bin/cmake -E echo npm1100_ek && /opt/homebrew/bin/cmake -E echo npm1300_ek && /opt/homebrew/bin/cmake -E echo npm6001_ek && /opt/homebrew/bin/cmake -E echo nrf21540ek && /opt/homebrew/bin/cmake -E echo nrf21540ek_fwd && /opt/homebrew/bin/cmake -E echo nrf2220ek && /opt/homebrew/bin/cmake -E echo nrf2220ek_fwd && /opt/homebrew/bin/cmake -E echo nrf2240ek && /opt/homebrew/bin/cmake -E echo nrf2240ek_fwd && /opt/homebrew/bin/cmake -E echo nrf7002eb && /opt/homebrew/bin/cmake -E echo nrf7002eb2 && /opt/homebrew/bin/cmake -E echo nrf7002eb2_coex && /opt/homebrew/bin/cmake -E echo nrf7002eb2_nrf7000 && /opt/homebrew/bin/cmake -E echo nrf7002eb2_nrf7001 && /opt/homebrew/bin/cmake -E echo nrf7002eb_coex && /opt/homebrew/bin/cmake -E echo nrf7002eb_interposer_p1 && /opt/homebrew/bin/cmake -E echo nrf7002ek && /opt/homebrew/bin/cmake -E echo nrf7002ek_coex && /opt/homebrew/bin/cmake -E echo nrf7002ek_nrf7000 && /opt/homebrew/bin/cmake -E echo nrf7002ek_nrf7001 && /opt/homebrew/bin/cmake -E echo nxp_btb44_ov5640 && /opt/homebrew/bin/cmake -E echo p3t1755dp_ard_i2c && /opt/homebrew/bin/cmake -E echo p3t1755dp_ard_i3c && /opt/homebrew/bin/cmake -E echo pca63565 && /opt/homebrew/bin/cmake -E echo pca63566 && /opt/homebrew/bin/cmake -E echo pca63566_fwd && /opt/homebrew/bin/cmake -E echo pmod_acl && /opt/homebrew/bin/cmake -E echo pmod_sd && /opt/homebrew/bin/cmake -E echo renesas_us159_da14531evz && /opt/homebrew/bin/cmake -E echo reyax_lora && /opt/homebrew/bin/cmake -E echo rk043fn02h_ct && /opt/homebrew/bin/cmake -E echo rk043fn66hs_ctg && /opt/homebrew/bin/cmake -E echo rk055hdmipi4m && /opt/homebrew/bin/cmake -E echo rk055hdmipi4ma0 && /opt/homebrew/bin/cmake -E echo rpi_pico_uno_flexypin && /opt/homebrew/bin/cmake -E echo rtkmipilcdb00000be && /opt/homebrew/bin/cmake -E echo seeed_w5500 && /opt/homebrew/bin/cmake -E echo seeed_xiao_expansion_board && /opt/homebrew/bin/cmake -E echo seeed_xiao_round_display && /opt/homebrew/bin/cmake -E echo semtech_sx1262mb2das && /opt/homebrew/bin/cmake -E echo semtech_sx1272mb2das && /opt/homebrew/bin/cmake -E echo semtech_sx1276mb1mas && /opt/homebrew/bin/cmake -E echo sh1106_128x64 && /opt/homebrew/bin/cmake -E echo sparkfun_carrier_asset_tracker && /opt/homebrew/bin/cmake -E echo sparkfun_max3421e && /opt/homebrew/bin/cmake -E echo sparkfun_sara_r4 && /opt/homebrew/bin/cmake -E echo ssd1306_128x32 && /opt/homebrew/bin/cmake -E echo ssd1306_128x64 && /opt/homebrew/bin/cmake -E echo ssd1306_128x64_spi && /opt/homebrew/bin/cmake -E echo st7735r_ada_160x128 && /opt/homebrew/bin/cmake -E echo st7789v_tl019fqv01 && /opt/homebrew/bin/cmake -E echo st7789v_waveshare_240x240 && /opt/homebrew/bin/cmake -E echo st_b_lcd40_dsi1_mb1166 && /opt/homebrew/bin/cmake -E echo st_b_lcd40_dsi1_mb1166_a09 && /opt/homebrew/bin/cmake -E echo tcan4550evm && /opt/homebrew/bin/cmake -E echo ti_bp_bassensorsmkii && /opt/homebrew/bin/cmake -E echo v2c_daplink && /opt/homebrew/bin/cmake -E echo v2c_daplink_cfg && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdeh0154a07 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdeh0213b1 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdeh0213b72 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdeh029a1 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdew042t2 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdew042t2-p && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdew075t7 && /opt/homebrew/bin/cmake -E echo waveshare_epaper_gdey0213b74 && /opt/homebrew/bin/cmake -E echo waveshare_pico_ups_b && /opt/homebrew/bin/cmake -E echo weact_ov2640_cam_module && /opt/homebrew/bin/cmake -E echo wnc_m14a2a && /opt/homebrew/bin/cmake -E echo x_nucleo_53l0a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_bnrg2a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_eeprma2 && /opt/homebrew/bin/cmake -E echo x_nucleo_idb05a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks01a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks01a2 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks01a2_shub && /opt/homebrew/bin/cmake -E echo x_nucleo_iks01a3 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks01a3_shub && /opt/homebrew/bin/cmake -E echo x_nucleo_iks02a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks02a1_mic && /opt/homebrew/bin/cmake -E echo x_nucleo_iks02a1_shub && /opt/homebrew/bin/cmake -E echo x_nucleo_iks4a1 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks4a1_shub1 && /opt/homebrew/bin/cmake -E echo x_nucleo_iks4a1_shub2 && /opt/homebrew/bin/cmake -E echo x_nucleo_wb05kn1_spi && /opt/homebrew/bin/cmake -E echo x_nucleo_wb05kn1_uart
  pool = console


#############################################
# Custom command for _sysbuild/CMakeFiles/snippets

build _sysbuild/CMakeFiles/snippets | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/snippets: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/homebrew/bin/cmake -E echo bt-ll-sw-split && /opt/homebrew/bin/cmake -E echo cdc-acm-console && /opt/homebrew/bin/cmake -E echo ci-shell && /opt/homebrew/bin/cmake -E echo diagnostic-logs && /opt/homebrew/bin/cmake -E echo hpf-gpio-icbmsg && /opt/homebrew/bin/cmake -E echo hpf-gpio-icmsg && /opt/homebrew/bin/cmake -E echo hpf-gpio-mbox && /opt/homebrew/bin/cmake -E echo hpf-mspi && /opt/homebrew/bin/cmake -E echo hw-flow-control && /opt/homebrew/bin/cmake -E echo matter-debug && /opt/homebrew/bin/cmake -E echo nordic-bt-rpc && /opt/homebrew/bin/cmake -E echo nordic-flpr && /opt/homebrew/bin/cmake -E echo nordic-flpr-xip && /opt/homebrew/bin/cmake -E echo nordic-log-stm && /opt/homebrew/bin/cmake -E echo nordic-log-stm-dict && /opt/homebrew/bin/cmake -E echo nordic-ppr && /opt/homebrew/bin/cmake -E echo nordic-ppr-xip && /opt/homebrew/bin/cmake -E echo nrf54l09-switch-uart && /opt/homebrew/bin/cmake -E echo nrf70-driver-debug && /opt/homebrew/bin/cmake -E echo nrf70-driver-verbose-debug && /opt/homebrew/bin/cmake -E echo nrf70-fw-patch-ext-flash && /opt/homebrew/bin/cmake -E echo nrf70-wifi && /opt/homebrew/bin/cmake -E echo nrf91-modem-trace-ext-flash && /opt/homebrew/bin/cmake -E echo nrf91-modem-trace-ram && /opt/homebrew/bin/cmake -E echo nrf91-modem-trace-rtt && /opt/homebrew/bin/cmake -E echo nrf91-modem-trace-uart && /opt/homebrew/bin/cmake -E echo nus-console && /opt/homebrew/bin/cmake -E echo power-consumption-tests && /opt/homebrew/bin/cmake -E echo ram-console && /opt/homebrew/bin/cmake -E echo rtt-console && /opt/homebrew/bin/cmake -E echo rtt-tracing && /opt/homebrew/bin/cmake -E echo serial-console && /opt/homebrew/bin/cmake -E echo tfm-enable-share-uart && /opt/homebrew/bin/cmake -E echo wifi-enterprise && /opt/homebrew/bin/cmake -E echo wifi-ipv4 && /opt/homebrew/bin/cmake -E echo wpa-supplicant-debug && /opt/homebrew/bin/cmake -E echo xen_dom0 && /opt/homebrew/bin/cmake -E echo zperf
  pool = console


#############################################
# Custom command for _sysbuild/CMakeFiles/sysbuild_menuconfig

build _sysbuild/CMakeFiles/sysbuild_menuconfig | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/sysbuild_menuconfig: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/zephyr/kconfig && /opt/homebrew/bin/cmake -E env ZEPHYR_BASE=/opt/nordic/ncs/v3.0.2/zephyr PYTHON_EXECUTABLE=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 srctree=/opt/nordic/ncs/v3.0.2/zephyr KERNELVERSION= APPVERSION= APP_VERSION_EXTENDED_STRING= APP_VERSION_TWEAK_STRING= CONFIG_=SB_CONFIG_ KCONFIG_CONFIG=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/zephyr/.config KCONFIG_BOARD_DIR=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/Kconfig/boards BOARD=nrf7002dk BOARD_REVISION= BOARD_QUALIFIERS=/nrf5340/cpuapp HWM_SCHEME=v2 KCONFIG_BINARY_DIR=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/Kconfig APPLICATION_SOURCE_DIR=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild ZEPHYR_TOOLCHAIN_VARIANT= TOOLCHAIN_KCONFIG_DIR= TOOLCHAIN_HAS_NEWLIB=n TOOLCHAIN_HAS_PICOLIBC=n EDT_PICKLE= NCS_MEMFAULT_FIRMWARE_SDK_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/memfault-firmware-sdk/Kconfig BOARD=nrf7002dk ZEPHYR_NRF_MODULE_DIR=/opt/nordic/ncs/v3.0.2/nrf ZEPHYR_HOSTAP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/hostap ZEPHYR_HOSTAP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hostap/Kconfig ZEPHYR_MCUBOOT_MODULE_DIR=/opt/nordic/ncs/v3.0.2/bootloader/mcuboot ZEPHYR_MCUBOOT_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/mcuboot/Kconfig ZEPHYR_MBEDTLS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls ZEPHYR_MBEDTLS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/mbedtls/Kconfig ZEPHYR_OBERON_PSA_CRYPTO_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto ZEPHYR_TRUSTED_FIRMWARE_M_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/trusted-firmware-m/Kconfig ZEPHYR_PSA_ARCH_TESTS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/psa-arch-tests ZEPHYR_CJSON_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cjson ZEPHYR_CJSON_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/cjson/Kconfig ZEPHYR_AZURE_SDK_FOR_C_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c ZEPHYR_AZURE_SDK_FOR_C_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/azure-sdk-for-c/Kconfig ZEPHYR_CIRRUS_LOGIC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic ZEPHYR_OPENTHREAD_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/openthread ZEPHYR_OPENTHREAD_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/openthread/Kconfig ZEPHYR_SUIT_GENERATOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator ZEPHYR_SUIT_PROCESSOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk ZEPHYR_COREMARK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark ZEPHYR_COREMARK_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/coremark/Kconfig ZEPHYR_CANOPENNODE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/canopennode ZEPHYR_CANOPENNODE_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/canopennode/Kconfig ZEPHYR_CHRE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/chre ZEPHYR_LZ4_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/lz4 ZEPHYR_LZ4_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/lz4/Kconfig ZEPHYR_NANOPB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/nanopb ZEPHYR_NANOPB_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/nanopb/Kconfig ZEPHYR_TF_M_TESTS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/tf-m-tests ZEPHYR_ZSCILIB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/zscilib ZEPHYR_CMSIS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/cmsis ZEPHYR_CMSIS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis/Kconfig ZEPHYR_CMSIS_DSP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp ZEPHYR_CMSIS_DSP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis-dsp/Kconfig ZEPHYR_CMSIS_NN_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn ZEPHYR_CMSIS_NN_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis-nn/Kconfig ZEPHYR_FATFS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/fs/fatfs ZEPHYR_FATFS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/fatfs/Kconfig ZEPHYR_HAL_NORDIC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/nordic ZEPHYR_HAL_NORDIC_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_nordic/Kconfig ZEPHYR_HAL_NXP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/nxp ZEPHYR_HAL_NXP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_nxp/Kconfig ZEPHYR_HAL_ST_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/st ZEPHYR_HAL_ST_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_st/Kconfig ZEPHYR_HAL_STM32_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/stm32 ZEPHYR_HAL_TDK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/tdk ZEPHYR_HAL_TDK_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_tdk/Kconfig ZEPHYR_HAL_WURTHELEKTRONIK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/wurthelektronik ZEPHYR_LIBLC3_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/liblc3 ZEPHYR_LIBLC3_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/liblc3/Kconfig ZEPHYR_LIBMETAL_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/libmetal ZEPHYR_LITTLEFS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/fs/littlefs ZEPHYR_LITTLEFS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/littlefs/Kconfig ZEPHYR_LORAMAC_NODE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node ZEPHYR_LORAMAC_NODE_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/loramac-node/Kconfig ZEPHYR_LVGL_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl ZEPHYR_LVGL_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/lvgl/Kconfig ZEPHYR_MIPI_SYS_T_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/mipi-sys-t ZEPHYR_NRF_WIFI_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi ZEPHYR_NRF_WIFI_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/nrf_wifi/Kconfig ZEPHYR_OPEN_AMP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/open-amp ZEPHYR_PERCEPIO_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/percepio ZEPHYR_PERCEPIO_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/percepio/Kconfig ZEPHYR_PICOLIBC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/picolibc ZEPHYR_SEGGER_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/segger ZEPHYR_SEGGER_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/segger/Kconfig ZEPHYR_TINYCRYPT_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/tinycrypt ZEPHYR_UOSCORE_UEDHOC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc ZEPHYR_UOSCORE_UEDHOC_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/uoscore-uedhoc/Kconfig ZEPHYR_ZCBOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/zcbor ZEPHYR_ZCBOR_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/zcbor/Kconfig ZEPHYR_NRFXLIB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/nrfxlib ZEPHYR_NRF_HW_MODELS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models ZEPHYR_CONNECTEDHOMEIP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/matter ARCH=* ARCH_DIR=/opt/nordic/ncs/v3.0.2/zephyr/arch SHIELD_AS_LIST= DTS_POST_CPP= DTS_ROOT_BINDINGS= /opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 /opt/nordic/ncs/v3.0.2/zephyr/scripts/kconfig/menuconfig.py /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig
  pool = console


#############################################
# Custom command for _sysbuild/CMakeFiles/sysbuild_guiconfig

build _sysbuild/CMakeFiles/sysbuild_guiconfig | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/sysbuild_guiconfig: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/zephyr/kconfig && /opt/homebrew/bin/cmake -E env ZEPHYR_BASE=/opt/nordic/ncs/v3.0.2/zephyr PYTHON_EXECUTABLE=/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 srctree=/opt/nordic/ncs/v3.0.2/zephyr KERNELVERSION= APPVERSION= APP_VERSION_EXTENDED_STRING= APP_VERSION_TWEAK_STRING= CONFIG_=SB_CONFIG_ KCONFIG_CONFIG=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/zephyr/.config KCONFIG_BOARD_DIR=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/Kconfig/boards BOARD=nrf7002dk BOARD_REVISION= BOARD_QUALIFIERS=/nrf5340/cpuapp HWM_SCHEME=v2 KCONFIG_BINARY_DIR=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/Kconfig APPLICATION_SOURCE_DIR=/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild ZEPHYR_TOOLCHAIN_VARIANT= TOOLCHAIN_KCONFIG_DIR= TOOLCHAIN_HAS_NEWLIB=n TOOLCHAIN_HAS_PICOLIBC=n EDT_PICKLE= NCS_MEMFAULT_FIRMWARE_SDK_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/memfault-firmware-sdk/Kconfig BOARD=nrf7002dk ZEPHYR_NRF_MODULE_DIR=/opt/nordic/ncs/v3.0.2/nrf ZEPHYR_HOSTAP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/hostap ZEPHYR_HOSTAP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hostap/Kconfig ZEPHYR_MCUBOOT_MODULE_DIR=/opt/nordic/ncs/v3.0.2/bootloader/mcuboot ZEPHYR_MCUBOOT_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/mcuboot/Kconfig ZEPHYR_MBEDTLS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls ZEPHYR_MBEDTLS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/mbedtls/Kconfig ZEPHYR_OBERON_PSA_CRYPTO_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto ZEPHYR_TRUSTED_FIRMWARE_M_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/trusted-firmware-m/Kconfig ZEPHYR_PSA_ARCH_TESTS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/psa-arch-tests ZEPHYR_CJSON_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cjson ZEPHYR_CJSON_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/cjson/Kconfig ZEPHYR_AZURE_SDK_FOR_C_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c ZEPHYR_AZURE_SDK_FOR_C_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/azure-sdk-for-c/Kconfig ZEPHYR_CIRRUS_LOGIC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic ZEPHYR_OPENTHREAD_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/openthread ZEPHYR_OPENTHREAD_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/openthread/Kconfig ZEPHYR_SUIT_GENERATOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator ZEPHYR_SUIT_PROCESSOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk ZEPHYR_COREMARK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark ZEPHYR_COREMARK_KCONFIG=/opt/nordic/ncs/v3.0.2/nrf/modules/coremark/Kconfig ZEPHYR_CANOPENNODE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/canopennode ZEPHYR_CANOPENNODE_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/canopennode/Kconfig ZEPHYR_CHRE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/chre ZEPHYR_LZ4_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/lz4 ZEPHYR_LZ4_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/lz4/Kconfig ZEPHYR_NANOPB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/nanopb ZEPHYR_NANOPB_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/nanopb/Kconfig ZEPHYR_TF_M_TESTS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/tf-m-tests ZEPHYR_ZSCILIB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/zscilib ZEPHYR_CMSIS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/cmsis ZEPHYR_CMSIS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis/Kconfig ZEPHYR_CMSIS_DSP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp ZEPHYR_CMSIS_DSP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis-dsp/Kconfig ZEPHYR_CMSIS_NN_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn ZEPHYR_CMSIS_NN_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/cmsis-nn/Kconfig ZEPHYR_FATFS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/fs/fatfs ZEPHYR_FATFS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/fatfs/Kconfig ZEPHYR_HAL_NORDIC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/nordic ZEPHYR_HAL_NORDIC_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_nordic/Kconfig ZEPHYR_HAL_NXP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/nxp ZEPHYR_HAL_NXP_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_nxp/Kconfig ZEPHYR_HAL_ST_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/st ZEPHYR_HAL_ST_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_st/Kconfig ZEPHYR_HAL_STM32_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/stm32 ZEPHYR_HAL_TDK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/tdk ZEPHYR_HAL_TDK_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/hal_tdk/Kconfig ZEPHYR_HAL_WURTHELEKTRONIK_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/wurthelektronik ZEPHYR_LIBLC3_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/liblc3 ZEPHYR_LIBLC3_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/liblc3/Kconfig ZEPHYR_LIBMETAL_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/hal/libmetal ZEPHYR_LITTLEFS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/fs/littlefs ZEPHYR_LITTLEFS_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/littlefs/Kconfig ZEPHYR_LORAMAC_NODE_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node ZEPHYR_LORAMAC_NODE_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/loramac-node/Kconfig ZEPHYR_LVGL_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl ZEPHYR_LVGL_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/lvgl/Kconfig ZEPHYR_MIPI_SYS_T_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/mipi-sys-t ZEPHYR_NRF_WIFI_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi ZEPHYR_NRF_WIFI_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/nrf_wifi/Kconfig ZEPHYR_OPEN_AMP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/open-amp ZEPHYR_PERCEPIO_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/percepio ZEPHYR_PERCEPIO_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/percepio/Kconfig ZEPHYR_PICOLIBC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/picolibc ZEPHYR_SEGGER_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/debug/segger ZEPHYR_SEGGER_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/segger/Kconfig ZEPHYR_TINYCRYPT_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/crypto/tinycrypt ZEPHYR_UOSCORE_UEDHOC_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc ZEPHYR_UOSCORE_UEDHOC_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/uoscore-uedhoc/Kconfig ZEPHYR_ZCBOR_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/zcbor ZEPHYR_ZCBOR_KCONFIG=/opt/nordic/ncs/v3.0.2/zephyr/modules/zcbor/Kconfig ZEPHYR_NRFXLIB_MODULE_DIR=/opt/nordic/ncs/v3.0.2/nrfxlib ZEPHYR_NRF_HW_MODELS_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models ZEPHYR_CONNECTEDHOMEIP_MODULE_DIR=/opt/nordic/ncs/v3.0.2/modules/lib/matter ARCH=* ARCH_DIR=/opt/nordic/ncs/v3.0.2/zephyr/arch SHIELD_AS_LIST= DTS_POST_CPP= DTS_ROOT_BINDINGS= /opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 /opt/nordic/ncs/v3.0.2/zephyr/scripts/kconfig/guiconfig.py /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig
  pool = console


#############################################
# Phony custom command for _sysbuild/CMakeFiles/config-twister

build _sysbuild/CMakeFiles/config-twister | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/config-twister: phony zephyr/.config


#############################################
# Phony custom command for _sysbuild/CMakeFiles/build_info_yaml_saved

build _sysbuild/CMakeFiles/build_info_yaml_saved | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/build_info_yaml_saved: phony build_info.yml


#############################################
# Custom command for _sysbuild/CMakeFiles/wifi_udp_latency_extra_byproducts

build _sysbuild/CMakeFiles/wifi_udp_latency_extra_byproducts wifi_udp_latency/zephyr/zephyr.bin wifi_udp_latency/zephyr/zephyr.elf wifi_udp_latency/zephyr/zephyr.hex | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/wifi_udp_latency_extra_byproducts ${cmake_ninja_workdir}wifi_udp_latency/zephyr/zephyr.bin ${cmake_ninja_workdir}wifi_udp_latency/zephyr/zephyr.elf ${cmake_ninja_workdir}wifi_udp_latency/zephyr/zephyr.hex: CUSTOM_COMMAND || _sysbuild/sysbuild/images/wifi_udp_latency
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/homebrew/bin/cmake -E true
  restat = 1


#############################################
# Phony custom command for _sysbuild/CMakeFiles/merged_hex

build _sysbuild/CMakeFiles/merged_hex | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/merged_hex: phony merged.hex || _sysbuild/sysbuild/images/wifi_udp_latency _sysbuild/wifi_udp_latency_extra_byproducts


#############################################
# Custom command for merged.hex

build merged.hex | ${cmake_ninja_workdir}merged.hex: CUSTOM_COMMAND wifi_udp_latency/zephyr/zephyr.hex || _sysbuild/sysbuild/images/wifi_udp_latency _sysbuild/wifi_udp_latency_extra_byproducts
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 /opt/nordic/ncs/v3.0.2/zephyr/scripts/build/mergehex.py -o /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/merged.hex --overlap=replace /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/zephyr.hex
  DESC = Generating ../merged.hex
  restat = 1


#############################################
# Custom command for _sysbuild/CMakeFiles/partition_manager_report

build _sysbuild/CMakeFiles/partition_manager_report | ${cmake_ninja_workdir}_sysbuild/CMakeFiles/partition_manager_report: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild && /opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/bin/python3.12 /opt/nordic/ncs/v3.0.2/nrf/scripts/partition_manager_report.py --input /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/partitions.yml

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_images.cmake
# =============================================================================


#############################################
# Utility command for menuconfig

build _sysbuild/sysbuild/images/menuconfig: phony _sysbuild/sysbuild/images/CMakeFiles/menuconfig


#############################################
# Utility command for hardenconfig

build _sysbuild/sysbuild/images/hardenconfig: phony _sysbuild/sysbuild/images/CMakeFiles/hardenconfig


#############################################
# Utility command for guiconfig

build _sysbuild/sysbuild/images/guiconfig: phony _sysbuild/sysbuild/images/CMakeFiles/guiconfig


#############################################
# Utility command for wifi_udp_latency

build _sysbuild/sysbuild/images/wifi_udp_latency: phony _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-done _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update


#############################################
# Utility command for edit_cache

build _sysbuild/sysbuild/images/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/edit_cache: phony _sysbuild/sysbuild/images/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild/sysbuild/images/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/rebuild_cache: phony _sysbuild/sysbuild/images/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for _sysbuild/sysbuild/images/CMakeFiles/menuconfig

build _sysbuild/sysbuild/images/CMakeFiles/menuconfig | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/CMakeFiles/menuconfig: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/ninja menuconfig
  pool = console


#############################################
# Custom command for _sysbuild/sysbuild/images/CMakeFiles/hardenconfig

build _sysbuild/sysbuild/images/CMakeFiles/hardenconfig | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/CMakeFiles/hardenconfig: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/ninja hardenconfig
  pool = console


#############################################
# Custom command for _sysbuild/sysbuild/images/CMakeFiles/guiconfig

build _sysbuild/sysbuild/images/CMakeFiles/guiconfig | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/CMakeFiles/guiconfig: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/ninja guiconfig
  pool = console


#############################################
# Phony custom command for _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency

build _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency: phony _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete


#############################################
# Custom command for _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete

build _sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-done | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-done: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake -E make_directory /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/CMakeFiles && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/CMakeFiles/wifi_udp_latency-complete && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-done
  DESC = Completed 'wifi_udp_latency'
  restat = 1


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/cmake --build .
  DESC = Performing build step for 'wifi_udp_latency'
  pool = console


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/tmp/wifi_udp_latency-cfgcmd.txt _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-configure
  DESC = No configure step for 'wifi_udp_latency'
  restat = 1


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-source_dirinfo.txt _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download
  DESC = No download step for 'wifi_udp_latency'
  restat = 1


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-install: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-build
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency && /opt/homebrew/bin/cmake -E echo_append
  DESC = No install step for 'wifi_udp_latency'


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake -Dcfgdir= -P /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/tmp/wifi_udp_latency-mkdirs.cmake && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-mkdir
  DESC = Creating directories for 'wifi_udp_latency'
  restat = 1


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch-info.txt _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-patch
  DESC = No patch step for 'wifi_udp_latency'
  restat = 1


#############################################
# Custom command for _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update

build _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update | ${cmake_ninja_workdir}_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update: CUSTOM_COMMAND _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update-info.txt _sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-download
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/wifi_udp_latency-update
  DESC = No update step for 'wifi_udp_latency'
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild/sysbuild/images/bootloader/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/bootloader && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/bootloader/edit_cache: phony _sysbuild/sysbuild/images/bootloader/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild/sysbuild/images/bootloader/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/bootloader && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/bootloader/rebuild_cache: phony _sysbuild/sysbuild/images/bootloader/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build modules/nrf/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/nrf && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build modules/nrf/edit_cache: phony modules/nrf/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build modules/nrf/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/nrf && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build modules/nrf/rebuild_cache: phony modules/nrf/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build modules/mcuboot/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/mcuboot && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build modules/mcuboot/edit_cache: phony modules/mcuboot/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build modules/mcuboot/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/mcuboot && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build modules/mcuboot/rebuild_cache: phony modules/mcuboot/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild/sysbuild/images/boards/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/boards && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/boards/edit_cache: phony _sysbuild/sysbuild/images/boards/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild/sysbuild/images/boards/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/boards && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/boards/rebuild_cache: phony _sysbuild/sysbuild/images/boards/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild/sysbuild/images/soc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/soc && /opt/homebrew/bin/ccmake -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/soc/edit_cache: phony _sysbuild/sysbuild/images/soc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild/sysbuild/images/soc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/soc && /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild/sysbuild/images/soc/rebuild_cache: phony _sysbuild/sysbuild/images/soc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build boards: phony _sysbuild/boards

build build_info_yaml_saved: phony _sysbuild/build_info_yaml_saved

build config-twister: phony _sysbuild/config-twister

build guiconfig: phony _sysbuild/sysbuild/images/guiconfig

build hardenconfig: phony _sysbuild/sysbuild/images/hardenconfig

build menuconfig: phony _sysbuild/sysbuild/images/menuconfig

build merged_hex: phony _sysbuild/merged_hex

build partition_manager: phony _sysbuild/partition_manager

build partition_manager_report: phony _sysbuild/partition_manager_report

build shields: phony _sysbuild/shields

build snippets: phony _sysbuild/snippets

build sysbuild_guiconfig: phony _sysbuild/sysbuild_guiconfig

build sysbuild_menuconfig: phony _sysbuild/sysbuild_menuconfig

build wifi_udp_latency: phony _sysbuild/sysbuild/images/wifi_udp_latency

build wifi_udp_latency_cache: phony _sysbuild/wifi_udp_latency_cache

build wifi_udp_latency_devicetree_target: phony _sysbuild/wifi_udp_latency_devicetree_target

build wifi_udp_latency_extra_byproducts: phony _sysbuild/wifi_udp_latency_extra_byproducts

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build

build all: phony _sysbuild/all

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild

build _sysbuild/all: phony _sysbuild/build_info_yaml_saved _sysbuild/merged_hex _sysbuild/sysbuild/images/all

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images

build _sysbuild/sysbuild/images/all: phony _sysbuild/sysbuild/images/wifi_udp_latency _sysbuild/sysbuild/images/bootloader/all modules/nrf/all modules/mcuboot/all _sysbuild/sysbuild/images/boards/all _sysbuild/sysbuild/images/soc/all

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/boards

build _sysbuild/sysbuild/images/boards/all: phony

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/bootloader

build _sysbuild/sysbuild/images/bootloader/all: phony

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/soc

build _sysbuild/sysbuild/images/soc/all: phony

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/mcuboot

build modules/mcuboot/all: phony

# =============================================================================

#############################################
# Folder: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/nrf

build modules/nrf/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/bootloader/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/nrf/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/modules/mcuboot/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/boards/cmake_install.cmake /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/soc/cmake_install.cmake: RERUN_CMAKE | /opt/homebrew/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeNinjaFindMake.cmake /opt/homebrew/share/cmake/Modules/CMakeSystem.cmake.in /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/CheckCCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake /opt/homebrew/share/cmake/Modules/FindPython3.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake /opt/nordic/ncs/v3.0.2/bootloader/mcuboot/boot/zephyr/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/nrf/cmake/extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/b0_mcuboot_signing.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/modules/ncs_sysbuild_extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/partition_manager.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit_provisioning.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit_utilities.cmake /opt/nordic/ncs/v3.0.2/nrf/modules/modules.cmake /opt/nordic/ncs/v3.0.2/nrf/snippets/ci-shell/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/icbmsg/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/icmsg/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/mbox/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/mspi/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hw-flow-control/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-diagnostic-logs/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-power-consumption-tests/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nordic-bt-rpc/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf54l09-switch-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-driver-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-driver-verbose-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-fw-patch-ext-flash/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-wifi/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-ext-flash/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-ram/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-rtt/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/tfm-enable-share-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/wpa-supplicant-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/zperf/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/nrf54l/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/nrf71/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/subsys/bootloader/cmake/packaging.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.appcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.approtect /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.bt_fast_pair /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.cracen /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.dfu /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.flprcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.hpf /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.lwm2m_carrier /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.matter /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.mcuboot /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.netcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.pprcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.secureboot /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.suit /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.suit_provisioning /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.tfm /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.wifi /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.xip /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.zip /opt/nordic/ncs/v3.0.2/nrf/sysbuild/appcore.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/hpf.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/mcuboot.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/netcore.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/secureboot.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.nrf54h20 /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.nrf9280 /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.template.manifest_config /opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf /opt/nordic/ncs/v3.0.2/zephyr/VERSION /opt/nordic/ncs/v3.0.2/zephyr/boards/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/boards/deprecated.cmake /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/Kconfig.nrf7002dk /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/board.yml /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/FindDeprecated.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/boards.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/hwm_v2.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kconfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/python.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/shields.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/snippets.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/user_cache.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/version.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/west.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/yaml.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_module.cmake /opt/nordic/ncs/v3.0.2/zephyr/modules/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/modules/modules.cmake /opt/nordic/ncs/v3.0.2/zephyr/scripts/snippets.py /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild-package/cmake/SysbuildConfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild-package/cmake/SysbuildConfigVersion.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/build/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/domains.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/native_simulator_sb_extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_images.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_kconfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_root.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_snippets.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/image_configurations/ALL_image_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/image_configurations/MAIN_image_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/boards/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/bootloader/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/bootloader/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/soc/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/template/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfigVersion.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/zephyr_package_search.cmake /opt/nordic/ncs/v3.0.2/zephyr/snippets/bt-ll-sw-split/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/cdc-acm-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-flpr-xip/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-flpr/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-log-stm-dict/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-log-stm/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-ppr-xip/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-ppr/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nus-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/ram-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/rtt-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/rtt-tracing/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/serial-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/wifi-enterprise/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/wifi-ipv4/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/xen_dom0/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/soc/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/soc/adi/max32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/altr/qemu_nios2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/altr/zephyr_nios2f/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/apollo3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/apollo4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/amd/acp_6_0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/andestech/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/andestech/ae350/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/beetle/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/designstart/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/fvp_aemv8a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/fvp_aemv8r/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/mps2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/mps3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/musca/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/qemu_cortex_a53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/qemu_virt_arm64/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/aspeed/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/aspeed/ast10x0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4e/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4l/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4s/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/same70/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/samv71/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samc20/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samc21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd20/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same54/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/saml21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr34/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr35/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcm2711/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcm2712/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/valkyrie/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/viper/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/dc233c/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/sample_controller32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/xtensa_sample_controller/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/efinix/sapphire/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ene/kb1200/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c6/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32s2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32s3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/gr716a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/leon3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32a50x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32e10x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32e50x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f3x0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f403/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f4xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32l23x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32vf103/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_01/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_02/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_03/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_04/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_legacy/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1b/cyw20829/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat3/xmc4xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/alder_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/apollo_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/atom/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/elkhart_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/ace/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/cavs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_ish/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_ish/intel_ish5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_niosv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_niosv/niosv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/agilex/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/agilex5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga_std/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga_std/cyclonev/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/lakemont/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/raptor_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ite/ec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ite/ec/it8xxx2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/litex/litex_vexriscv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/lowrisc/opentitan/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/mediatek/mt8xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec15xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec172x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec174x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec175x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mech172x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/miv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/polarfire/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/native/inf_clock/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/neorv32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/common/vpr/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf52/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf54h/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf54l/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf91/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf92/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/sysbuild.cmake /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcm/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcm/npcm4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx7/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx9/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/m2l31x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/m46x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numicro/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numicro/m48x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx6sx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx7d/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8m/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8ulp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/imx93/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/imx95/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt10xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt118x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt11xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt5xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt6xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k8x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/ke1xf/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/ke1xz/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kl2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kv5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kwx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/layerscape/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/layerscape/ls1046a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc11u6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc51u68/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc54xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc55xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxa/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxn/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxw/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/rw/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32k1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32k3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32ze/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/openisa/rv32m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/malta/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv32e/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv64/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/quicklogic/eos_s3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/rp2040/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/rp2350/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/realtek/ec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/realtek/ec/rts5912/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra2a1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4e2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4w1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6e1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6e2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8d1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8t1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/rcar_gen3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/rcar_gen4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rz/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rz/rzg3s/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rzt2m/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/smartbond/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/smartbond/da1469x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renode/cortex_r8_virtual/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renode/riscv_virtual/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/rk3399/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/rk3568/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/ganymed/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/ganymed/sy1xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fe300/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fu500/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fu700/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/efm32hg/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/efm32wg/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32gg11b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32gg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32jg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32pg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32pg1b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32bg13p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32fg13p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32fg1p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32mg12p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32bg22/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32bg27/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32mg21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32mg24/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32zg23/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_sim3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_sim3/sim3u/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/arc_iot/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/emsdp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/emsk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/hsdk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/hsdk4xd/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/em/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/hs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/sem/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/vpx5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_v/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_v/rmx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/qemu_arc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32c0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f7x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32g0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32g4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h7rsx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h7x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32mp1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32u0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32u5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wb0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wbax/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wbx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wlx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/starfive/jh71xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/telink/tlsr/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/telink/tlsr/tlsr951x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/k3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/k3/am6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/lm3s6965/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc13x2_cc26x2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc13x2x7_cc26x2x7/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc32xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/msp432p4xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/wch/ch32v00x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xen/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/xc7zxxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/xc7zxxxs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynqmp/Kconfig.soc CMakeCache.txt CMakeFiles/4.0.3/CMakeSystem.cmake Kconfig/Kconfig.sysbuild.modules Kconfig/boards/Kconfig.nrf7002dk Kconfig/boards/Kconfig.sysbuild Kconfig/soc/Kconfig.soc Kconfig/soc/Kconfig.sysbuild _sysbuild/autoconf.h _sysbuild/empty.conf _sysbuild/sysbuild/images/wifi_udp_latency-prefix/tmp/wifi_udp_latency-mkdirs.cmake wifi_udp_latency/CMakeCache.tmp wifi_udp_latency/zephyr/.config wifi_udp_latency/zephyr/dts.cmake zephyr/.config zephyr/snippets_generated.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /opt/homebrew/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeNinjaFindMake.cmake /opt/homebrew/share/cmake/Modules/CMakeSystem.cmake.in /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/CheckCCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake /opt/homebrew/share/cmake/Modules/FindPython3.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake /opt/nordic/ncs/v3.0.2/bootloader/mcuboot/boot/zephyr/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/nrf/cmake/extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/b0_mcuboot_signing.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/modules/ncs_sysbuild_extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/partition_manager.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit_provisioning.cmake /opt/nordic/ncs/v3.0.2/nrf/cmake/sysbuild/suit_utilities.cmake /opt/nordic/ncs/v3.0.2/nrf/modules/modules.cmake /opt/nordic/ncs/v3.0.2/nrf/snippets/ci-shell/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/icbmsg/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/icmsg/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/gpio/mbox/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hpf/mspi/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/hw-flow-control/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-diagnostic-logs/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/matter-power-consumption-tests/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nordic-bt-rpc/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf54l09-switch-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-driver-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-driver-verbose-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-fw-patch-ext-flash/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf70-wifi/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-ext-flash/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-ram/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-rtt/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/nrf91-modem-trace-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/tfm-enable-share-uart/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/wpa-supplicant-debug/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/snippets/zperf/snippet.yml /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/nrf54l/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/soc/nordic/nrf71/Kconfig.soc /opt/nordic/ncs/v3.0.2/nrf/subsys/bootloader/cmake/packaging.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.appcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.approtect /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.bt_fast_pair /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.cracen /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.dfu /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.flprcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.hpf /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.lwm2m_carrier /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.matter /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.mcuboot /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.netcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.pprcore /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.secureboot /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.suit /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.suit_provisioning /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.tfm /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.wifi /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.xip /opt/nordic/ncs/v3.0.2/nrf/sysbuild/Kconfig.zip /opt/nordic/ncs/v3.0.2/nrf/sysbuild/appcore.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/extensions.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/hpf.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/mcuboot.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/netcore.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/secureboot.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit.cmake /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.nrf54h20 /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.nrf9280 /opt/nordic/ncs/v3.0.2/nrf/sysbuild/suit_provisioning/Kconfig.template.manifest_config /opt/nordic/ncs/v3.0.2/wifi_udp_latency/sysbuild.conf /opt/nordic/ncs/v3.0.2/zephyr/VERSION /opt/nordic/ncs/v3.0.2/zephyr/boards/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/boards/deprecated.cmake /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/Kconfig.nrf7002dk /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/board.yml /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/FindDeprecated.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/boards.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/hwm_v2.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kconfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/python.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/shields.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/snippets.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/user_cache.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/version.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/west.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/yaml.cmake /opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_module.cmake /opt/nordic/ncs/v3.0.2/zephyr/modules/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/modules/modules.cmake /opt/nordic/ncs/v3.0.2/zephyr/scripts/snippets.py /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild-package/cmake/SysbuildConfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild-package/cmake/SysbuildConfigVersion.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/build/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/domains.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/native_simulator_sb_extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_extensions.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_images.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_kconfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_root.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_snippets.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/image_configurations/ALL_image_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/image_configurations/MAIN_image_default.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/boards/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/bootloader/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/bootloader/Kconfig /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/images/soc/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/sysbuild/template/CMakeLists.txt /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfigVersion.cmake /opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/zephyr_package_search.cmake /opt/nordic/ncs/v3.0.2/zephyr/snippets/bt-ll-sw-split/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/cdc-acm-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-flpr-xip/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-flpr/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-log-stm-dict/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-log-stm/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-ppr-xip/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nordic-ppr/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/nus-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/ram-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/rtt-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/rtt-tracing/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/serial-console/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/wifi-enterprise/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/wifi-ipv4/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/snippets/xen_dom0/snippet.yml /opt/nordic/ncs/v3.0.2/zephyr/soc/Kconfig.v2 /opt/nordic/ncs/v3.0.2/zephyr/soc/adi/max32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/altr/qemu_nios2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/altr/zephyr_nios2f/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/apollo3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ambiq/apollo4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/amd/acp_6_0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/andestech/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/andestech/ae350/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/beetle/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/designstart/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/fvp_aemv8a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/fvp_aemv8r/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/mps2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/mps3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/musca/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/qemu_cortex_a53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/arm/qemu_virt_arm64/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/aspeed/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/aspeed/ast10x0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4e/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4l/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/sam4s/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/same70/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam/samv71/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samc20/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samc21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd20/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samd51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/same54/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/saml21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr34/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/atmel/sam0/samr35/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcm2711/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcm2712/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/valkyrie/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/brcm/bcmvk/viper/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/dc233c/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/sample_controller32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/cdns/xtensa_sample_controller/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/efinix/sapphire/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ene/kb1200/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32c6/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32s2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/espressif/esp32s3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/gr716a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gaisler/leon3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32a50x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32e10x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32e50x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f3x0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f403/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32f4xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32l23x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/gd/gd32/gd32vf103/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_01/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_02/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_03/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_04/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1a/psoc6_legacy/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat1b/cyw20829/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/infineon/cat3/xmc4xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/alder_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/apollo_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/atom/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/elkhart_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/ace/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_adsp/cavs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_ish/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_ish/intel_ish5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_niosv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_niosv/niosv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/agilex/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga/agilex5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga_std/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/intel_socfpga_std/cyclonev/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/lakemont/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/intel/raptor_lake/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ite/ec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ite/ec/it8xxx2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/litex/litex_vexriscv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/lowrisc/opentitan/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/mediatek/mt8xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec15xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec172x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec174x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mec175x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/mec/mech172x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/miv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/microchip/miv/polarfire/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/native/inf_clock/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/neorv32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/common/vpr/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf51/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf52/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf53/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf54h/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf54l/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf91/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/nrf92/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nordic/sysbuild.cmake /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcm/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcm/npcm4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx7/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/npcx/npcx9/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/m2l31x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numaker/m46x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numicro/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nuvoton/numicro/m48x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx6sx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx7d/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8m/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8ulp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx8x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/imx93/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imx/imx9/imx95/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/Kconfig.sysbuild /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt10xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt118x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt11xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt5xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/imxrt/imxrt6xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/k8x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/ke1xf/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/ke1xz/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kl2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kv5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/kinetis/kwx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/layerscape/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/layerscape/ls1046a/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc11u6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc51u68/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc54xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/lpc/lpc55xxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxa/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxn/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/mcx/mcxw/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/rw/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32k1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32k3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/nxp/s32/s32ze/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/openisa/rv32m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/malta/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv32e/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/qemu/virt_riscv/qemu_virt_riscv64/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/quicklogic/eos_s3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/rp2040/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/raspberrypi/rpi_pico/rp2350/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/realtek/ec/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/realtek/ec/rts5912/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra2a1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4e2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4m3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra4w1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6e1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6e2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra6m5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8d1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8m1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/ra/ra8t1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/rcar_gen3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rcar/rcar_gen4/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rz/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rz/rzg3s/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/rzt2m/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/smartbond/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renesas/smartbond/da1469x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renode/cortex_r8_virtual/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/renode/riscv_virtual/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/rk3399/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/rockchip/rk3568/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/ganymed/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sensry/ganymed/sy1xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fe300/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fu500/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/sifive/sifive_freedom/fu700/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/efm32hg/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s0/efm32wg/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32gg11b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32gg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32jg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32pg12b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efm32pg1b/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32bg13p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32fg13p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32fg1p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s1/efr32mg12p/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32bg22/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32bg27/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32mg21/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32mg24/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_s2/efr32zg23/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_sim3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/silabs/silabs_sim3/sim3u/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/arc_iot/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/emsdp/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/emsk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/hsdk/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/hsdk4xd/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/em/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/hs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/sem/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_classic/vpx5/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_v/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/nsim/arc_v/rmx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/snps/qemu_arc/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32c0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f2x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f3x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32f7x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32g0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32g4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h7rsx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32h7x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l4x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32l5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32mp1x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32u0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32u5x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wb0x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wbax/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wbx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/st/stm32/stm32wlx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/starfive/jh71xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/telink/tlsr/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/telink/tlsr/tlsr951x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/k3/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/k3/am6x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/lm3s6965/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc13x2_cc26x2/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc13x2x7_cc26x2x7/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/cc32xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/ti/simplelink/msp432p4xx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/wch/ch32v00x/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xen/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/xc7zxxx/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynq7000/xc7zxxxs/Kconfig.soc /opt/nordic/ncs/v3.0.2/zephyr/soc/xlnx/zynqmp/Kconfig.soc CMakeCache.txt CMakeFiles/4.0.3/CMakeSystem.cmake Kconfig/Kconfig.sysbuild.modules Kconfig/boards/Kconfig.nrf7002dk Kconfig/boards/Kconfig.sysbuild Kconfig/soc/Kconfig.soc Kconfig/soc/Kconfig.sysbuild _sysbuild/autoconf.h _sysbuild/empty.conf _sysbuild/sysbuild/images/wifi_udp_latency-prefix/tmp/wifi_udp_latency-mkdirs.cmake wifi_udp_latency/CMakeCache.tmp wifi_udp_latency/zephyr/.config wifi_udp_latency/zephyr/dts.cmake zephyr/.config zephyr/snippets_generated.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
