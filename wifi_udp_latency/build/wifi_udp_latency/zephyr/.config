CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345
CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS=10000
CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS=500
# CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX is not set
CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX=y
# CONFIG_WIFI_UDP_LATENCY_TEST_MODE_AP is not set
CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP=y
CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID="wifi-latency-test"
CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK="testpass123"
# CONFIG_INPUT is not set
CONFIG_WIFI=y
CONFIG_NET_L2_ETHERNET=y
# CONFIG_MIPI_DSI is not set
# CONFIG_MODEM is not set
CONFIG_UART_INTERRUPT_DRIVEN=y
CONFIG_NET_IPV6=y
CONFIG_FLASH_LOAD_SIZE=0
CONFIG_SRAM_SIZE=448
CONFIG_FLASH_LOAD_OFFSET=0
CONFIG_NUM_IRQS=69
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=32768
CONFIG_FLASH_SIZE=1024
CONFIG_FLASH_BASE_ADDRESS=0x0
CONFIG_MP_MAX_NUM_CPUS=1
CONFIG_SOC_RESET_HOOK=y
CONFIG_MAIN_STACK_SIZE=8192
CONFIG_IDLE_STACK_SIZE=320
CONFIG_ISR_STACK_SIZE=2048
CONFIG_CLOCK_CONTROL=y
CONFIG_SYS_CLOCK_TICKS_PER_SEC=32768
CONFIG_ROM_START_OFFSET=0
CONFIG_KERNEL_ENTRY="__start"
CONFIG_BUILD_OUTPUT_BIN=y
CONFIG_XIP=y
CONFIG_HAS_FLASH_LOAD_OFFSET=y
# CONFIG_SRAM_VECTOR_TABLE is not set
CONFIG_CPU_HAS_ARM_MPU=y
# CONFIG_COUNTER is not set
# CONFIG_SHARED_INTERRUPTS is not set
# CONFIG_PM_DEVICE is not set
CONFIG_TICKLESS_KERNEL=y
# CONFIG_FPU is not set
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=6144
CONFIG_CLOCK_CONTROL_INIT_PRIORITY=30
CONFIG_PHY_INIT_PRIORITY=70
# CONFIG_USE_DT_CODE_PARTITION is not set
# CONFIG_MULTI_LEVEL_INTERRUPTS is not set
CONFIG_GEN_IRQ_VECTOR_TABLE=y
# CONFIG_DYNAMIC_INTERRUPTS is not set
CONFIG_GEN_ISR_TABLES=y
CONFIG_INIT_STACKS=y
CONFIG_TIMESLICE_SIZE=0
# CONFIG_REGULATOR is not set
CONFIG_SYS_CLOCK_EXISTS=y
CONFIG_INIT_ARCH_HW_AT_BOOT=y
# CONFIG_BUILD_OUTPUT_S19 is not set
CONFIG_FLASH_FILL_BUFFER_SIZE=32
CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT=y
CONFIG_HW_STACK_PROTECTION=y
CONFIG_NET_TCP_CHECKSUM=y
CONFIG_NET_UDP_CHECKSUM=y
# CONFIG_MFD is not set
CONFIG_GPIO=y
CONFIG_SERIAL=y
# CONFIG_CPU_HAS_CUSTOM_FIXED_SOC_MPU_REGIONS is not set
CONFIG_SPI=y
# CONFIG_NET_L2_IEEE802154 is not set
# CONFIG_CODE_DATA_RELOCATION_SRAM is not set
# CONFIG_MEMC is not set
# CONFIG_CACHE is not set
CONFIG_LOG=y
CONFIG_CMSIS_CORE_HAS_SYSTEM_CORE_CLOCK=y
# CONFIG_CODE_DATA_RELOCATION is not set
# CONFIG_ROMSTART_RELOCATION_ROM is not set
CONFIG_DCACHE_LINE_SIZE=32
# CONFIG_RESET is not set
CONFIG_ARCH_SW_ISR_TABLE_ALIGN=4
CONFIG_NRF_RTC_TIMER=y
CONFIG_LOG_DOMAIN_NAME=""
CONFIG_SHELL_BACKEND_SERIAL=y
CONFIG_ASSERT=y
CONFIG_BUILD_OUTPUT_HEX=y
CONFIG_SOC_HAS_TIMING_FUNCTIONS=y
# CONFIG_UART_USE_RUNTIME_CONFIGURE is not set
# CONFIG_SYSCON is not set
CONFIG_SERIAL_INIT_PRIORITY=50
CONFIG_ENTROPY_INIT_PRIORITY=50
# CONFIG_INTC_MTK_ADSP is not set
# CONFIG_MTK_ADSP_TIMER is not set
CONFIG_CONSOLE=y
# CONFIG_WINSTREAM is not set
CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE=-1
CONFIG_SOC_TOOLCHAIN_NAME="amd_acp_6_0_adsp"
CONFIG_GEN_SW_ISR_TABLE=y
# CONFIG_REBOOT is not set
CONFIG_FLASH_INIT_PRIORITY=50
CONFIG_GEN_IRQ_START_VECTOR=0
CONFIG_SRAM_OFFSET=0
# CONFIG_POWER_DOMAIN is not set
CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN=4
CONFIG_SPI_NOR=y
# CONFIG_BOOTLOADER_MCUBOOT is not set
# CONFIG_SCHED_CPU_MASK is not set
# CONFIG_WATCHDOG is not set
CONFIG_ICACHE_LINE_SIZE=32
CONFIG_PRIVILEGED_STACK_SIZE=1024

#
# Devicetree Info
#
CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED=y
CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_ITM_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED=y
CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED=y
CONFIG_DT_HAS_ARM_CRYPTOCELL_312_ENABLED=y
CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED=y
CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED=y
CONFIG_DT_HAS_GPIO_KEYS_ENABLED=y
CONFIG_DT_HAS_GPIO_LEDS_ENABLED=y
CONFIG_DT_HAS_JEDEC_SPI_NOR_ENABLED=y
CONFIG_DT_HAS_MMIO_SRAM_ENABLED=y
CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PWM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_QSPI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SAADC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SPIM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SPU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_TWIM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_USBD_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_HFXO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_LFXO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_OSCILLATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53X_REGULATOR_HV_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53X_REGULATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF5X_REGULATOR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF7002_COEX_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF7002_QSPI_ENABLED=y
CONFIG_DT_HAS_PWM_LEDS_ENABLED=y
CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_IPC_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED=y
# end of Devicetree Info

#
# Modules
#
# CONFIG_BUILD_ONLY_NO_BLOBS is not set

#
# Available modules.
#

#
# nrf (/opt/nordic/ncs/v3.0.2/nrf)
#
CONFIG_NUM_METAIRQ_PRIORITIES=0
CONFIG_LOG_BUFFER_SIZE=1024
CONFIG_NET_IPV6_NBR_CACHE=y
CONFIG_NET_IPV6_MLD=y
CONFIG_NET_PKT_RX_COUNT=8
CONFIG_NET_PKT_TX_COUNT=8
# CONFIG_MBEDTLS_SSL_PROTO_DTLS is not set
# CONFIG_MBEDTLS_SSL_MAX_FRAGMENT_LENGTH is not set
CONFIG_MBEDTLS_ENTROPY_C=y
CONFIG_MBEDTLS_CIPHER_MODE_CBC=y
CONFIG_MBEDTLS_CIPHER_MODE_CTR=y
CONFIG_MBEDTLS_CHACHA20_C=y
CONFIG_MBEDTLS_POLY1305_C=y
CONFIG_MBEDTLS_CHACHAPOLY_C=y
CONFIG_MBEDTLS_DHM_C=y
# CONFIG_MBEDTLS_RSA_C is not set
CONFIG_MBEDTLS_SHA512_C=y
CONFIG_MBEDTLS_GCM_C=y
CONFIG_MBEDTLS_SSL_SRV_C=y
CONFIG_MBEDTLS_SSL_COOKIE_C=y
CONFIG_MBEDTLS_SSL_CLI_C=y
CONFIG_MBEDTLS_SSL_TLS_C=y
# CONFIG_NET_PKT_TXTIME is not set
# CONFIG_NET_PKT_TIMESTAMP is not set
CONFIG_MBEDTLS_PSA_KEY_SLOT_COUNT=32
CONFIG_MPSL_WORK_STACK_SIZE=1024
CONFIG_NET_RX_STACK_SIZE=4096
CONFIG_NET_TX_STACK_SIZE=4096
CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN=16384
CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN=16384
CONFIG_MBEDTLS_KEY_EXCHANGE_PSK_ENABLED=y
CONFIG_ZMS=y
CONFIG_NVS=y
CONFIG_MBEDTLS_SHA1_C=y
CONFIG_MBEDTLS=y
# CONFIG_MBEDTLS_TLS_VERSION_1_2 is not set
CONFIG_MBEDTLS_MAC_SHA256_ENABLED=y
# CONFIG_MBEDTLS_CTR_DRBG_ENABLED is not set
CONFIG_MBEDTLS_MD_C=y
CONFIG_MBEDTLS_TLS_LIBRARY=y
CONFIG_MBEDTLS_X509_LIBRARY=y
# CONFIG_MBEDTLS_ENABLE_HEAP is not set
CONFIG_MBEDTLS_ECP_C=y
CONFIG_MBEDTLS_CTR_DRBG_C=y
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_CCM_C=y
# CONFIG_MBEDTLS_PSA_CRYPTO_STORAGE_C is not set
CONFIG_MBEDTLS_LEGACY_CRYPTO_C=y

#
# Nordic nRF Connect
#
CONFIG_WARN_EXPERIMENTAL=y
CONFIG_BT_BUF_CMD_TX_COUNT=10
CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE=4096
# CONFIG_GETOPT is not set
CONFIG_NRF_SECURITY_ENABLER=y
# CONFIG_NCS_SAMPLES_DEFAULTS is not set

#
# Image build variants
#
# CONFIG_NCS_MCUBOOT_IN_BUILD is not set
# end of Image build variants

#
# Subsystems
#

#
# Bootloader
#
# CONFIG_SECURE_BOOT is not set
CONFIG_PM_PARTITION_SIZE_PROVISION=0x280
# CONFIG_B0_MIN_PARTITION_SIZE is not set
CONFIG_PM_PARTITION_SIZE_B0_IMAGE=0x8000
# CONFIG_IS_SECURE_BOOTLOADER is not set

#
# Secure Boot firmware validation
#
CONFIG_SB_VALIDATION_INFO_MAGIC=0x86518483
CONFIG_SB_VALIDATION_POINTER_MAGIC=0x6919b47e
CONFIG_SB_VALIDATION_INFO_CRYPTO_ID=1
CONFIG_SB_VALIDATION_INFO_VERSION=2
CONFIG_SB_VALIDATION_METADATA_OFFSET=0
CONFIG_SB_VALIDATION_STRUCT_HAS_HASH=y
CONFIG_SB_VALIDATION_STRUCT_HAS_PUBLIC_KEY=y
CONFIG_SB_VALIDATE_FW_SIGNATURE=y
# end of Secure Boot firmware validation

# CONFIG_SECURE_BOOT_STORAGE is not set
# CONFIG_MCUBOOT_COMPRESSED_IMAGE_SUPPORT_ENABLED is not set
# CONFIG_MCUBOOT_BOOTLOADER_SIGNATURE_TYPE_ED25519 is not set
# CONFIG_MCUBOOT_BOOTLOADER_SIGNATURE_TYPE_PURE is not set
# end of Bootloader

#
# Bluetooth Low Energy
#
CONFIG_SYSTEM_WORKQUEUE_PRIORITY=-1

#
# BLE over nRF RPC
#
CONFIG_HEAP_MEM_POOL_SIZE=0
# end of BLE over nRF RPC
# end of Bluetooth Low Energy

#
# DFU
#
# CONFIG_DFU_MULTI_IMAGE is not set
# CONFIG_DFU_TARGET is not set
# end of DFU

# CONFIG_ESB is not set
# CONFIG_EMDS is not set

#
# Peripheral CPU DFU (PCD)
#
# CONFIG_PCD is not set
# CONFIG_PCD_APP is not set
CONFIG_PCD_VERSION_PAGE_BUF_SIZE=2046
# CONFIG_PCD_NET is not set
# end of Peripheral CPU DFU (PCD)

#
# Networking
#

#
# Application protocols
#
# CONFIG_NRF_CLOUD is not set
# CONFIG_REST_CLIENT is not set
# CONFIG_DOWNLOAD_CLIENT is not set
# CONFIG_DOWNLOADER is not set
# CONFIG_AWS_IOT is not set
# CONFIG_AWS_JOBS is not set
# CONFIG_AZURE_IOT_HUB is not set

#
# Self-Registration (Zi ZHu Ce)
#
# end of Self-Registration (Zi ZHu Ce)

# CONFIG_ICAL_PARSER is not set
# CONFIG_FTP_CLIENT is not set
# CONFIG_LWM2M_CLIENT_UTILS is not set
# CONFIG_WIFI_READY_LIB is not set
# CONFIG_MQTT_HELPER is not set
# CONFIG_NRF_MCUMGR_SMP_CLIENT is not set
CONFIG_NRF_WIFI_PATCHES_EXT_FLASH_DISABLED=y
# CONFIG_NRF_WIFI_PATCHES_EXT_FLASH_XIP is not set
# CONFIG_NRF_WIFI_PATCHES_EXT_FLASH_STORE is not set
CONFIG_CUSTOM_LINKER_SCRIPT="/opt/nordic/ncs/v3.0.2/zephyr/../nrf/subsys/net/lib/nrf70_fw_ext/rpu_fw_patches.ld"
CONFIG_HOSTAP_CRYPTO_ALT_LEGACY_PSA=y
# CONFIG_HOSTAP_CRYPTO_ALT_PSA is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_WPA3=y
# CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_ENTERPRISE is not set
# end of Application protocols

# CONFIG_OPENTHREAD_RPC is not set
CONFIG_L2_WIFI_CONNECTIVITY=y
CONFIG_L2_WIFI_CONN_WQ_STACK_SIZE=6144
CONFIG_L2_WIFI_CONNECTIVITY_CONNECT_TIMEOUT_SECONDS=0
CONFIG_L2_WIFI_CONNECTIVITY_CONNECTION_PERSISTENCE=y
# CONFIG_L2_WIFI_CONNECTIVITY_AUTO_CONNECT is not set
# CONFIG_L2_WIFI_CONNECTIVITY_AUTO_DOWN is not set
# end of Networking

#
# NFC
#
# CONFIG_NFC_NDEF is not set
# CONFIG_NFC_NDEF_PARSER is not set
# CONFIG_NFC_NDEF_PAYLOAD_TYPE_COMMON is not set
# CONFIG_NFC_T2T_PARSER is not set
# CONFIG_NFC_T4T_NDEF_FILE is not set
# CONFIG_NFC_T4T_ISODEP is not set
# CONFIG_NFC_T4T_APDU is not set
# CONFIG_NFC_T4T_CC_FILE is not set
# CONFIG_NFC_T4T_HL_PROCEDURE is not set
# CONFIG_NFC_PLATFORM is not set
# CONFIG_NFC_TNEP_TAG is not set
# CONFIG_NFC_TNEP_POLLER is not set
# CONFIG_NFC_TNEP_CH is not set
# CONFIG_NFC_RPC is not set
# end of NFC

# CONFIG_APP_EVENT_MANAGER is not set
# CONFIG_NRF_PROFILER is not set
# CONFIG_FW_INFO is not set

#
# Debug
#
# CONFIG_CPU_LOAD is not set
# CONFIG_PPI_TRACE is not set
# end of Debug

#
# Multiprotocol service layer (MPSL)
#
# CONFIG_MPSL_FEM_ONLY is not set
# CONFIG_MPSL_FEM_DEVICE_CONFIG_254 is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_INF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_DBG is not set
CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_FEM_LOG_LEVEL=3
CONFIG_MPSL_CX_ANY_SUPPORT=y
CONFIG_MPSL_CX_NRF70_SUPPORT=y
# CONFIG_MPSL_CX is not set
CONFIG_MPSL_THREAD_COOP_PRIO=8
CONFIG_MPSL_TIMESLOT_SESSION_COUNT=0
# CONFIG_MPSL_ASSERT_HANDLER is not set
CONFIG_MPSL_LOW_PRIO_IRQN=26
CONFIG_MPSL_HFCLK_LATENCY=1400
CONFIG_MPSL_INIT_PRIORITY=40
# CONFIG_MPSL_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_LOG_LEVEL_INF is not set
# CONFIG_MPSL_LOG_LEVEL_DBG is not set
CONFIG_MPSL_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_LOG_LEVEL=3
# end of Multiprotocol service layer (MPSL)

#
# Partition Manager
#
CONFIG_PARTITION_MANAGER_ENABLED=y
CONFIG_FLASH_MAP_CUSTOM=y
CONFIG_SRAM_BASE_ADDRESS=0x20000000
CONFIG_NRF_TRUSTZONE_FLASH_REGION_SIZE=0x4000
CONFIG_NRF_TRUSTZONE_RAM_REGION_SIZE=0x2000

#
# Zephyr subsystem configurations
#
CONFIG_RPMSG_NRF53_SRAM_SIZE=0x10000
CONFIG_PM_PARTITION_SIZE_LITTLEFS=0x6000
CONFIG_PM_PARTITION_SIZE_SETTINGS_STORAGE=0x2000
CONFIG_PM_PARTITION_ALIGN_SETTINGS_STORAGE=0x4000
CONFIG_PM_PARTITION_SIZE_NVS_STORAGE=0x6000
# end of Zephyr subsystem configurations

#
# NCS subsystem configurations
#
CONFIG_PM_EXTERNAL_FLASH_HAS_DRIVER=y
CONFIG_PM_EXTERNAL_FLASH_BASE=0
CONFIG_PM_EXTERNAL_FLASH_PATH=""
CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS=0
# CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK is not set
CONFIG_PM_SRAM_BASE=0x20000000
CONFIG_PM_SRAM_SIZE=0x80000
# end of Partition Manager

#
# nRF RPC (Remote Procedure Call) library
#
# end of nRF RPC (Remote Procedure Call) library

#
# Full Modem Firmware Update Management(FMFU)
#
# end of Full Modem Firmware Update Management(FMFU)

#
# Additional MCUmgr configuration
#

#
# Additional MCUmgr group configuration
#

#
# Additional MCUmgr OS group management functionality
#
# end of Additional MCUmgr OS group management functionality
# end of Additional MCUmgr group configuration
# end of Additional MCUmgr configuration

# CONFIG_CAF is not set

#
# Nordic IEEE 802.15.4
#
# end of Nordic IEEE 802.15.4

# CONFIG_DM_MODULE is not set

#
# nRF Security
#
# CONFIG_NORDIC_SECURITY_BACKEND is not set
CONFIG_NRF_SECURITY=y

#
# Nordic-added meta types
#
CONFIG_PSA_HAS_AEAD_SUPPORT=y
CONFIG_PSA_HAS_ASYM_SIGN_SUPPORT=y
CONFIG_PSA_HAS_CIPHER_SUPPORT=y
CONFIG_PSA_HAS_HASH_SUPPORT=y
CONFIG_PSA_HAS_KEY_AGREEMENT=y
CONFIG_PSA_HAS_KEY_DERIVATION=y
CONFIG_PSA_HAS_KEY_SUPPORT=y
CONFIG_PSA_HAS_MAC_SUPPORT=y

#
# Nordic added alg types
#
# CONFIG_PSA_WANT_ALG_ECDSA_ANY is not set
# CONFIG_PSA_WANT_ALG_ED25519PH is not set
# CONFIG_PSA_WANT_ALG_ED448PH is not set
# CONFIG_PSA_WANT_ALG_PURE_EDDSA is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN_RAW is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS_ANY_SALT is not set
# CONFIG_PSA_WANT_ALG_SHA_512_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_256 is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_HMAC is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_CMAC is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P_MATTER is not set
# CONFIG_PSA_WANT_ALG_SP800_108_COUNTER_HMAC is not set
# CONFIG_PSA_WANT_ALG_SP800_108_COUNTER_CMAC is not set
# CONFIG_PSA_WANT_ALG_SRP_6 is not set
# CONFIG_PSA_WANT_ALG_SRP_PASSWORD_HASH is not set
# CONFIG_PSA_WANT_ALG_XTS is not set
CONFIG_PSA_WANT_ALG_ECB_NO_PADDING=y
CONFIG_PSA_WANT_ALG_CHACHA20=y
# CONFIG_PSA_WANT_ALG_SHAKE256_512 is not set
# CONFIG_PSA_WANT_ALG_AES_KW is not set
# CONFIG_PSA_WANT_ALG_AES_KWP is not set
# CONFIG_PSA_WANT_ALG_WPA3_SAE_PT is not set
# CONFIG_PSA_WANT_ALG_WPA3_SAE is not set

#
# Nordic added ECC curve types
#
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_160 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_192 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_224 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_320 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_255 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_448 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_R2_160 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_239 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R2_163 is not set
# CONFIG_PSA_WANT_ECC_FRP_V1_256 is not set

#
# Nordic addded RNG configuration
#
CONFIG_PSA_WANT_GENERATE_RANDOM=y

#
# Nordic added key types
#
# CONFIG_PSA_WANT_KEY_TYPE_PEPPER is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_GENERATE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SPAKE2P_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_IMPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_EXPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_GENERATE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_KEY_PAIR_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_SRP_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_WPA3_SAE_PT is not set
CONFIG_MBEDTLS_CFG_FILE="nrf-config.h"
CONFIG_MBEDTLS_PSA_CRYPTO_CONFIG=y
CONFIG_MBEDTLS_PSA_CRYPTO_CONFIG_FILE="nrf-psa-crypto-config.h"
CONFIG_MBEDTLS_PSA_CRYPTO_USER_CONFIG_FILE="nrf-psa-crypto-user-config.h"
CONFIG_MBEDTLS_X509_USE_C=y
# CONFIG_MBEDTLS_X509_CREATE_C is not set
CONFIG_MBEDTLS_X509_CHECK_KEY_USAGE=y
CONFIG_MBEDTLS_X509_CHECK_EXTENDED_KEY_USAGE=y
CONFIG_MBEDTLS_X509_CRL_PARSE_C=y
CONFIG_MBEDTLS_X509_CSR_PARSE_C=y
CONFIG_MBEDTLS_X509_CRT_PARSE_C=y
# CONFIG_MBEDTLS_X509_CSR_WRITE_C is not set
CONFIG_MBEDTLS_X509_REMOVE_INFO=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_2=y
CONFIG_MBEDTLS_SSL_ENCRYPT_THEN_MAC=y
CONFIG_MBEDTLS_SSL_EXTENDED_MASTER_SECRET=y
# CONFIG_MBEDTLS_TLS_VERSION_1_3 is not set
# CONFIG_MBEDTLS_DEBUG_C is not set
# CONFIG_MBEDTLS_MEMORY_DEBUG is not set
CONFIG_MBEDTLS_SSL_ALL_ALERT_MESSAGES=y
CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION=y
# CONFIG_MBEDTLS_SSL_DEBUG_ALL is not set
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=y
# CONFIG_MBEDTLS_SSL_RENEGOTIATION is not set
# CONFIG_MBEDTLS_SSL_SESSION_TICKETS is not set
# CONFIG_MBEDTLS_SSL_SERVER_NAME_INDICATION is not set
# CONFIG_MBEDTLS_SSL_CACHE_C is not set
# CONFIG_MBEDTLS_SSL_TICKET_C is not set
CONFIG_MBEDTLS_SSL_EXPORT_KEYS=y
CONFIG_MBEDTLS_SSL_CIPHERSUITES=""

#
# TLS/DTL Cipher Suites
#
CONFIG_MBEDTLS_HAS_CBC_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_GCM_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_CCM_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_CHACHAPOLY_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_CIPHER_MODE_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_ECDH_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_ECDSA_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_HAS_ECJPAKE_CIPHERSUITE_REQUIREMENTS=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED=y
# end of TLS/DTL Cipher Suites

CONFIG_MBEDTLS_PSA_CRYPTO_C=y
CONFIG_PSA_CORE_OBERON=y

#
# PSA Driver Support
#
CONFIG_MBEDTLS_PSA_CRYPTO_DRIVERS=y
CONFIG_MBEDTLS_PSA_CRYPTO_CLIENT=y
CONFIG_PSA_CRYPTO_DRIVER_OBERON=y
# CONFIG_PSA_CRYPTO_DRIVER_CC3XX is not set

#
# Choose DRBG algorithm
#
CONFIG_PSA_WANT_ALG_CTR_DRBG=y
# CONFIG_PSA_WANT_ALG_HMAC_DRBG is not set
CONFIG_PSA_USE_CTR_DRBG_DRIVER=y
# end of Choose DRBG algorithm

#
# CryptoCell PSA Driver Configuration
#
CONFIG_PSA_USE_CC3XX_CTR_DRBG_DRIVER=y
# end of CryptoCell PSA Driver Configuration

#
# CRACEN PSA Driver Configuration
#
# end of CRACEN PSA Driver Configuration

#
# AES key size configuration
#
CONFIG_PSA_WANT_AES_KEY_SIZE_128=y
CONFIG_PSA_WANT_AES_KEY_SIZE_192=y
CONFIG_PSA_WANT_AES_KEY_SIZE_256=y
# end of AES key size configuration

#
# RSA key size configuration
#
# CONFIG_PSA_WANT_RSA_KEY_SIZE_1024 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_1536 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_2048 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_3072 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_4096 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_6144 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_8192 is not set
CONFIG_PSA_MAX_RSA_KEY_BITS=0
# end of RSA key size configuration

CONFIG_PSA_ACCEL_GENERATE_RANDOM=y
CONFIG_PSA_NEED_CC3XX_CTR_DRBG_DRIVER=y
# CONFIG_CRACEN_LOG_LEVEL_OFF is not set
# CONFIG_CRACEN_LOG_LEVEL_ERR is not set
# CONFIG_CRACEN_LOG_LEVEL_WRN is not set
# CONFIG_CRACEN_LOG_LEVEL_INF is not set
# CONFIG_CRACEN_LOG_LEVEL_DBG is not set
CONFIG_CRACEN_LOG_LEVEL_DEFAULT=y
CONFIG_CRACEN_LOG_LEVEL=3
CONFIG_PSA_NEED_OBERON_CCM_AES=y
CONFIG_PSA_NEED_OBERON_GCM_AES=y
CONFIG_PSA_NEED_OBERON_CHACHA20_POLY1305=y
CONFIG_PSA_NEED_OBERON_AEAD_DRIVER=y
CONFIG_PSA_NEED_OBERON_CTR_AES=y
CONFIG_PSA_NEED_OBERON_CBC_NO_PADDING_AES=y
CONFIG_PSA_NEED_OBERON_CBC_PKCS7_AES=y
CONFIG_PSA_NEED_OBERON_ECB_NO_PADDING_AES=y
CONFIG_PSA_NEED_OBERON_STREAM_CIPHER_CHACHA20=y
CONFIG_PSA_NEED_OBERON_CIPHER_DRIVER=y
CONFIG_PSA_NEED_OBERON_ECDH_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_ECDH=y
CONFIG_PSA_NEED_OBERON_KEY_AGREEMENT_DRIVER=y
CONFIG_PSA_NEED_OBERON_ECDSA_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_ECDSA_VERIFY=y
CONFIG_PSA_NEED_OBERON_ECDSA_SIGN=y
CONFIG_PSA_NEED_OBERON_ECDSA_DETERMINISTIC=y
CONFIG_PSA_NEED_OBERON_ECDSA_RANDOMIZED=y
CONFIG_PSA_NEED_OBERON_SHA_1=y
CONFIG_PSA_NEED_OBERON_SHA_224=y
CONFIG_PSA_NEED_OBERON_SHA_256=y
CONFIG_PSA_NEED_OBERON_SHA_384=y
CONFIG_PSA_NEED_OBERON_SHA_512=y
CONFIG_PSA_NEED_OBERON_HASH_DRIVER=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP_R1_256=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT=y
CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE=y
CONFIG_PSA_NEED_OBERON_KEY_MANAGEMENT_DRIVER=y
CONFIG_PSA_NEED_OBERON_HMAC=y
CONFIG_PSA_NEED_OBERON_CMAC=y
CONFIG_PSA_NEED_OBERON_MAC_DRIVER=y
CONFIG_PSA_NEED_OBERON_HKDF=y
CONFIG_PSA_NEED_OBERON_TLS12_PRF=y
CONFIG_PSA_NEED_OBERON_TLS12_PSK_TO_MS=y
CONFIG_PSA_NEED_OBERON_PBKDF2_AES_CMAC_PRF_128=y
CONFIG_PSA_NEED_OBERON_TLS12_ECJPAKE_TO_PMS=y
CONFIG_PSA_NEED_OBERON_KEY_DERIVATION_DRIVER=y
CONFIG_PSA_NEED_OBERON_ASYMMETRIC_SIGNATURE_DRIVER=y

#
# PSA API support
#
# CONFIG_MBEDTLS_USE_PSA_CRYPTO is not set
# CONFIG_MBEDTLS_PSA_STATIC_KEY_SLOTS is not set
# end of PSA API support

CONFIG_MBEDTLS_PLATFORM_MEMORY=y
CONFIG_MBEDTLS_PLATFORM_C=y
CONFIG_MBEDTLS_MEMORY_C=y
CONFIG_MBEDTLS_MEMORY_BUFFER_ALLOC_C=y
CONFIG_MBEDTLS_THREADING_C=y
CONFIG_MBEDTLS_BASE64_C=y
CONFIG_MBEDTLS_OID_C=y
CONFIG_MBEDTLS_ENTROPY_HARDWARE_ALT=y
CONFIG_MBEDTLS_THREADING_ALT=y
CONFIG_MBEDTLS_AES_SETKEY_ENC_ALT=y
CONFIG_MBEDTLS_AES_SETKEY_DEC_ALT=y
CONFIG_MBEDTLS_AES_ENCRYPT_ALT=y
CONFIG_MBEDTLS_AES_DECRYPT_ALT=y
CONFIG_MBEDTLS_CHACHA20_ALT=y
CONFIG_MBEDTLS_POLY1305_ALT=y
CONFIG_MBEDTLS_ECDH_GEN_PUBLIC_ALT=y
CONFIG_MBEDTLS_ECDH_COMPUTE_SHARED_ALT=y
CONFIG_MBEDTLS_ECDSA_GENKEY_ALT=y
CONFIG_MBEDTLS_ECDSA_SIGN_ALT=y
CONFIG_MBEDTLS_ECDSA_VERIFY_ALT=y
CONFIG_MBEDTLS_ECJPAKE_ALT=y
CONFIG_MBEDTLS_SHA1_ALT=y
CONFIG_MBEDTLS_SHA224_ALT=y
CONFIG_MBEDTLS_SHA256_ALT=y
CONFIG_MBEDTLS_ENTROPY_FORCE_SHA256=y
CONFIG_MBEDTLS_ENTROPY_MAX_SOURCES=1
CONFIG_MBEDTLS_NO_PLATFORM_ENTROPY=y
# CONFIG_NRF_SECURITY_ADVANCED is not set
CONFIG_OBERON_ONLY_PSA_ENABLED=y
CONFIG_OBERON_ONLY_ENABLED=y

#
# Legacy mbed TLS crypto APIs
#
CONFIG_MBEDTLS_MPI_WINDOW_SIZE=6
CONFIG_MBEDTLS_MPI_MAX_SIZE=256
# CONFIG_CC3XX_BACKEND is not set
# CONFIG_OBERON_BACKEND is not set
CONFIG_MBEDTLS_HMAC_DRBG_C=y
CONFIG_MBEDTLS_AES_C=y

#
# Cipher Selection
#

#
# CBC cipher padding modes
#
CONFIG_MBEDTLS_CIPHER_PADDING_PKCS7=y
CONFIG_MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS=y
CONFIG_MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN=y
CONFIG_MBEDTLS_CIPHER_PADDING_ZEROS=y
# CONFIG_MBEDTLS_AES_FEWER_TABLES is not set
# CONFIG_MBEDTLS_AES_ROM_TABLES is not set
# end of CBC cipher padding modes

# CONFIG_MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH is not set

#
# AEAD  - Authenticated Encryption with Associated Data
#
# end of AEAD  - Authenticated Encryption with Associated Data

CONFIG_MBEDTLS_ECDH_C=y
CONFIG_MBEDTLS_ECDSA_C=y
CONFIG_MBEDTLS_ECDSA_DETERMINISTIC=y
CONFIG_MBEDTLS_ECJPAKE_C=y

#
# ECC curves
#
# CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED is not set
CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED=y
# CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED is not set
# CONFIG_MBEDTLS_ECP_DP_CURVE448_ENABLED is not set
# end of ECC curves

CONFIG_MBEDTLS_HKDF_C=y

#
# SHA - Secure Hash Algorithm
#
CONFIG_MBEDTLS_MD5_C=y
CONFIG_MBEDTLS_SHA224_C=y
CONFIG_MBEDTLS_SHA256_C=y
# CONFIG_MBEDTLS_SHA256_SMALLER is not set
CONFIG_MBEDTLS_SHA384_C=y
# CONFIG_MBEDTLS_512_SMALLER is not set
# end of SHA - Secure Hash Algorithm

# CONFIG_MBEDTLS_FORCE_LEGACY_MD is not set
# CONFIG_MBEDTLS_FORCE_LEGACY_CIPHER is not set
CONFIG_MBEDTLS_CIPHER_C=y
CONFIG_MBEDTLS_PK_C=y
CONFIG_MBEDTLS_PKCS5_C=y
CONFIG_MBEDTLS_PK_WRITE_C=y
CONFIG_MBEDTLS_PK_PARSE_C=y
CONFIG_MBEDTLS_PK_PARSE_EC_EXTENDED=y
# CONFIG_MBEDTLS_PEM_PARSE_C is not set
# CONFIG_MBEDTLS_PEM_WRITE_C is not set
# end of Legacy mbed TLS crypto APIs

#
# Zephyr legacy configurations
#
# CONFIG_MBEDTLS_DTLS is not set
CONFIG_MBEDTLS_KEY_EXCHANGE_ALL_ENABLED=y
# CONFIG_MBEDTLS_KEY_EXCHANGE_SOME_PSK_ENABLED is not set
# CONFIG_MBEDTLS_ECP_ALL_ENABLED is not set
# CONFIG_MBEDTLS_CIPHER_ALL_ENABLED is not set
# CONFIG_MBEDTLS_MAC_ALL_ENABLED is not set
# CONFIG_MBEDTLS_MAC_MD5_ENABLED is not set
# CONFIG_MBEDTLS_HMAC_DRBG_ENABLED is not set
# CONFIG_MBEDTLS_CIPHER is not set
# CONFIG_MBEDTLS_MD is not set
# CONFIG_MBEDTLS_ENTROPY_ENABLED is not set
# CONFIG_MBEDTLS_GENPRIME_ENABLED is not set
# end of Zephyr legacy configurations

CONFIG_MBEDTLS_LIBRARY_NRF_SECURITY=y
# end of nRF Security

# CONFIG_NET_CORE_MONITOR is not set

#
# Audio Modules
#
# CONFIG_AUDIO_MODULE_TEST is not set

#
# Log levels
#
# end of Log levels
# end of Audio Modules

#
# Audio Modules
#
# CONFIG_AUDIO_MODULE_TEMPLATE is not set
# end of Audio Modules

# CONFIG_UART_ASYNC_ADAPTER is not set
# CONFIG_TRUSTED_STORAGE is not set

#
# Logging over RPC
#
# CONFIG_LOG_FORWARDER_RPC is not set
# CONFIG_LOG_BACKEND_RPC is not set
# end of Logging over RPC

# CONFIG_SUIT is not set
# CONFIG_NRF_COMPRESS is not set

#
# MCUboot IDs (informative only, do not change)
#
CONFIG_MCUBOOT_APPLICATION_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_NETWORK_CORE_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_WIFI_PATCHES_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_QSPI_XIP_IMAGE_NUMBER=-1
CONFIG_MCUBOOT_MCUBOOT_IMAGE_NUMBER=-1
# end of MCUboot IDs (informative only, do not change)

# CONFIG_SETTINGS_ZMS_LEGACY is not set
CONFIG_SETTINGS_ZMS_SECTOR_SIZE_MULT=1
# end of Subsystems

# CONFIG_WFA_QT_LOG_LEVEL_OFF is not set
# CONFIG_WFA_QT_LOG_LEVEL_ERR is not set
# CONFIG_WFA_QT_LOG_LEVEL_WRN is not set
# CONFIG_WFA_QT_LOG_LEVEL_INF is not set
# CONFIG_WFA_QT_LOG_LEVEL_DBG is not set
CONFIG_WFA_QT_LOG_LEVEL_DEFAULT=y
CONFIG_WFA_QT_LOG_LEVEL=3
# CONFIG_WFA_QT_CONTROL_APP is not set
CONFIG_WFA_QT_THREAD_STACK_SIZE=5200
CONFIG_WFA_QT_REBOOT_TIMEOUT_MS=1000
CONFIG_WFA_QT_DEFAULT_INTERFACE="wlan0"
CONFIG_WPAS_READY_TIMEOUT_MS=10000

#
# Libraries
#
# CONFIG_APP_JWT is not set

#
# Binary libraries
#
# end of Binary libraries

CONFIG_NET_CONNECTION_MANAGER_MONITOR_STACK_SIZE=512
# CONFIG_AT_MONITOR is not set
# CONFIG_LTE_LINK_CONTROL is not set
CONFIG_NRF_SPU_FLASH_REGION_SIZE=0x4000
CONFIG_FPROTECT_BLOCK_SIZE=0x4000
# CONFIG_FPROTECT is not set
# CONFIG_AT_CMD_CUSTOM is not set
CONFIG_DK_LIBRARY=y
CONFIG_DK_LIBRARY_BUTTON_SCAN_INTERVAL=10
CONFIG_DK_LIBRARY_DYNAMIC_BUTTON_HANDLERS=y
# CONFIG_DK_LIBRARY_LOG_LEVEL_OFF is not set
# CONFIG_DK_LIBRARY_LOG_LEVEL_ERR is not set
# CONFIG_DK_LIBRARY_LOG_LEVEL_WRN is not set
# CONFIG_DK_LIBRARY_LOG_LEVEL_INF is not set
# CONFIG_DK_LIBRARY_LOG_LEVEL_DBG is not set
CONFIG_DK_LIBRARY_LOG_LEVEL_DEFAULT=y
CONFIG_DK_LIBRARY_LOG_LEVEL=3
# CONFIG_DK_LIBRARY_SHELL is not set
# CONFIG_AT_CMD_PARSER is not set
# CONFIG_AT_PARSER is not set
# CONFIG_RESET_ON_FATAL_ERROR is not set
# CONFIG_SMS is not set
# CONFIG_DATE_TIME is not set
# CONFIG_HW_ID_LIBRARY is not set
# CONFIG_RAM_POWER_DOWN_LIBRARY is not set
# CONFIG_WAVE_GEN_LIB is not set
CONFIG_HW_UNIQUE_KEY_SUPPORTED=y
# CONFIG_HW_UNIQUE_KEY is not set
CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE=0
# CONFIG_LOCATION is not set
# CONFIG_QOS is not set
# CONFIG_IDENTITY_KEY is not set
# CONFIG_SFLOAT is not set
# CONFIG_CONTIN_ARRAY is not set
# CONFIG_PCM_MIX is not set
# CONFIG_TONE is not set
# CONFIG_PSCM is not set
# CONFIG_DATA_FIFO is not set
# CONFIG_FEM_AL_LIB is not set
# CONFIG_SAMPLE_RATE_CONVERTER is not set
CONFIG_NCS_BOOT_BANNER=y
CONFIG_NCS_NCS_BOOT_BANNER_STRING="nRF Connect SDK"
CONFIG_NCS_ZEPHYR_BOOT_BANNER_STRING="Zephyr OS"
# end of Libraries

#
# Device Drivers
#
# CONFIG_BT_DRIVER_QUIRK_NO_AUTO_DLE is not set
CONFIG_ENTROPY_CC3XX=y
# CONFIG_FLASH_RPC is not set
CONFIG_HW_CC3XX=y
# CONFIG_SOC_FLASH_NRF_RADIO_SYNC_RPC is not set
CONFIG_SOC_FLASH_NRF_RADIO_SYNC_MPSL_TIMESLOT_SESSION_COUNT=0
# CONFIG_ETH_RTT is not set
# CONFIG_SENSOR is not set
# CONFIG_NRF_SW_LPUART is not set
CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS=1
# end of Device Drivers

#
# External libraries
#
# CONFIG_GETOPT_LIB is not set
# end of External libraries

#
# Test
#
# CONFIG_UNITY is not set

#
# Mocks
#
# CONFIG_MOCK_NRF_MODEM_AT is not set
# CONFIG_MOCK_NRF_RPC is not set
# end of Mocks
# end of Test
# end of Nordic nRF Connect

CONFIG_ZEPHYR_NRF_MODULE=y
# end of nrf (/opt/nordic/ncs/v3.0.2/nrf)

#
# hostap (/opt/nordic/ncs/v3.0.2/modules/lib/hostap)
#
CONFIG_WIFI_NM_WPA_SUPPLICANT=y
CONFIG_HEAP_MEM_POOL_ADD_SIZE_HOSTAP=41808
CONFIG_WIFI_NM_WPA_SUPPLICANT_THREAD_STACK_SIZE=5800
CONFIG_WIFI_NM_WPA_SUPPLICANT_WQ_STACK_SIZE=4400
CONFIG_WIFI_NM_WPA_SUPPLICANT_WQ_PRIO=7
CONFIG_WIFI_NM_WPA_SUPPLICANT_PRIO=0
# CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_OFF is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_ERR is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_WRN is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_INF is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_DBG is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_DEFAULT=y
CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL=3
CONFIG_WIFI_NM_WPA_SUPPLICANT_DEBUG_LEVEL=5
# CONFIG_WIFI_NM_WPA_SUPPLICANT_ADVANCED_FEATURES is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_WEP is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_ALT is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_NONE is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_EXT=y
# CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_MBEDTLS_PSA is not set
# CONFIG_EAP_TLS is not set
# CONFIG_EAP_TTLS is not set
# CONFIG_EAP_PEAP is not set
# CONFIG_EAP_MD5 is not set
# CONFIG_EAP_GTC is not set
# CONFIG_EAP_MSCHAPV2 is not set
# CONFIG_EAP_LEAP is not set
# CONFIG_EAP_PSK is not set
# CONFIG_EAP_PAX is not set
# CONFIG_EAP_SAKE is not set
# CONFIG_EAP_GPSK is not set
# CONFIG_EAP_PWD is not set
# CONFIG_EAP_EKE is not set
# CONFIG_EAP_IKEV2 is not set
# CONFIG_EAP_SIM is not set
# CONFIG_EAP_AKA is not set
# CONFIG_EAP_FAST is not set
# CONFIG_EAP_ALL is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_AP=y
# CONFIG_WIFI_NM_WPA_SUPPLICANT_WPS is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_P2P is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_EAPOL is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_CLI is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_INF_MON=y
# CONFIG_WIFI_NM_HOSTAPD_CRYPTO_ENTERPRISE is not set
# CONFIG_EAP_SERVER_TLS is not set
# CONFIG_EAP_SERVER_IDENTITY is not set
# CONFIG_EAP_SERVER_MD5 is not set
# CONFIG_EAP_SERVER_MSCHAPV2 is not set
# CONFIG_EAP_SERVER_PEAP is not set
# CONFIG_EAP_SERVER_GTC is not set
# CONFIG_EAP_SERVER_TTLS is not set
# CONFIG_EAP_SERVER_ALL is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_BSS_MAX_IDLE_TIME=300
# CONFIG_WIFI_NM_WPA_SUPPLICANT_NO_DEBUG is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_DPP is not set
CONFIG_WIFI_NM_WPA_SUPPLICANT_11AX=y
# CONFIG_WPA_CLI is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_ROAMING is not set
# CONFIG_WIFI_NM_WPA_SUPPLICANT_SKIP_DHCP_ON_ROAMING is not set
CONFIG_SME=y
CONFIG_NO_CONFIG_WRITE=y
CONFIG_NO_CONFIG_BLOBS=y
CONFIG_CTRL_IFACE=y
CONFIG_CTRL_IFACE_ZEPHYR=y
CONFIG_NO_RANDOM_POOL=y
CONFIG_AP=y
CONFIG_WIFI_NM_WPA_SUPPLICANT_NW_SEL_THROUGHPUT=y
# CONFIG_WIFI_NM_WPA_SUPPLICANT_NW_SEL_RELIABILITY is not set
# CONFIG_SAE_PWE_EARLY_EXIT is not set
CONFIG_WIFI_NM_WPA_CTRL_RESP_TIMEOUT_S=15
CONFIG_ZEPHYR_HOSTAP_MODULE=y
# end of hostap (/opt/nordic/ncs/v3.0.2/modules/lib/hostap)

#
# mcuboot (/opt/nordic/ncs/v3.0.2/bootloader/mcuboot)
#

#
# MCUboot
#
CONFIG_DT_FLASH_WRITE_BLOCK_SIZE=4
CONFIG_MCUBOOT_USB_SUPPORT=y
# CONFIG_USE_NRF53_MULTI_IMAGE_WITHOUT_UPGRADE_ONLY is not set
# CONFIG_MCUBOOT_USE_ALL_AVAILABLE_RAM is not set
# end of MCUboot

CONFIG_ZEPHYR_MCUBOOT_MODULE=y
# end of mcuboot (/opt/nordic/ncs/v3.0.2/bootloader/mcuboot)

#
# mbedtls (/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls)
#
CONFIG_ZEPHYR_MBEDTLS_MODULE=y
CONFIG_PSA_CRYPTO_CLIENT=y
# CONFIG_PSA_CRYPTO_ENABLE_ALL is not set
CONFIG_PSA_WANT_ALG_CBC_NO_PADDING=y
CONFIG_PSA_WANT_ALG_CBC_PKCS7=y
CONFIG_PSA_WANT_ALG_CCM=y
# CONFIG_PSA_WANT_ALG_CCM_STAR_NO_TAG is not set
CONFIG_PSA_WANT_ALG_CMAC=y
CONFIG_PSA_WANT_ALG_CHACHA20_POLY1305=y
CONFIG_PSA_WANT_ALG_CTR=y
CONFIG_PSA_WANT_ALG_DETERMINISTIC_ECDSA=y
CONFIG_PSA_WANT_ALG_ECDH=y
CONFIG_PSA_WANT_ALG_ECDSA=y
# CONFIG_PSA_WANT_ALG_JPAKE is not set
CONFIG_PSA_WANT_ALG_GCM=y
CONFIG_PSA_WANT_ALG_HKDF=y
# CONFIG_PSA_WANT_ALG_HKDF_EXTRACT is not set
# CONFIG_PSA_WANT_ALG_HKDF_EXPAND is not set
CONFIG_PSA_WANT_ALG_HMAC=y
# CONFIG_PSA_WANT_ALG_MD5 is not set
# CONFIG_PSA_WANT_ALG_PBKDF2_HMAC is not set
# CONFIG_PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128 is not set
# CONFIG_PSA_WANT_ALG_RSA_OAEP is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_CRYPT is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS is not set
CONFIG_PSA_WANT_ALG_SHA_1=y
CONFIG_PSA_WANT_ALG_SHA_224=y
CONFIG_PSA_WANT_ALG_SHA_256=y
CONFIG_PSA_WANT_ALG_SHA_384=y
CONFIG_PSA_WANT_ALG_SHA_512=y
# CONFIG_PSA_WANT_ALG_SHA3_224 is not set
# CONFIG_PSA_WANT_ALG_SHA3_256 is not set
# CONFIG_PSA_WANT_ALG_SHA3_384 is not set
# CONFIG_PSA_WANT_ALG_SHA3_512 is not set
CONFIG_PSA_WANT_ALG_STREAM_CIPHER=y
CONFIG_PSA_WANT_ALG_TLS12_PRF=y
CONFIG_PSA_WANT_ALG_TLS12_PSK_TO_MS=y
CONFIG_PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS=y
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_256 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_384 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_512 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_255 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_448 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_256 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_224 is not set
CONFIG_PSA_WANT_ECC_SECP_R1_256=y
# CONFIG_PSA_WANT_ECC_SECP_R1_384 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_521 is not set
# CONFIG_PSA_WANT_KEY_TYPE_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD_HASH is not set
# CONFIG_PSA_WANT_KEY_TYPE_HMAC is not set
CONFIG_PSA_WANT_KEY_TYPE_AES=y
CONFIG_PSA_WANT_KEY_TYPE_CHACHA20=y
CONFIG_PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY=y
# CONFIG_PSA_WANT_KEY_TYPE_RAW_DATA is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY is not set
CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT=y
CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT=y
CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE=y
CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE=y
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_DERIVE is not set
CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC=y
# CONFIG_MBEDTLS_LIBRARY is not set
CONFIG_DISABLE_MBEDTLS_BUILTIN=y
# CONFIG_CUSTOM_MBEDTLS_CFG_FILE is not set
# CONFIG_MBEDTLS_LOG_LEVEL_OFF is not set
# CONFIG_MBEDTLS_LOG_LEVEL_ERR is not set
# CONFIG_MBEDTLS_LOG_LEVEL_WRN is not set
# CONFIG_MBEDTLS_LOG_LEVEL_INF is not set
# CONFIG_MBEDTLS_LOG_LEVEL_DBG is not set
CONFIG_MBEDTLS_LOG_LEVEL_DEFAULT=y
CONFIG_MBEDTLS_LOG_LEVEL=3
# CONFIG_MBEDTLS_DEBUG is not set
CONFIG_MBEDTLS_INIT=y
# CONFIG_MBEDTLS_SHELL is not set
# CONFIG_MBEDTLS_ZEROIZE_ALT is not set
CONFIG_APP_LINK_WITH_MBEDTLS=y
# end of mbedtls (/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls)

#
# oberon-psa-crypto (/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto)
#
CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE=y
# end of oberon-psa-crypto (/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto)

#
# trusted-firmware-m (/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m)
#
CONFIG_TFM_BOARD="/opt/nordic/ncs/v3.0.2/zephyr/modules/trusted-firmware-m/nordic/nrf5340_cpuapp"
CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE=y
# end of trusted-firmware-m (/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m)

CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE=y

#
# cjson (/opt/nordic/ncs/v3.0.2/modules/lib/cjson)
#
# CONFIG_CJSON_LIB is not set
CONFIG_ZEPHYR_CJSON_MODULE=y
# end of cjson (/opt/nordic/ncs/v3.0.2/modules/lib/cjson)

#
# azure-sdk-for-c (/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c)
#
# CONFIG_AZURE_SDK is not set
CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE=y
# end of azure-sdk-for-c (/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c)

#
# cirrus-logic (/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic)
#
# CONFIG_HW_CODEC_CIRRUS_LOGIC is not set
CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE=y
# end of cirrus-logic (/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic)

#
# openthread (/opt/nordic/ncs/v3.0.2/modules/lib/openthread)
#
# CONFIG_OPENTHREAD is not set
CONFIG_ZEPHYR_OPENTHREAD_MODULE=y
# end of openthread (/opt/nordic/ncs/v3.0.2/modules/lib/openthread)

#
# suit-generator (/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator)
#
CONFIG_SUIT_ENVELOPE_TEMPLATE_FILENAME=""
CONFIG_SUIT_ENVELOPE_TARGET=""
CONFIG_SUIT_ENVELOPE_OUTPUT_ARTIFACT="merged.hex"
# CONFIG_SUIT_RECOVERY is not set
# CONFIG_SUIT_LOCAL_ENVELOPE_GENERATE is not set
# CONFIG_SUIT_DFU_CACHE_EXTRACT_IMAGE is not set
# CONFIG_SUIT_ENVELOPE_TARGET_ENCRYPT is not set
# CONFIG_SUIT_ENVELOPE_TARGET_SIGN is not set
CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE=y
# end of suit-generator (/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator)

#
# suit-processor (/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor)
#
# CONFIG_SUIT_PROCESSOR is not set
CONFIG_SUIT_PLATFORM_DRY_RUN_SUPPORT=y
CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE=y
# end of suit-processor (/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor)

#
# memfault-firmware-sdk (/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk)
#
# CONFIG_MEMFAULT is not set
CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE=y
# end of memfault-firmware-sdk (/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk)

#
# coremark (/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark)
#
# CONFIG_COREMARK is not set
CONFIG_ZEPHYR_COREMARK_MODULE=y
# end of coremark (/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark)

#
# canopennode (/opt/nordic/ncs/v3.0.2/modules/lib/canopennode)
#
CONFIG_ZEPHYR_CANOPENNODE_MODULE=y
# end of canopennode (/opt/nordic/ncs/v3.0.2/modules/lib/canopennode)

#
# chre (/opt/nordic/ncs/v3.0.2/modules/lib/chre)
#
CONFIG_ZEPHYR_CHRE_MODULE=y
# CONFIG_CHRE is not set
# end of chre (/opt/nordic/ncs/v3.0.2/modules/lib/chre)

#
# lz4 (/opt/nordic/ncs/v3.0.2/modules/lib/lz4)
#
CONFIG_ZEPHYR_LZ4_MODULE=y
# CONFIG_LZ4 is not set
# end of lz4 (/opt/nordic/ncs/v3.0.2/modules/lib/lz4)

#
# nanopb (/opt/nordic/ncs/v3.0.2/modules/lib/nanopb)
#
CONFIG_ZEPHYR_NANOPB_MODULE=y
# CONFIG_NANOPB is not set
# end of nanopb (/opt/nordic/ncs/v3.0.2/modules/lib/nanopb)

CONFIG_ZEPHYR_TF_M_TESTS_MODULE=y

#
# zscilib (/opt/nordic/ncs/v3.0.2/modules/lib/zscilib)
#
# CONFIG_ZSL is not set
CONFIG_ZEPHYR_ZSCILIB_MODULE=y
# end of zscilib (/opt/nordic/ncs/v3.0.2/modules/lib/zscilib)

#
# cmsis (/opt/nordic/ncs/v3.0.2/modules/hal/cmsis)
#
CONFIG_ZEPHYR_CMSIS_MODULE=y
CONFIG_HAS_CMSIS_CORE=y
CONFIG_HAS_CMSIS_CORE_M=y
# CONFIG_CMSIS_M_CHECK_DEVICE_DEFINES is not set
# end of cmsis (/opt/nordic/ncs/v3.0.2/modules/hal/cmsis)

#
# cmsis-dsp (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp)
#
CONFIG_ZEPHYR_CMSIS_DSP_MODULE=y
# CONFIG_CMSIS_DSP is not set
# end of cmsis-dsp (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp)

#
# cmsis-nn (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn)
#
CONFIG_ZEPHYR_CMSIS_NN_MODULE=y
# CONFIG_CMSIS_NN is not set
# end of cmsis-nn (/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn)

#
# fatfs (/opt/nordic/ncs/v3.0.2/modules/fs/fatfs)
#
CONFIG_ZEPHYR_FATFS_MODULE=y
# end of fatfs (/opt/nordic/ncs/v3.0.2/modules/fs/fatfs)

#
# hal_nordic (/opt/nordic/ncs/v3.0.2/modules/hal/nordic)
#
CONFIG_ZEPHYR_HAL_NORDIC_MODULE=y
CONFIG_HAS_NORDIC_DRIVERS=y

#
# Nordic drivers
#
# CONFIG_NRF_802154_SOURCE_HAL_NORDIC is not set
# CONFIG_NRF_802154_SER_HOST is not set
# end of Nordic drivers

CONFIG_HAS_NRFX=y

#
# nrfx drivers
#

#
# nrfx drivers logging
#
# CONFIG_NRFX_CLOCK_LOG is not set
# CONFIG_NRFX_DPPI_LOG is not set
# CONFIG_NRFX_GPIOTE_LOG is not set
# CONFIG_NRFX_NVMC_LOG is not set
# CONFIG_NRFX_QSPI_LOG is not set
# CONFIG_NRFX_SPIM_LOG is not set
# end of nrfx drivers logging

CONFIG_NRFX_CLOCK=y
CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED=y
# CONFIG_NRFX_COMP is not set
CONFIG_NRFX_DPPI=y
CONFIG_NRFX_DPPI0=y
# CONFIG_NRFX_EGU0 is not set
# CONFIG_NRFX_EGU1 is not set
# CONFIG_NRFX_EGU2 is not set
# CONFIG_NRFX_EGU3 is not set
# CONFIG_NRFX_EGU4 is not set
# CONFIG_NRFX_EGU5 is not set
CONFIG_NRFX_GPIOTE=y
CONFIG_NRFX_GPIOTE0=y
# CONFIG_NRFX_GPIOTE1 is not set
CONFIG_NRFX_GPPI=y
# CONFIG_NRFX_I2S0 is not set
# CONFIG_NRFX_IPC is not set
# CONFIG_NRFX_LPCOMP is not set
# CONFIG_NRFX_NFCT is not set
CONFIG_NRFX_NVMC=y
# CONFIG_NRFX_PDM0 is not set
# CONFIG_NRFX_POWER is not set
# CONFIG_NRFX_PWM0 is not set
# CONFIG_NRFX_PWM1 is not set
# CONFIG_NRFX_PWM2 is not set
# CONFIG_NRFX_PWM3 is not set
# CONFIG_NRFX_QDEC0 is not set
# CONFIG_NRFX_QDEC1 is not set
CONFIG_NRFX_QSPI=y
# CONFIG_NRFX_RTC0 is not set
# CONFIG_NRFX_RTC1 is not set
# CONFIG_NRFX_SAADC is not set
CONFIG_NRFX_SPIM=y
# CONFIG_NRFX_SPIM0 is not set
# CONFIG_NRFX_SPIM1 is not set
# CONFIG_NRFX_SPIM2 is not set
# CONFIG_NRFX_SPIM3 is not set
CONFIG_NRFX_SPIM4=y
# CONFIG_NRFX_SPIS0 is not set
# CONFIG_NRFX_SPIS1 is not set
# CONFIG_NRFX_SPIS2 is not set
# CONFIG_NRFX_SPIS3 is not set
# CONFIG_NRFX_SYSTICK is not set
# CONFIG_NRFX_TIMER0 is not set
# CONFIG_NRFX_TIMER1 is not set
# CONFIG_NRFX_TIMER2 is not set
# CONFIG_NRFX_TWIM0 is not set
# CONFIG_NRFX_TWIM1 is not set
# CONFIG_NRFX_TWIM2 is not set
# CONFIG_NRFX_TWIM3 is not set
# CONFIG_NRFX_TWIS0 is not set
# CONFIG_NRFX_TWIS1 is not set
# CONFIG_NRFX_TWIS2 is not set
# CONFIG_NRFX_TWIS3 is not set
# CONFIG_NRFX_UARTE0 is not set
# CONFIG_NRFX_UARTE1 is not set
# CONFIG_NRFX_UARTE2 is not set
# CONFIG_NRFX_UARTE3 is not set
# CONFIG_NRFX_USBREG is not set
# CONFIG_NRFX_WDT0 is not set
# CONFIG_NRFX_WDT1 is not set

#
# Peripheral Resource Sharing module
#
# CONFIG_NRFX_PRS_BOX_0 is not set
# CONFIG_NRFX_PRS_BOX_1 is not set
# CONFIG_NRFX_PRS_BOX_2 is not set
# CONFIG_NRFX_PRS_BOX_3 is not set
# CONFIG_NRFX_PRS_BOX_4 is not set
# end of Peripheral Resource Sharing module

CONFIG_NRFX_RESERVED_RESOURCES_HEADER="nrfx_config_reserved_resources_ncs.h"
# end of nrfx drivers
# end of hal_nordic (/opt/nordic/ncs/v3.0.2/modules/hal/nordic)

#
# hal_nxp (/opt/nordic/ncs/v3.0.2/modules/hal/nxp)
#
# CONFIG_MCUX_ACMP is not set
CONFIG_ZEPHYR_HAL_NXP_MODULE=y
# end of hal_nxp (/opt/nordic/ncs/v3.0.2/modules/hal/nxp)

#
# hal_st (/opt/nordic/ncs/v3.0.2/modules/hal/st)
#
CONFIG_ZEPHYR_HAL_ST_MODULE=y
# end of hal_st (/opt/nordic/ncs/v3.0.2/modules/hal/st)

CONFIG_ZEPHYR_HAL_STM32_MODULE=y

#
# hal_tdk (/opt/nordic/ncs/v3.0.2/modules/hal/tdk)
#
CONFIG_ZEPHYR_HAL_TDK_MODULE=y

#
# TDK drivers
#
# CONFIG_TDK_HAL is not set
# end of TDK drivers
# end of hal_tdk (/opt/nordic/ncs/v3.0.2/modules/hal/tdk)

CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE=y

#
# liblc3 (/opt/nordic/ncs/v3.0.2/modules/lib/liblc3)
#
CONFIG_ZEPHYR_LIBLC3_MODULE=y
# end of liblc3 (/opt/nordic/ncs/v3.0.2/modules/lib/liblc3)

CONFIG_ZEPHYR_LIBMETAL_MODULE=y

#
# littlefs (/opt/nordic/ncs/v3.0.2/modules/fs/littlefs)
#
CONFIG_ZEPHYR_LITTLEFS_MODULE=y
# end of littlefs (/opt/nordic/ncs/v3.0.2/modules/fs/littlefs)

#
# loramac-node (/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node)
#
CONFIG_ZEPHYR_LORAMAC_NODE_MODULE=y
# CONFIG_HAS_SEMTECH_RADIO_DRIVERS is not set
# end of loramac-node (/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node)

#
# lvgl (/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl)
#
CONFIG_ZEPHYR_LVGL_MODULE=y
# end of lvgl (/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl)

CONFIG_ZEPHYR_MIPI_SYS_T_MODULE=y

#
# nrf_wifi (/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi)
#
CONFIG_ZEPHYR_NRF_WIFI_MODULE=y
CONFIG_NRF70_BUSLIB=y
CONFIG_NRF70_ON_QSPI=y
# CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_OFF is not set
# CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_ERR is not set
# CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_WRN is not set
# CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_INF is not set
# CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_DBG is not set
CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_DEFAULT=y
CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL=3
CONFIG_NRF70_LOG_VERBOSE=y
# end of nrf_wifi (/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi)

CONFIG_ZEPHYR_OPEN_AMP_MODULE=y

#
# percepio (/opt/nordic/ncs/v3.0.2/modules/debug/percepio)
#

#
# Percepio Tracealyzer support can be enabled from the tracing subsystem
#
# CONFIG_PERCEPIO_DFM is not set
CONFIG_ZEPHYR_PERCEPIO_MODULE=y
# end of percepio (/opt/nordic/ncs/v3.0.2/modules/debug/percepio)

#
# picolibc (/opt/nordic/ncs/v3.0.2/modules/lib/picolibc)
#
# CONFIG_PICOLIBC_MODULE is not set
CONFIG_ZEPHYR_PICOLIBC_MODULE=y
# end of picolibc (/opt/nordic/ncs/v3.0.2/modules/lib/picolibc)

#
# segger (/opt/nordic/ncs/v3.0.2/modules/debug/segger)
#
CONFIG_ZEPHYR_SEGGER_MODULE=y
CONFIG_HAS_SEGGER_RTT=y
# CONFIG_USE_SEGGER_RTT is not set
# end of segger (/opt/nordic/ncs/v3.0.2/modules/debug/segger)

CONFIG_ZEPHYR_TINYCRYPT_MODULE=y

#
# uoscore-uedhoc (/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc)
#
CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE=y
# end of uoscore-uedhoc (/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc)

#
# zcbor (/opt/nordic/ncs/v3.0.2/modules/lib/zcbor)
#
CONFIG_ZEPHYR_ZCBOR_MODULE=y
# CONFIG_ZCBOR is not set
# end of zcbor (/opt/nordic/ncs/v3.0.2/modules/lib/zcbor)

#
# nrfxlib (/opt/nordic/ncs/v3.0.2/nrfxlib)
#

#
# Nordic nrfxlib
#

#
# nrf_modem (Modem library)
#
CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE=0x4e8
# end of nrf_modem (Modem library)

# CONFIG_NFC_T2T_NRFXLIB is not set
# CONFIG_NFC_T4T_NRFXLIB is not set

#
# Crypto libraries for nRF5x SOCs.
#
CONFIG_NRFXLIB_CRYPTO=y
CONFIG_HAS_HW_NRF_CC3XX=y
# CONFIG_NRF_OBERON is not set
# CONFIG_NRF_CC310_BL is not set
CONFIG_NRF_CC3XX_PLATFORM=y
CONFIG_CC3XX_MUTEX_LOCK=y
# CONFIG_CC3XX_ATOMIC_LOCK is not set
# CONFIG_CC3XX_HW_MUTEX_LOCK is not set
# end of Crypto libraries for nRF5x SOCs.

# CONFIG_NRF_RPC is not set
CONFIG_NRF_802154_SOURCE_NRFXLIB=y
# CONFIG_GZLL is not set
# CONFIG_NRF_DM is not set
# CONFIG_NRF_FUEL_GAUGE is not set
# end of Nordic nrfxlib

CONFIG_ZEPHYR_NRFXLIB_MODULE=y
# end of nrfxlib (/opt/nordic/ncs/v3.0.2/nrfxlib)

CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE=y

#
# connectedhomeip (/opt/nordic/ncs/v3.0.2/modules/lib/matter)
#
# CONFIG_CHIP is not set
CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE=y
# end of connectedhomeip (/opt/nordic/ncs/v3.0.2/modules/lib/matter)

# CONFIG_LIBMETAL is not set
# CONFIG_LVGL is not set
# CONFIG_HAS_MEC_HAL is not set
# CONFIG_HAS_MPFS_HAL is not set
# CONFIG_HAS_MEC5_HAL is not set
# CONFIG_OPENAMP is not set
# CONFIG_MIPI_SYST_LIB is not set
# CONFIG_HAS_TELINK_DRIVERS is not set
# CONFIG_TINYCRYPT is not set
# CONFIG_MCUBOOT_BOOTUTIL_LIB is not set

#
# Unavailable modules, please install those via the project manifest.
#

#
# hal_gigadevice module not available.
#

#
# Trusted-firmware-a module not available.
#

#
# THRIFT module not available.
#
# CONFIG_ACPI is not set
# end of Modules

CONFIG_BOARD="nrf7002dk"
CONFIG_BOARD_REVISION=""
CONFIG_BOARD_TARGET="nrf7002dk/nrf5340/cpuapp"
# CONFIG_NET_DRIVERS is not set
CONFIG_BOARD_NRF7002DK=y
CONFIG_BOARD_NRF7002DK_NRF5340_CPUAPP=y
CONFIG_BOARD_QUALIFIERS="nrf5340/cpuapp"

#
# Board Options
#
CONFIG_DOMAIN_CPUNET_BOARD="nrf7002dk/nrf5340/cpunet"
# end of Board Options

#
# Hardware Configuration
#
CONFIG_SOC="nrf5340"
CONFIG_SOC_SERIES="nrf53"
CONFIG_SOC_FAMILY="nordic_nrf"
CONFIG_SOC_FAMILY_NORDIC_NRF=y
CONFIG_SOC_SERIES_NRF53X=y
CONFIG_SOC_NRF5340_CPUAPP=y
CONFIG_SOC_NRF5340_CPUAPP_QKAA=y
# CONFIG_BUILD_OUTPUT_INFO_HEADER is not set
CONFIG_HAS_HW_NRF_CC312=y
CONFIG_HAS_HW_NRF_CLOCK=y
CONFIG_HAS_HW_NRF_CTRLAP=y
CONFIG_HAS_HW_NRF_DCNF=y
CONFIG_HAS_HW_NRF_DPPIC=y
CONFIG_HAS_HW_NRF_EGU0=y
CONFIG_HAS_HW_NRF_EGU1=y
CONFIG_HAS_HW_NRF_EGU2=y
CONFIG_HAS_HW_NRF_EGU3=y
CONFIG_HAS_HW_NRF_EGU4=y
CONFIG_HAS_HW_NRF_EGU5=y
CONFIG_HAS_HW_NRF_GPIO0=y
CONFIG_HAS_HW_NRF_GPIO1=y
CONFIG_HAS_HW_NRF_GPIOTE0=y
CONFIG_HAS_HW_NRF_KMU=y
CONFIG_HAS_HW_NRF_MUTEX=y
CONFIG_HAS_HW_NRF_NVMC_PE=y
CONFIG_HAS_HW_NRF_POWER=y
CONFIG_HAS_HW_NRF_PWM0=y
CONFIG_HAS_HW_NRF_QSPI=y
CONFIG_HAS_HW_NRF_RESET=y
CONFIG_HAS_HW_NRF_SAADC=y
CONFIG_HAS_HW_NRF_SPIM4=y
CONFIG_HAS_HW_NRF_SPU=y
CONFIG_HAS_HW_NRF_TWIM1=y
CONFIG_HAS_HW_NRF_UARTE0=y
CONFIG_HAS_HW_NRF_USBD=y
CONFIG_HAS_HW_NRF_USBREG=y
CONFIG_HAS_HW_NRF_VMC=y
CONFIG_HAS_HW_NRF_WDT0=y
CONFIG_HAS_NORDIC_RAM_CTRL=y
# CONFIG_NRF_FORCE_RAM_ON_REBOOT is not set
# CONFIG_NRF_SYS_EVENT is not set
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED=y
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND=y
CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND=y
# CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND_FOR_EXECUTION_FROM_RAM is not set
CONFIG_SOC_NRF53_RTC_PRETICK=y
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET=10
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET=11
CONFIG_NRF_SPU_RAM_REGION_SIZE=0x2000
CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340=y
CONFIG_SOC_NRF53_CPUNET_MGMT=y
# CONFIG_SOC_NRF53_CPUNET_ENABLE is not set
# CONFIG_BOARD_ENABLE_CPUNET is not set
# CONFIG_SOC_ENABLE_LFXO is not set
CONFIG_SOC_HFXO_CAP_DEFAULT=y
# CONFIG_SOC_HFXO_CAP_EXTERNAL is not set
# CONFIG_SOC_HFXO_CAP_INTERNAL is not set
CONFIG_NRF_ENABLE_CACHE=y
# CONFIG_NRF53_SYNC_RTC is not set
CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT=0
# CONFIG_SOC_NRF54H20_GPD is not set
CONFIG_NRF_SOC_SECURE_SUPPORTED=y
# CONFIG_NFCT_PINS_AS_GPIOS is not set
CONFIG_NRF_APPROTECT_USE_UICR=y
# CONFIG_NRF_APPROTECT_LOCK is not set
# CONFIG_NRF_APPROTECT_USER_HANDLING is not set
CONFIG_NRF_SECURE_APPROTECT_USE_UICR=y
# CONFIG_NRF_SECURE_APPROTECT_LOCK is not set
# CONFIG_NRF_SECURE_APPROTECT_USER_HANDLING is not set
# CONFIG_NRF_TRACE_PORT is not set
CONFIG_GPIO_INIT_PRIORITY=40
# CONFIG_SOC_LOG_LEVEL_OFF is not set
# CONFIG_SOC_LOG_LEVEL_ERR is not set
# CONFIG_SOC_LOG_LEVEL_WRN is not set
# CONFIG_SOC_LOG_LEVEL_INF is not set
# CONFIG_SOC_LOG_LEVEL_DBG is not set
CONFIG_SOC_LOG_LEVEL_DEFAULT=y
CONFIG_SOC_LOG_LEVEL=3
# end of Hardware Configuration

CONFIG_SOC_COMPATIBLE_NRF=y
CONFIG_SOC_COMPATIBLE_NRF53X=y
CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP=y
CONFIG_ARCH="arm"
# CONFIG_EXTRA_EXCEPTION_INFO is not set
CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT=y
CONFIG_CPU_CORTEX=y
CONFIG_KOBJECT_TEXT_AREA=256
CONFIG_ARM_MPU=y
CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE=32
CONFIG_MPU_ALLOW_FLASH_WRITE=y

#
# ARM Options
#
CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK=y
CONFIG_ARM_ON_EXIT_CPU_IDLE=y
CONFIG_CPU_CORTEX_M=y
# CONFIG_ARM_ZIMAGE_HEADER is not set
CONFIG_ISA_THUMB2=y
CONFIG_ASSEMBLER_ISA_THUMB2=y
CONFIG_COMPILER_ISA_THUMB2=y
CONFIG_STACK_ALIGN_DOUBLE_WORD=y
# CONFIG_RUNTIME_NMI is not set
# CONFIG_PLATFORM_SPECIFIC_INIT is not set
CONFIG_FAULT_DUMP=2
CONFIG_BUILTIN_STACK_GUARD=y
CONFIG_ARM_STACK_PROTECTION=y
CONFIG_FP16=y
CONFIG_FP16_IEEE=y
# CONFIG_FP16_ALT is not set
CONFIG_CPU_CORTEX_M33=y
CONFIG_CPU_CORTEX_M_HAS_SYSTICK=y
CONFIG_CPU_CORTEX_M_HAS_DWT=y
CONFIG_CPU_CORTEX_M_HAS_BASEPRI=y
CONFIG_CPU_CORTEX_M_HAS_VTOR=y
CONFIG_CPU_CORTEX_M_HAS_SPLIM=y
CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS=y
CONFIG_CPU_CORTEX_M_HAS_CMSE=y
CONFIG_ARMV7_M_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_SE=y
CONFIG_ARMV7_M_ARMV8_M_FP=y
CONFIG_ARMV8_M_DSP=y

#
# ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33/M55 options
#
# CONFIG_ZERO_LATENCY_IRQS is not set
# CONFIG_SW_VECTOR_RELAY is not set
# CONFIG_CORTEX_M_DWT is not set
# CONFIG_CORTEX_M_DEBUG_MONITOR_HOOK is not set
# CONFIG_TRAP_UNALIGNED_ACCESS is not set
# end of ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33/M55 options

CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE=y
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_DWT is not set
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_MPU is not set
CONFIG_ARM_TRUSTZONE_M=y
# CONFIG_MPU_STACK_GUARD is not set
# CONFIG_MPU_DISABLE_BACKGROUND_MAP is not set
# CONFIG_CUSTOM_SECTION_ALIGN is not set
CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE=32
CONFIG_CPU_HAS_NRF_IDAU=y
CONFIG_HAS_SWO=y
# end of ARM Options

CONFIG_ARM=y
CONFIG_ARCH_IS_SET=y

#
# General Architecture Options
#
# CONFIG_SEMIHOST is not set
# CONFIG_ISR_TABLE_SHELL is not set
# CONFIG_ARCH_LOG_LEVEL_OFF is not set
# CONFIG_ARCH_LOG_LEVEL_ERR is not set
# CONFIG_ARCH_LOG_LEVEL_WRN is not set
# CONFIG_ARCH_LOG_LEVEL_INF is not set
# CONFIG_ARCH_LOG_LEVEL_DBG is not set
CONFIG_ARCH_LOG_LEVEL_DEFAULT=y
CONFIG_ARCH_LOG_LEVEL=3
CONFIG_LITTLE_ENDIAN=y
# CONFIG_TRUSTED_EXECUTION_SECURE is not set
# CONFIG_TRUSTED_EXECUTION_NONSECURE is not set
# CONFIG_USERSPACE is not set
CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT=100
CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES=16
CONFIG_GEN_PRIV_STACKS=y
# CONFIG_STACK_GROWS_UP is not set
# CONFIG_FRAME_POINTER is not set

#
# Interrupt Configuration
#
CONFIG_ISR_TABLES_LOCAL_DECLARATION_SUPPORTED=y
CONFIG_ISR_TABLES_LOCAL_DECLARATION=y
CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS=y
# CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_CODE is not set
CONFIG_EXCEPTION_DEBUG=y
# CONFIG_SIMPLIFIED_EXCEPTION_CODES is not set
# end of Interrupt Configuration
# end of General Architecture Options

CONFIG_ARCH_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_TRUSTED_EXECUTION=y
CONFIG_ARCH_HAS_STACK_PROTECTION=y
CONFIG_ARCH_HAS_USERSPACE=y
CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT=y
CONFIG_ARCH_HAS_RAMFUNC_SUPPORT=y
CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION=y
CONFIG_ARCH_SUPPORTS_COREDUMP=y
CONFIG_ARCH_SUPPORTS_COREDUMP_THREADS=y
CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT=y
CONFIG_ARCH_SUPPORTS_ROM_START=y
CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO=y
CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE=y
CONFIG_ARCH_HAS_SUSPEND_TO_RAM=y
CONFIG_ARCH_HAS_THREAD_ABORT=y
CONFIG_ARCH_HAS_CODE_DATA_RELOCATION=y
CONFIG_CPU_HAS_TEE=y
CONFIG_CPU_HAS_FPU=y
CONFIG_CPU_HAS_MPU=y
CONFIG_MPU=y
# CONFIG_MPU_LOG_LEVEL_OFF is not set
# CONFIG_MPU_LOG_LEVEL_ERR is not set
# CONFIG_MPU_LOG_LEVEL_WRN is not set
# CONFIG_MPU_LOG_LEVEL_INF is not set
# CONFIG_MPU_LOG_LEVEL_DBG is not set
CONFIG_MPU_LOG_LEVEL_DEFAULT=y
CONFIG_MPU_LOG_LEVEL=3
CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS=y
CONFIG_MPU_GAP_FILLING=y
CONFIG_SRAM_REGION_PERMISSIONS=y

#
# DSP Options
#
# end of DSP Options

#
# Floating Point Options
#
# end of Floating Point Options

#
# Cache Options
#
# end of Cache Options

CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS=y
CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN=y

#
# General Kernel Options
#
# CONFIG_KERNEL_LOG_LEVEL_OFF is not set
# CONFIG_KERNEL_LOG_LEVEL_ERR is not set
# CONFIG_KERNEL_LOG_LEVEL_WRN is not set
# CONFIG_KERNEL_LOG_LEVEL_INF is not set
# CONFIG_KERNEL_LOG_LEVEL_DBG is not set
CONFIG_KERNEL_LOG_LEVEL_DEFAULT=y
CONFIG_KERNEL_LOG_LEVEL=3
CONFIG_MULTITHREADING=y
CONFIG_NUM_COOP_PRIORITIES=16
CONFIG_NUM_PREEMPT_PRIORITIES=15
CONFIG_MAIN_THREAD_PRIORITY=0
CONFIG_COOP_ENABLED=y
CONFIG_PREEMPT_ENABLED=y
CONFIG_PRIORITY_CEILING=-127
# CONFIG_SCHED_DEADLINE is not set
CONFIG_THREAD_STACK_INFO=y
# CONFIG_THREAD_CUSTOM_DATA is not set
# CONFIG_DYNAMIC_THREAD is not set
# CONFIG_SCHED_DUMB is not set
CONFIG_SCHED_SIMPLE=y
# CONFIG_SCHED_SCALABLE is not set
# CONFIG_SCHED_MULTIQ is not set
# CONFIG_WAITQ_DUMB is not set
# CONFIG_WAITQ_SCALABLE is not set
CONFIG_WAITQ_SIMPLE=y

#
# Misc Kernel related options
#
CONFIG_LIBC_ERRNO=y
CONFIG_ERRNO=y
CONFIG_CURRENT_THREAD_USE_TLS=y
# end of Misc Kernel related options

#
# Kernel Debugging and Metrics
#
CONFIG_BOOT_DELAY=0
# CONFIG_BOOT_CLEAR_SCREEN is not set
CONFIG_THREAD_MONITOR=y
CONFIG_THREAD_NAME=y
CONFIG_THREAD_MAX_NAME_LEN=32
# CONFIG_THREAD_RUNTIME_STATS is not set
# end of Kernel Debugging and Metrics

# CONFIG_OBJ_CORE is not set

#
# System Work Queue Options
#
# CONFIG_SYSTEM_WORKQUEUE_NO_YIELD is not set
# end of System Work Queue Options

#
# Barrier Operations
#
CONFIG_BARRIER_OPERATIONS_ARCH=y
# end of Barrier Operations

#
# Atomic Operations
#
CONFIG_ATOMIC_OPERATIONS_BUILTIN=y
# end of Atomic Operations

#
# Timer API Options
#
CONFIG_TIMESLICING=y
CONFIG_TIMESLICE_PRIORITY=0
# CONFIG_TIMESLICE_PER_THREAD is not set
# end of Timer API Options

#
# Other Kernel Object Options
#
CONFIG_POLL=y
CONFIG_MEM_SLAB_POINTER_VALIDATE=y
# CONFIG_MEM_SLAB_TRACE_MAX_UTILIZATION is not set
CONFIG_NUM_MBOX_ASYNC_MSGS=10
# CONFIG_EVENTS is not set
CONFIG_PIPES=y
CONFIG_KERNEL_MEM_POOL=y
# CONFIG_HEAP_MEM_POOL_IGNORE_MIN is not set
# end of Other Kernel Object Options

CONFIG_SWAP_NONATOMIC=y
CONFIG_TIMEOUT_64BIT=y
CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS=365

#
# Security Options
#
CONFIG_STACK_POINTER_RANDOM=0
# end of Security Options

#
# Memory Domains
#
CONFIG_ARCH_MEM_DOMAIN_SUPPORTS_ISOLATED_STACKS=y
CONFIG_MEM_DOMAIN_ISOLATED_STACKS=y
# end of Memory Domains

#
# SMP Options
#
# CONFIG_TICKET_SPINLOCKS is not set
# end of SMP Options

CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE=y
CONFIG_THREAD_LOCAL_STORAGE=y
CONFIG_TOOLCHAIN_SUPPORTS_STATIC_INIT_GNU=y
# CONFIG_STATIC_INIT_GNU is not set
# CONFIG_BOOTARGS is not set
# end of General Kernel Options

#
# Device Options
#
# CONFIG_DEVICE_DEPS is not set
# CONFIG_DEVICE_MUTABLE is not set
CONFIG_DEVICE_DT_METADATA=y
# end of Device Options

#
# Initialization Priorities
#
CONFIG_KERNEL_INIT_PRIORITY_OBJECTS=30
CONFIG_KERNEL_INIT_PRIORITY_LIBC=35
CONFIG_KERNEL_INIT_PRIORITY_DEFAULT=40
CONFIG_KERNEL_INIT_PRIORITY_DEVICE=50
CONFIG_APPLICATION_INIT_PRIORITY=90
# end of Initialization Priorities

#
# Virtual Memory Support
#
# end of Virtual Memory Support

#
# SoC and Board Hooks
#
# CONFIG_SOC_PREP_HOOK is not set
# CONFIG_SOC_EARLY_INIT_HOOK is not set
# CONFIG_SOC_LATE_INIT_HOOK is not set
# CONFIG_SOC_PER_CORE_INIT_HOOK is not set
# CONFIG_BOARD_EARLY_INIT_HOOK is not set
# CONFIG_BOARD_LATE_INIT_HOOK is not set
# end of SoC and Board Hooks

#
# Device Drivers
#
# CONFIG_ADC is not set
# CONFIG_AUDIO is not set
# CONFIG_AUXDISPLAY is not set
# CONFIG_BBRAM is not set
CONFIG_FLASH=y
# CONFIG_CAN is not set
# CONFIG_CHARGER is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_OFF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_ERR is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_WRN is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_INF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_DBG is not set
CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT=y
CONFIG_CLOCK_CONTROL_LOG_LEVEL=3
CONFIG_CLOCK_CONTROL_NRF=y
# CONFIG_CLOCK_CONTROL_NRF_SHELL is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_RC is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_SYNTH is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_500PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_250PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_150PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_100PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_75PPM is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_30PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_20PPM is not set
CONFIG_CLOCK_CONTROL_NRF_ACCURACY=50
# CONFIG_CLOCK_CONTROL_FIXED_RATE_CLOCK is not set
# CONFIG_CLOCK_CONTROL_RTS5912_SCCON is not set
# CONFIG_COMPARATOR is not set
CONFIG_CONSOLE_INPUT_MAX_LINE_LEN=128
CONFIG_CONSOLE_HAS_DRIVER=y
# CONFIG_CONSOLE_HANDLER is not set
CONFIG_CONSOLE_INIT_PRIORITY=60
CONFIG_UART_CONSOLE=y
# CONFIG_UART_CONSOLE_DEBUG_SERVER_HOOKS is not set
# CONFIG_UART_CONSOLE_MCUMGR is not set
# CONFIG_RAM_CONSOLE is not set
# CONFIG_IPM_CONSOLE_SENDER is not set
# CONFIG_IPM_CONSOLE_RECEIVER is not set
# CONFIG_UART_MCUMGR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_OFF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_ERR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_WRN is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_INF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_DBG is not set
CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT=y
CONFIG_UART_CONSOLE_LOG_LEVEL=3
# CONFIG_EFI_CONSOLE is not set
CONFIG_WINSTREAM_CONSOLE_STATIC=y
CONFIG_WINSTREAM_CONSOLE_STATIC_SIZE=32768
# CONFIG_COREDUMP_DEVICE is not set
# CONFIG_CRYPTO is not set
# CONFIG_DAC is not set
# CONFIG_DAI is not set
# CONFIG_DISK_DRIVERS is not set
# CONFIG_DISPLAY is not set
# CONFIG_DMA is not set
# CONFIG_DP_DRIVER is not set
# CONFIG_EDAC is not set
# CONFIG_EEPROM is not set
CONFIG_ENTROPY_GENERATOR=y
# CONFIG_ENTROPY_LOG_LEVEL_OFF is not set
# CONFIG_ENTROPY_LOG_LEVEL_ERR is not set
# CONFIG_ENTROPY_LOG_LEVEL_WRN is not set
# CONFIG_ENTROPY_LOG_LEVEL_INF is not set
# CONFIG_ENTROPY_LOG_LEVEL_DBG is not set
CONFIG_ENTROPY_LOG_LEVEL_DEFAULT=y
CONFIG_ENTROPY_LOG_LEVEL=3
CONFIG_ENTROPY_HAS_DRIVER=y
# CONFIG_ESPI is not set
CONFIG_ETH_DRIVER=y
# CONFIG_ETHERNET_LOG_LEVEL_OFF is not set
# CONFIG_ETHERNET_LOG_LEVEL_ERR is not set
# CONFIG_ETHERNET_LOG_LEVEL_WRN is not set
# CONFIG_ETHERNET_LOG_LEVEL_INF is not set
# CONFIG_ETHERNET_LOG_LEVEL_DBG is not set
CONFIG_ETHERNET_LOG_LEVEL_DEFAULT=y
CONFIG_ETHERNET_LOG_LEVEL=3
# CONFIG_ETH_IVSHMEM is not set

#
# DWC XGMAC configuration
#
# end of DWC XGMAC configuration

CONFIG_ETH_PHY_DRIVER=y
# CONFIG_PHY_LOG_LEVEL_OFF is not set
# CONFIG_PHY_LOG_LEVEL_ERR is not set
# CONFIG_PHY_LOG_LEVEL_WRN is not set
# CONFIG_PHY_LOG_LEVEL_INF is not set
# CONFIG_PHY_LOG_LEVEL_DBG is not set
CONFIG_PHY_LOG_LEVEL_DEFAULT=y
CONFIG_PHY_LOG_LEVEL=3
CONFIG_PHY_AUTONEG_TIMEOUT_MS=4000
CONFIG_PHY_MONITOR_PERIOD=500

#
# NXP ENET
#
# end of NXP ENET

CONFIG_ETH_INIT_PRIORITY=80

#
# Firmware drivers
#
# CONFIG_ARM_SCMI is not set
# end of Firmware drivers

CONFIG_FLASH_HAS_DRIVER_ENABLED=y
CONFIG_FLASH_HAS_EX_OP=y
CONFIG_FLASH_HAS_EXPLICIT_ERASE=y
CONFIG_FLASH_HAS_PAGE_LAYOUT=y
CONFIG_FLASH_JESD216=y
# CONFIG_FLASH_JESD216_API is not set
# CONFIG_FLASH_SHELL is not set
CONFIG_FLASH_PAGE_LAYOUT=y
# CONFIG_FLASH_EX_OP_ENABLED is not set

#
# MSPI flash device driver
#
# end of MSPI flash device driver

CONFIG_SPI_NOR_SFDP_MINIMAL=y
# CONFIG_SPI_NOR_SFDP_DEVICETREE is not set
# CONFIG_SPI_NOR_SFDP_RUNTIME is not set
CONFIG_SPI_NOR_INIT_PRIORITY=80
CONFIG_SPI_NOR_CS_WAIT_DELAY=0
CONFIG_SPI_NOR_SLEEP_WHILE_WAITING_UNTIL_READY=y
CONFIG_SPI_NOR_FLASH_LAYOUT_PAGE_SIZE=65536
CONFIG_SOC_FLASH_NRF=y
CONFIG_SOC_FLASH_NRF_RADIO_SYNC_NONE=y
# CONFIG_SOC_FLASH_NRF_PARTIAL_ERASE is not set
# CONFIG_SOC_FLASH_NRF_UICR is not set
# CONFIG_SOC_FLASH_NRF_EMULATE_ONE_BYTE_WRITE_ACCESS is not set
# CONFIG_FLASH_LOG_LEVEL_OFF is not set
# CONFIG_FLASH_LOG_LEVEL_ERR is not set
# CONFIG_FLASH_LOG_LEVEL_WRN is not set
# CONFIG_FLASH_LOG_LEVEL_INF is not set
# CONFIG_FLASH_LOG_LEVEL_DBG is not set
CONFIG_FLASH_LOG_LEVEL_DEFAULT=y
CONFIG_FLASH_LOG_LEVEL=3
# CONFIG_FPGA is not set
# CONFIG_FUEL_GAUGE is not set
# CONFIG_GNSS is not set
# CONFIG_GPIO_LOG_LEVEL_OFF is not set
# CONFIG_GPIO_LOG_LEVEL_ERR is not set
# CONFIG_GPIO_LOG_LEVEL_WRN is not set
# CONFIG_GPIO_LOG_LEVEL_INF is not set
# CONFIG_GPIO_LOG_LEVEL_DBG is not set
CONFIG_GPIO_LOG_LEVEL_DEFAULT=y
CONFIG_GPIO_LOG_LEVEL=3
# CONFIG_GPIO_SHELL is not set
# CONFIG_GPIO_GET_DIRECTION is not set
# CONFIG_GPIO_GET_CONFIG is not set
# CONFIG_GPIO_HOGS is not set
# CONFIG_GPIO_ENABLE_DISABLE_INTERRUPT is not set
CONFIG_GPIO_NRFX=y
CONFIG_GPIO_NRFX_INTERRUPT=y
# CONFIG_GPIO_RTS5912 is not set
# CONFIG_HAPTICS is not set
# CONFIG_HWINFO is not set
# CONFIG_HWSPINLOCK is not set
# CONFIG_I2C is not set
# CONFIG_I2S is not set
# CONFIG_I3C is not set
# CONFIG_IEEE802154 is not set

#
# Interrupt controller drivers
#
CONFIG_INTC_INIT_PRIORITY=40
# CONFIG_INTC_LOG_LEVEL_OFF is not set
# CONFIG_INTC_LOG_LEVEL_ERR is not set
# CONFIG_INTC_LOG_LEVEL_WRN is not set
# CONFIG_INTC_LOG_LEVEL_INF is not set
# CONFIG_INTC_LOG_LEVEL_DBG is not set
CONFIG_INTC_LOG_LEVEL_DEFAULT=y
CONFIG_INTC_LOG_LEVEL=3
# end of Interrupt controller drivers

# CONFIG_IPM is not set
# CONFIG_KSCAN is not set
# CONFIG_LED is not set
# CONFIG_LED_STRIP is not set
# CONFIG_LORA is not set
# CONFIG_MBOX is not set
# CONFIG_MDIO is not set
# CONFIG_MIPI_DBI is not set

#
# Miscellaneous Drivers
#
# CONFIG_TIMEAWARE_GPIO is not set
# end of Miscellaneous Drivers

# CONFIG_MM_DRV is not set
# CONFIG_MSPI is not set
# CONFIG_PCIE is not set
# CONFIG_PCIE_ENDPOINT is not set
# CONFIG_PECI is not set
CONFIG_PINCTRL=y
# CONFIG_PINCTRL_LOG_LEVEL_OFF is not set
# CONFIG_PINCTRL_LOG_LEVEL_ERR is not set
# CONFIG_PINCTRL_LOG_LEVEL_WRN is not set
# CONFIG_PINCTRL_LOG_LEVEL_INF is not set
# CONFIG_PINCTRL_LOG_LEVEL_DBG is not set
CONFIG_PINCTRL_LOG_LEVEL_DEFAULT=y
CONFIG_PINCTRL_LOG_LEVEL=3
CONFIG_PINCTRL_STORE_REG=y
# CONFIG_PINCTRL_DYNAMIC is not set
CONFIG_PINCTRL_NRF=y
# CONFIG_PINCTRL_REALTEK_RTS5912 is not set
# CONFIG_PM_CPU_OPS is not set
# CONFIG_PS2 is not set
# CONFIG_PTP_CLOCK is not set
# CONFIG_PWM is not set
# CONFIG_RETAINED_MEM is not set
# CONFIG_RTC is not set
# CONFIG_SDHC is not set

#
# Capabilities
#
CONFIG_SERIAL_HAS_DRIVER=y
CONFIG_SERIAL_SUPPORT_ASYNC=y
CONFIG_SERIAL_SUPPORT_INTERRUPT=y
# CONFIG_UART_LOG_LEVEL_OFF is not set
# CONFIG_UART_LOG_LEVEL_ERR is not set
# CONFIG_UART_LOG_LEVEL_WRN is not set
# CONFIG_UART_LOG_LEVEL_INF is not set
# CONFIG_UART_LOG_LEVEL_DBG is not set
CONFIG_UART_LOG_LEVEL_DEFAULT=y
CONFIG_UART_LOG_LEVEL=3
# CONFIG_UART_ASYNC_API is not set
# CONFIG_UART_LINE_CTRL is not set
# CONFIG_UART_DRV_CMD is not set
# CONFIG_UART_WIDE_DATA is not set
# CONFIG_UART_PIPE is not set
# CONFIG_UART_ASYNC_RX_HELPER is not set
# CONFIG_UART_SHELL is not set

#
# Serial Drivers
#
CONFIG_UART_NRFX=y
CONFIG_UART_NRFX_UARTE=y
CONFIG_UART_NRFX_UARTE_LEGACY_SHIM=y
CONFIG_UART_0_INTERRUPT_DRIVEN=y
CONFIG_UART_0_ENHANCED_POLL_OUT=y
# CONFIG_UART_0_NRF_PARITY_BIT is not set
CONFIG_UART_0_NRF_TX_BUFFER_SIZE=32
# CONFIG_UART_RTS5912 is not set
# CONFIG_SMBUS is not set
# CONFIG_SPI_SHELL is not set
# CONFIG_SPI_ASYNC is not set
# CONFIG_SPI_RTIO is not set
# CONFIG_SPI_SLAVE is not set
# CONFIG_SPI_EXTENDED_MODES is not set
CONFIG_SPI_INIT_PRIORITY=50
CONFIG_SPI_COMPLETION_TIMEOUT_TOLERANCE=200
# CONFIG_SPI_LOG_LEVEL_OFF is not set
# CONFIG_SPI_LOG_LEVEL_ERR is not set
# CONFIG_SPI_LOG_LEVEL_WRN is not set
# CONFIG_SPI_LOG_LEVEL_INF is not set
# CONFIG_SPI_LOG_LEVEL_DBG is not set
CONFIG_SPI_LOG_LEVEL_DEFAULT=y
CONFIG_SPI_LOG_LEVEL=3
CONFIG_SPI_NRFX=y
CONFIG_SPI_NRFX_SPIM=y
CONFIG_SPI_NRFX_RAM_BUFFER_SIZE=8
CONFIG_SPI_NRFX_WAKE_TIMEOUT_US=200
# CONFIG_STEPPER is not set

#
# Timer drivers
#
# CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME is not set
# CONFIG_SYSTEM_CLOCK_SLOPPY_IDLE is not set
CONFIG_SYSTEM_CLOCK_INIT_PRIORITY=0
CONFIG_TICKLESS_CAPABLE=y
CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT=y
# CONFIG_NRF_RTC_TIMER_TRIGGER_OVERFLOW is not set
# CONFIG_SYSTEM_CLOCK_NO_WAIT is not set
# CONFIG_SYSTEM_CLOCK_WAIT_FOR_AVAILABILITY is not set
CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY=y
# CONFIG_REALTEK_RTS5912_RTMR is not set
# end of Timer drivers

# CONFIG_USB_BC12 is not set
# CONFIG_UDC_DRIVER is not set
# CONFIG_UHC_DRIVER is not set
# CONFIG_UVB is not set
# CONFIG_USB_DEVICE_DRIVER is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_OFF is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_ERR is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_WRN is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_INF is not set
# CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DBG is not set
CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_USBD_COMMON_LOG_LEVEL=3
# CONFIG_NRF_USBD_COMMON is not set
# CONFIG_USBC_TCPC_DRIVER is not set
# CONFIG_USBC_LOG_LEVEL_OFF is not set
# CONFIG_USBC_LOG_LEVEL_ERR is not set
# CONFIG_USBC_LOG_LEVEL_WRN is not set
# CONFIG_USBC_LOG_LEVEL_INF is not set
# CONFIG_USBC_LOG_LEVEL_DBG is not set
CONFIG_USBC_LOG_LEVEL_DEFAULT=y
CONFIG_USBC_LOG_LEVEL=3
# CONFIG_USBC_VBUS_DRIVER is not set
# CONFIG_USBC_PPC_DRIVER is not set
# CONFIG_VIDEO is not set
# CONFIG_VIRTUALIZATION is not set
# CONFIG_W1 is not set
# CONFIG_WIFI_LOG_LEVEL_OFF is not set
# CONFIG_WIFI_LOG_LEVEL_ERR is not set
# CONFIG_WIFI_LOG_LEVEL_WRN is not set
# CONFIG_WIFI_LOG_LEVEL_INF is not set
# CONFIG_WIFI_LOG_LEVEL_DBG is not set
CONFIG_WIFI_LOG_LEVEL_DEFAULT=y
CONFIG_WIFI_LOG_LEVEL=3
CONFIG_WIFI_INIT_PRIORITY=80
# CONFIG_WIFI_OFFLOAD is not set
CONFIG_WIFI_USE_NATIVE_NETWORKING=y
# CONFIG_WIFI_SIMPLELINK is not set
CONFIG_NET_TCP_WORKQ_STACK_SIZE=1024
CONFIG_NET_MGMT_EVENT_STACK_SIZE=4600
CONFIG_WIFI_MGMT_SCAN_CHAN_MAX_MANUAL=3
CONFIG_WIFI_NRF70=y
CONFIG_WIFI_NRF7002=y
CONFIG_NRF70_QSPI_LOW_POWER=y
# CONFIG_NRF70_SCAN_ONLY is not set
CONFIG_NRF70_SYSTEM_MODE=y
# CONFIG_NRF70_RADIO_TEST is not set
# CONFIG_NRF70_OFFLOADED_RAW_TX is not set
CONFIG_NRF70_STA_MODE=y
CONFIG_NRF70_AP_MODE=y
# CONFIG_NRF70_P2P_MODE is not set
# CONFIG_NRF70_SYSTEM_WITH_RAW_MODES is not set
# CONFIG_NRF70_RAW_DATA_TX is not set
# CONFIG_NRF70_RAW_DATA_RX is not set
# CONFIG_NRF70_PROMISC_DATA_RX is not set
CONFIG_NRF70_DATA_TX=y
CONFIG_NRF_WIFI_IF_AUTO_START=y
# CONFIG_NRF_WIFI_PATCHES_BUILTIN is not set
CONFIG_NRF_WIFI_PATCHES_EXTERNAL=y
CONFIG_NRF_WIFI_LOW_POWER=y
CONFIG_NRF70_TCP_IP_CHECKSUM_OFFLOAD=y
CONFIG_NRF70_REG_DOMAIN="00"
# CONFIG_WIFI_NRF70_LOG_LEVEL_OFF is not set
# CONFIG_WIFI_NRF70_LOG_LEVEL_ERR is not set
# CONFIG_WIFI_NRF70_LOG_LEVEL_WRN is not set
# CONFIG_WIFI_NRF70_LOG_LEVEL_INF is not set
# CONFIG_WIFI_NRF70_LOG_LEVEL_DBG is not set
CONFIG_WIFI_NRF70_LOG_LEVEL_DEFAULT=y
CONFIG_WIFI_NRF70_LOG_LEVEL=3
# CONFIG_NRF70_SR_COEX is not set
CONFIG_NRF70_SR_COEX_SLEEP_CTRL_GPIO_CTRL=y
CONFIG_NRF70_SR_COEX_SWCTRL1_OUTPUT=0
CONFIG_NRF70_SR_COEX_BT_GRANT_ACTIVE_LOW=1
CONFIG_NRF70_WORKQ_STACK_SIZE=4096
CONFIG_NRF70_WORKQ_MAX_ITEMS=100
CONFIG_NRF70_MAX_TX_PENDING_QLEN=18
# CONFIG_NRF70_UTIL is not set
# CONFIG_NRF70_DEBUG_SHELL is not set
CONFIG_NRF70_PCB_LOSS_2G=0
CONFIG_NRF70_PCB_LOSS_5G_BAND1=0
CONFIG_NRF70_PCB_LOSS_5G_BAND2=0
CONFIG_NRF70_PCB_LOSS_5G_BAND3=0
CONFIG_NRF70_ANT_GAIN_2G=0
CONFIG_NRF70_ANT_GAIN_5G_BAND1=0
CONFIG_NRF70_ANT_GAIN_5G_BAND2=0
CONFIG_NRF70_ANT_GAIN_5G_BAND3=0
CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_DSSS=0
CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_DSSS=0
CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_1_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_1_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_1_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_1_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_2A_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_2A_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_2A_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_2A_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_2C_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_2C_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_2C_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_2C_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_3_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_3_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_3_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_3_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_4_LOWER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_4_LOWER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_BAND_UNII_4_UPPER_EDGE_BACKOFF_HT=0
CONFIG_NRF70_BAND_UNII_4_UPPER_EDGE_BACKOFF_HE=0
CONFIG_NRF70_RX_NUM_BUFS=16
CONFIG_NRF70_MAX_TX_AGGREGATION=4
CONFIG_NRF70_MAX_TX_TOKENS=10
CONFIG_NRF70_TX_MAX_DATA_SIZE=1600
CONFIG_NRF70_RX_MAX_DATA_SIZE=1600
# CONFIG_NRF70_TX_DONE_WQ_ENABLED is not set
# CONFIG_NRF70_RX_WQ_ENABLED is not set
CONFIG_NRF70_IRQ_WQ_PRIORITY=-15
CONFIG_NRF70_BH_WQ_PRIORITY=0
CONFIG_NRF70_IRQ_WQ_STACK_SIZE=2048
CONFIG_NRF70_BH_WQ_STACK_SIZE=2048
CONFIG_NRF70_RPU_PS_IDLE_TIMEOUT_MS=10
# CONFIG_NRF70_RPU_EXTEND_TWT_SP is not set
CONFIG_WIFI_FIXED_MAC_ADDRESS=""
CONFIG_WIFI_OTP_MAC_ADDRESS=y
# CONFIG_WIFI_FIXED_MAC_ADDRESS_ENABLED is not set
# CONFIG_WIFI_RANDOM_MAC_ADDRESS is not set
CONFIG_NRF70_RSSI_STALE_TIMEOUT_MS=1000
CONFIG_NRF_WIFI_CTRL_HEAP_SIZE=20000
CONFIG_NRF_WIFI_DATA_HEAP_SIZE=54856
CONFIG_NET_TC_TX_COUNT=1
CONFIG_SHELL_STACK_SIZE=6144
CONFIG_WIFI_MGMT_SCAN_SSID_FILT_MAX=2
CONFIG_NRF_WIFI_SCAN_MAX_BSS_CNT=0
CONFIG_NRF_WIFI_BEAMFORMING=y
CONFIG_WIFI_NRF70_SCAN_TIMEOUT_S=30

#
# nRF Wi-Fi operation band(s)
#
# CONFIG_NRF_WIFI_2G_BAND is not set
# CONFIG_NRF_WIFI_5G_BAND is not set
CONFIG_NRF_WIFI_OP_BAND=3
# end of nRF Wi-Fi operation band(s)

CONFIG_NRF_WIFI_IFACE_MTU=1500
# CONFIG_WIFI_NRF70_SKIP_LOCAL_ADMIN_MAC is not set
# CONFIG_WIFI_NRF70_SCAN_DISABLE_DFS_CHANNELS is not set
CONFIG_NET_INTERFACE_NAME_LEN=15
CONFIG_NRF_WIFI_AP_DEAD_DETECT_TIMEOUT=20
CONFIG_NET_MGMT_EVENT_QUEUE_SIZE=5
CONFIG_NRF_WIFI_FEAT_WMM=y
CONFIG_NRF_WIFI_PS_POLL_BASED_RETRIEVAL=y
# CONFIG_NRF_WIFI_QOS_NULL_BASED_RETRIEVAL is not set
CONFIG_NRF_WIFI_MGMT_BUFF_OFFLOAD=y
# CONFIG_NRF_WIFI_FEAT_KEEPALIVE is not set
# CONFIG_NRF_WIFI_PS_EXIT_EVERY_TIM is not set
CONFIG_NRF_WIFI_PS_INT_PS=y
CONFIG_NRF_WIFI_DISPLAY_SCAN_BSS_LIMIT=150
# CONFIG_NRF_WIFI_COEX_DISABLE_PRIORITY_WINDOW_FOR_SCAN is not set
# CONFIG_NRF_WIFI_ZERO_COPY_TX is not set
CONFIG_NRF_WIFI_MAX_PS_POLL_FAIL_CNT=10
CONFIG_NRF_WIFI_RX_STBC_HT=y
# CONFIG_TEE is not set
# end of Device Drivers

CONFIG_REQUIRES_FULL_LIBC=y
# CONFIG_REQUIRES_FLOAT_PRINTF is not set
CONFIG_FULL_LIBC_SUPPORTED=y
CONFIG_MINIMAL_LIBC_SUPPORTED=y
CONFIG_NEWLIB_LIBC_SUPPORTED=y
CONFIG_PICOLIBC_SUPPORTED=y
CONFIG_NATIVE_LIBC_INCOMPATIBLE=y

#
# C Library
#
CONFIG_PICOLIBC=y
# CONFIG_NEWLIB_LIBC is not set
# CONFIG_EXTERNAL_LIBC is not set
CONFIG_HAS_NEWLIB_LIBC_NANO=y
CONFIG_COMMON_LIBC_ABORT=y
# CONFIG_COMMON_LIBC_ASCTIME_R is not set
# CONFIG_COMMON_LIBC_CTIME_R is not set
CONFIG_COMMON_LIBC_MALLOC=y
CONFIG_COMMON_LIBC_CALLOC=y
CONFIG_COMMON_LIBC_REALLOCARRAY=y
CONFIG_COMMON_LIBC_REMOVE=y
# CONFIG_PICOLIBC_USE_MODULE is not set
CONFIG_PICOLIBC_USE_TOOLCHAIN=y
# CONFIG_PICOLIBC_IO_FLOAT is not set
CONFIG_PICOLIBC_IO_LONG_LONG=y
CONFIG_STDOUT_CONSOLE=y
CONFIG_NEED_LIBC_MEM_PARTITION=y
# end of C Library

#
# C++ Language Support
#
# CONFIG_CPP is not set
# end of C++ Language Support

CONFIG_CRC=y
# CONFIG_CRC_SHELL is not set

#
# Additional libraries
#

#
# Hash Function Support
#
CONFIG_SYS_HASH_FUNC32=y
# CONFIG_SYS_HASH_FUNC32_DJB2 is not set
CONFIG_SYS_HASH_FUNC32_MURMUR3=y
# CONFIG_SYS_HASH_FUNC32_CHOICE_DJB2 is not set
CONFIG_SYS_HASH_FUNC32_CHOICE_MURMUR3=y
# CONFIG_SYS_HASH_FUNC32_CHOICE_IDENTITY is not set
# end of Hash Function Support

#
# Hashmap (Hash Table) Support
#
# CONFIG_SYS_HASH_MAP is not set
# end of Hashmap (Hash Table) Support

#
# Heap and Memory Allocation
#
# CONFIG_SYS_HEAP_VALIDATE is not set
# CONFIG_SYS_HEAP_STRESS is not set
# CONFIG_SYS_HEAP_INFO is not set
CONFIG_SYS_HEAP_ALLOC_LOOPS=3
# CONFIG_SYS_HEAP_RUNTIME_STATS is not set
# CONFIG_SYS_HEAP_LISTENER is not set
# CONFIG_SYS_HEAP_SMALL_ONLY is not set
# CONFIG_SYS_HEAP_BIG_ONLY is not set
CONFIG_SYS_HEAP_AUTO=y
# CONFIG_MULTI_HEAP is not set
# CONFIG_SHARED_MULTI_HEAP is not set
# end of Heap and Memory Allocation

#
# Memory Blocks
#
# CONFIG_SYS_MEM_BLOCKS is not set
# end of Memory Blocks

CONFIG_NET_BUF=y
# CONFIG_NET_BUF_LOG is not set
# CONFIG_NET_BUF_LOG_LEVEL_OFF is not set
# CONFIG_NET_BUF_LOG_LEVEL_ERR is not set
# CONFIG_NET_BUF_LOG_LEVEL_WRN is not set
# CONFIG_NET_BUF_LOG_LEVEL_INF is not set
# CONFIG_NET_BUF_LOG_LEVEL_DBG is not set
CONFIG_NET_BUF_LOG_LEVEL_DEFAULT=y
CONFIG_NET_BUF_LOG_LEVEL=3
# CONFIG_NET_BUF_POOL_USAGE is not set
CONFIG_NET_BUF_ALIGNMENT=0

#
# OS Support Library
#
CONFIG_FDTABLE=y
CONFIG_ZVFS_OPEN_MAX=16
# CONFIG_PRINTK_SYNC is not set
CONFIG_MPSC_PBUF=y
# CONFIG_SPSC_PBUF is not set
# CONFIG_MPSC_CLEAR_ALLOCATED is not set
CONFIG_HAS_POWEROFF=y
# CONFIG_POWEROFF is not set
CONFIG_CBPRINTF_COMPLETE=y
# CONFIG_CBPRINTF_NANO is not set
CONFIG_CBPRINTF_FULL_INTEGRAL=y
# CONFIG_CBPRINTF_REDUCED_INTEGRAL is not set
# CONFIG_CBPRINTF_FP_SUPPORT is not set
# CONFIG_CBPRINTF_FP_A_SUPPORT is not set
# CONFIG_CBPRINTF_LIBC_SUBSTS is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_OFF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_ERR is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_WRN is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_INF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DBG is not set
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT=y
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL=3
# CONFIG_CBPRINTF_PACKAGE_LONGDOUBLE is not set
CONFIG_CBPRINTF_CONVERT_CHECK_PTR=y
CONFIG_ZVFS=y
CONFIG_ZVFS_EVENTFD=y
CONFIG_ZVFS_EVENTFD_MAX=1
CONFIG_ZVFS_POLL=y
CONFIG_ZVFS_POLL_MAX=6
CONFIG_ZVFS_SELECT=y
# end of OS Support Library

#
# POSIX API Support
#

#
# POSIX Options
#
CONFIG_POSIX_API=y
CONFIG_POSIX_AEP_CHOICE_NONE=y
# CONFIG_POSIX_AEP_CHOICE_BASE is not set
# CONFIG_POSIX_AEP_CHOICE_PSE51 is not set
# CONFIG_POSIX_AEP_CHOICE_PSE52 is not set
# CONFIG_POSIX_AEP_CHOICE_PSE53 is not set
CONFIG_POSIX_BASE_DEFINITIONS=y
CONFIG_POSIX_AEP_REALTIME_MINIMAL=y
CONFIG_POSIX_ASYNCHRONOUS_IO=y
CONFIG_POSIX_BARRIERS=y
CONFIG_MAX_PTHREAD_BARRIER_COUNT=5
# CONFIG_PTHREAD_CREATE_BARRIER is not set
# CONFIG_PTHREAD_BARRIER_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_BARRIER_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_BARRIER_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_BARRIER_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_BARRIER_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_BARRIER_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_BARRIER_LOG_LEVEL=3
CONFIG_POSIX_C_LANG_SUPPORT_R=y
CONFIG_POSIX_C_LIB_EXT=y
CONFIG_GETOPT_LONG=y

#
# POSIX device I/O
#
CONFIG_POSIX_DEVICE_IO=y
CONFIG_POSIX_OPEN_MAX=16
# end of POSIX device I/O

CONFIG_POSIX_FD_MGMT=y
CONFIG_POSIX_FILE_SYSTEM_R=y
CONFIG_POSIX_FILE_SYSTEM=y

#
# POSIX memory
#
CONFIG_POSIX_PAGE_SIZE=0x40
# CONFIG_POSIX_SHARED_MEMORY_OBJECTS is not set
# CONFIG_POSIX_MAPPED_FILES is not set
# CONFIG_POSIX_MEMORY_PROTECTION is not set
# end of POSIX memory

CONFIG_POSIX_MESSAGE_PASSING=y
CONFIG_POSIX_MQ_OPEN_MAX=16
CONFIG_POSIX_MQ_PRIO_MAX=32
CONFIG_MSG_SIZE_MAX=16
CONFIG_MQUEUE_NAMELEN_MAX=16
CONFIG_HEAP_MEM_POOL_ADD_SIZE_MQUEUE=1024
CONFIG_POSIX_NETWORKING=y
CONFIG_POSIX_HOST_NAME_MAX=63
# CONFIG_POSIX_IPV6 is not set
# CONFIG_POSIX_RAW_SOCKETS is not set
CONFIG_POSIX_SINGLE_PROCESS=y
CONFIG_POSIX_SYSCONF_IMPL_MACRO=y
# CONFIG_POSIX_SYSCONF_IMPL_FULL is not set
CONFIG_POSIX_UNAME_VERSION_LEN=70
CONFIG_POSIX_UNAME_NODENAME_LEN=6
# CONFIG_POSIX_ENV_LOG_LEVEL_OFF is not set
# CONFIG_POSIX_ENV_LOG_LEVEL_ERR is not set
# CONFIG_POSIX_ENV_LOG_LEVEL_WRN is not set
# CONFIG_POSIX_ENV_LOG_LEVEL_INF is not set
# CONFIG_POSIX_ENV_LOG_LEVEL_DBG is not set
CONFIG_POSIX_ENV_LOG_LEVEL_DEFAULT=y
CONFIG_POSIX_ENV_LOG_LEVEL=3
CONFIG_POSIX_MULTI_PROCESS=y
CONFIG_POSIX_MULTI_PROCESS_ALIAS_GETPID=y
CONFIG_POSIX_THREADS=y
CONFIG_POSIX_THREAD_THREADS_MAX=5
CONFIG_MAX_PTHREAD_MUTEX_COUNT=5
CONFIG_MAX_PTHREAD_COND_COUNT=5
CONFIG_POSIX_THREAD_KEYS_MAX=5
CONFIG_PTHREAD_RECYCLER_DELAY_MS=100
CONFIG_POSIX_THREAD_ATTR_STACKADDR=y
CONFIG_POSIX_THREAD_ATTR_STACKSIZE=y
# CONFIG_POSIX_THREADS_EXT is not set
CONFIG_POSIX_THREAD_PRIORITY_SCHEDULING=y
CONFIG_POSIX_PTHREAD_ATTR_STACKSIZE_BITS=23
CONFIG_POSIX_PTHREAD_ATTR_GUARDSIZE_BITS=9
CONFIG_POSIX_PTHREAD_ATTR_GUARDSIZE_DEFAULT=0
CONFIG_POSIX_THREAD_PRIO_INHERIT=y
CONFIG_POSIX_THREAD_PRIO_PROTECT=y
CONFIG_POSIX_THREAD_SAFE_FUNCTIONS=y
# CONFIG_PTHREAD_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_LOG_LEVEL=3
# CONFIG_PTHREAD_MUTEX_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_MUTEX_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_MUTEX_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_MUTEX_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_MUTEX_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_MUTEX_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_MUTEX_LOG_LEVEL=3
# CONFIG_PTHREAD_COND_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_COND_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_COND_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_COND_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_COND_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_COND_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_COND_LOG_LEVEL=3
# CONFIG_PTHREAD_KEY_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_KEY_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_KEY_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_KEY_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_KEY_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_KEY_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_KEY_LOG_LEVEL=3
CONFIG_POSIX_READER_WRITER_LOCKS=y
CONFIG_MAX_PTHREAD_RWLOCK_COUNT=5
# CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_RWLOCK_LOG_LEVEL=3

#
# POSIX scheduler options
#
# CONFIG_POSIX_PRIORITY_SCHEDULING is not set
# end of POSIX scheduler options

CONFIG_POSIX_SEMAPHORES=y
CONFIG_POSIX_SEM_VALUE_MAX=32767
CONFIG_POSIX_SEM_NSEMS_MAX=256
CONFIG_POSIX_SEM_NAMELEN_MAX=16

#
# POSIX signals
#
CONFIG_POSIX_REALTIME_SIGNALS=y
CONFIG_POSIX_RTSIG_MAX=8
CONFIG_POSIX_SIGNALS=y
CONFIG_POSIX_SIGNAL_STRING_DESC=y
# end of POSIX signals

CONFIG_POSIX_SPIN_LOCKS=y
CONFIG_MAX_PTHREAD_SPINLOCK_COUNT=5
# CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_OFF is not set
# CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_ERR is not set
# CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_WRN is not set
# CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_INF is not set
# CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_DBG is not set
CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_DEFAULT=y
CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL=3

#
# POSIX synchronized I/O
#
CONFIG_POSIX_FSYNC=y
CONFIG_POSIX_SYNCHRONIZED_IO=y
# end of POSIX synchronized I/O

CONFIG_POSIX_TIMERS=y
CONFIG_POSIX_THREAD_CPUTIME=y
CONFIG_POSIX_MONOTONIC_CLOCK=y
# CONFIG_POSIX_CPUTIME is not set
CONFIG_POSIX_CLOCK_SELECTION=y
CONFIG_POSIX_DELAYTIMER_MAX=32
CONFIG_POSIX_TIMER_MAX=32
CONFIG_POSIX_TIMEOUTS=y
CONFIG_TIMER_CREATE_WAIT=100
# CONFIG_TIMER_LOG_LEVEL_OFF is not set
# CONFIG_TIMER_LOG_LEVEL_ERR is not set
# CONFIG_TIMER_LOG_LEVEL_WRN is not set
# CONFIG_TIMER_LOG_LEVEL_INF is not set
# CONFIG_TIMER_LOG_LEVEL_DBG is not set
CONFIG_TIMER_LOG_LEVEL_DEFAULT=y
CONFIG_TIMER_LOG_LEVEL=3

#
# X/Open system interfaces
#
# CONFIG_XSI_SINGLE_PROCESS is not set
# CONFIG_XOPEN_STREAMS is not set
# CONFIG_XSI_SYSTEM_LOGGING is not set
CONFIG_XSI_THREADS_EXT=y
# end of X/Open system interfaces

#
# Miscellaneous POSIX-related options
#
CONFIG_EVENTFD=y
# end of Miscellaneous POSIX-related options

#
# Deprecated POSIX options
#
CONFIG_EVENTFD_MAX=1
# CONFIG_FNMATCH is not set
CONFIG_MAX_PTHREAD_COUNT=5
CONFIG_MAX_PTHREAD_KEY_COUNT=5
CONFIG_MAX_TIMER_COUNT=32
CONFIG_MSG_COUNT_MAX=16
# CONFIG_POSIX_CLOCK is not set
# CONFIG_POSIX_FS is not set
CONFIG_POSIX_LIMITS_RTSIG_MAX=8
CONFIG_POSIX_MAX_FDS=16
CONFIG_POSIX_MAX_OPEN_FILES=16
# CONFIG_POSIX_MQUEUE is not set
# CONFIG_POSIX_PUTMSG is not set
# CONFIG_POSIX_SIGNAL is not set
# CONFIG_POSIX_SYSCONF is not set
# CONFIG_POSIX_UNAME is not set
# CONFIG_PTHREAD is not set
# CONFIG_PTHREAD_BARRIER is not set
# CONFIG_PTHREAD_COND is not set
# CONFIG_PTHREAD_IPC is not set
# CONFIG_PTHREAD_KEY is not set
# CONFIG_PTHREAD_MUTEX is not set
# CONFIG_PTHREAD_RWLOCK is not set
# CONFIG_PTHREAD_SPINLOCK is not set
# CONFIG_TIMER is not set
CONFIG_TIMER_DELAYTIMER_MAX=32
CONFIG_SEM_NAMELEN_MAX=16
CONFIG_SEM_VALUE_MAX=32767
# end of Deprecated POSIX options

CONFIG_TC_PROVIDES_POSIX_C_LANG_SUPPORT_R=y
# end of POSIX Options

#
# POSIX Shell Utilities
#
# CONFIG_POSIX_ENV_SHELL is not set
# CONFIG_POSIX_UNAME_SHELL is not set
# end of POSIX Shell Utilities
# end of POSIX API Support

# CONFIG_OPENAMP_RSC_TABLE is not set
# CONFIG_SMF is not set
CONFIG_LIBGCC_RTLIB=y

#
# Utility Library
#
# CONFIG_JSON_LIBRARY is not set
CONFIG_RING_BUFFER=y
CONFIG_NOTIFY=y
# CONFIG_BASE64 is not set
CONFIG_ONOFF=y
# CONFIG_UTF8 is not set
# end of Utility Library
# end of Additional libraries

#
# Subsystems and OS Services
#
# CONFIG_BINDESC is not set
# CONFIG_BT is not set

#
# Controller Area Network (CAN) bus subsystem
#
# end of Controller Area Network (CAN) bus subsystem

# CONFIG_CONSOLE_SUBSYS is not set
# CONFIG_DAP is not set

#
# System Monitoring Options
#
# CONFIG_THREAD_ANALYZER is not set
# end of System Monitoring Options

#
# Debugging Options
#
# CONFIG_DEBUG is not set
# CONFIG_STACK_USAGE is not set
CONFIG_STACK_SENTINEL=y
CONFIG_PRINTK=y
CONFIG_EARLY_CONSOLE=y
CONFIG_ASSERT_LEVEL=2
CONFIG_SPIN_VALIDATE=y
# CONFIG_FORCE_NO_ASSERT is not set
CONFIG_ASSERT_VERBOSE=y
# CONFIG_ASSERT_NO_FILE_INFO is not set
# CONFIG_ASSERT_NO_COND_INFO is not set
# CONFIG_ASSERT_NO_MSG_INFO is not set
# CONFIG_ASSERT_TEST is not set
# CONFIG_OVERRIDE_FRAME_POINTER_DEFAULT is not set
# CONFIG_DEBUG_INFO is not set
# CONFIG_DEBUG_THREAD_INFO is not set
CONFIG_DEBUG_COREDUMP=y
CONFIG_DEBUG_COREDUMP_BACKEND_LOGGING=y
# CONFIG_DEBUG_COREDUMP_BACKEND_FLASH_PARTITION is not set
# CONFIG_DEBUG_COREDUMP_BACKEND_OTHER is not set
CONFIG_DEBUG_COREDUMP_MEMORY_DUMP_MIN=y
# CONFIG_DEBUG_COREDUMP_MEMORY_DUMP_THREADS is not set
# CONFIG_DEBUG_COREDUMP_MEMORY_DUMP_LINKER_RAM is not set
# CONFIG_DEBUG_COREDUMP_SHELL is not set
# CONFIG_DEBUG_COREDUMP_THREADS_METADATA is not set
# CONFIG_SYMTAB is not set
# end of Debugging Options

# CONFIG_MIPI_STP_DECODER is not set
# CONFIG_CS_TRACE_DEFMT is not set
# CONFIG_DISK_ACCESS is not set
# CONFIG_DSP is not set
# CONFIG_EMUL is not set
# CONFIG_CHARACTER_FRAMEBUFFER is not set

#
# File Systems
#
CONFIG_FILE_SYSTEM_LIB_LINK=y
CONFIG_FILE_SYSTEM=y
# CONFIG_FS_LOG_LEVEL_OFF is not set
# CONFIG_FS_LOG_LEVEL_ERR is not set
# CONFIG_FS_LOG_LEVEL_WRN is not set
# CONFIG_FS_LOG_LEVEL_INF is not set
# CONFIG_FS_LOG_LEVEL_DBG is not set
CONFIG_FS_LOG_LEVEL_DEFAULT=y
CONFIG_FS_LOG_LEVEL=3
CONFIG_APP_LINK_WITH_FS=y
CONFIG_FILE_SYSTEM_MAX_TYPES=2
CONFIG_FILE_SYSTEM_MAX_FILE_NAME=-1
CONFIG_FILE_SYSTEM_INIT_PRIORITY=99
# CONFIG_FILE_SYSTEM_SHELL is not set
# CONFIG_FILE_SYSTEM_MKFS is not set
# CONFIG_FAT_FILESYSTEM_ELM is not set
CONFIG_FILE_SYSTEM_LITTLEFS=y

#
# LittleFS Settings
#
CONFIG_FS_LITTLEFS_NUM_FILES=4
CONFIG_FS_LITTLEFS_NUM_DIRS=4
CONFIG_FS_LITTLEFS_READ_SIZE=16
CONFIG_FS_LITTLEFS_PROG_SIZE=16
CONFIG_FS_LITTLEFS_CACHE_SIZE=64
CONFIG_FS_LITTLEFS_LOOKAHEAD_SIZE=32
CONFIG_FS_LITTLEFS_BLOCK_CYCLES=512
# end of LittleFS Settings

CONFIG_FS_LITTLEFS_FC_HEAP_SIZE=0
CONFIG_FS_LITTLEFS_HEAP_PER_ALLOC_OVERHEAD_SIZE=32
CONFIG_FS_LITTLEFS_FMP_DEV=y
# CONFIG_FS_LITTLEFS_BLK_DEV is not set
# CONFIG_FS_LITTLEFS_DISK_VERSION is not set
# CONFIG_FILE_SYSTEM_EXT2 is not set
# CONFIG_EXT2_LOG_LEVEL_OFF is not set
# CONFIG_EXT2_LOG_LEVEL_ERR is not set
# CONFIG_EXT2_LOG_LEVEL_WRN is not set
# CONFIG_EXT2_LOG_LEVEL_INF is not set
# CONFIG_EXT2_LOG_LEVEL_DBG is not set
CONFIG_EXT2_LOG_LEVEL_DEFAULT=y
CONFIG_EXT2_LOG_LEVEL=3
# CONFIG_FCB is not set
# CONFIG_NVS_LOOKUP_CACHE is not set
# CONFIG_NVS_DATA_CRC is not set
# CONFIG_NVS_INIT_BAD_MEMORY_REGION is not set
# CONFIG_NVS_LOG_LEVEL_OFF is not set
# CONFIG_NVS_LOG_LEVEL_ERR is not set
# CONFIG_NVS_LOG_LEVEL_WRN is not set
# CONFIG_NVS_LOG_LEVEL_INF is not set
# CONFIG_NVS_LOG_LEVEL_DBG is not set
CONFIG_NVS_LOG_LEVEL_DEFAULT=y
CONFIG_NVS_LOG_LEVEL=3
# CONFIG_ZMS_LOOKUP_CACHE is not set
# CONFIG_ZMS_DATA_CRC is not set
# CONFIG_ZMS_CUSTOMIZE_BLOCK_SIZE is not set
# CONFIG_ZMS_NO_DOUBLE_WRITE is not set
# CONFIG_ZMS_LOG_LEVEL_OFF is not set
# CONFIG_ZMS_LOG_LEVEL_ERR is not set
# CONFIG_ZMS_LOG_LEVEL_WRN is not set
# CONFIG_ZMS_LOG_LEVEL_INF is not set
# CONFIG_ZMS_LOG_LEVEL_DBG is not set
CONFIG_ZMS_LOG_LEVEL_DEFAULT=y
CONFIG_ZMS_LOG_LEVEL=3
# end of File Systems

#
# Inter Processor Communication
#
# CONFIG_RPMSG_SERVICE is not set
# CONFIG_IPC_SERVICE is not set
# end of Inter Processor Communication

# CONFIG_JWT is not set
# CONFIG_LLEXT is not set

#
# Linkable loadable Extension Development Kit (EDK)
#
CONFIG_LLEXT_EDK_NAME="llext-edk"
# CONFIG_LLEXT_EDK_USERSPACE_ONLY is not set
# end of Linkable loadable Extension Development Kit (EDK)

#
# Logging
#
CONFIG_LOG_CORE_INIT_PRIORITY=0
CONFIG_LOG_MODE_DEFERRED=y
# CONFIG_LOG_MODE_IMMEDIATE is not set
# CONFIG_LOG_MODE_MINIMAL is not set
# CONFIG_LOG_FRONTEND is not set
# CONFIG_LOG_FRONTEND_OPT_API is not set
# CONFIG_LOG_CUSTOM_HEADER is not set
# CONFIG_LOG_MULTIDOMAIN is not set

#
# Logging levels filtering
#
CONFIG_LOG_RUNTIME_FILTERING=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_OVERRIDE_LEVEL=0
CONFIG_LOG_MAX_LEVEL=4
# end of Logging levels filtering

#
# Processing
#
CONFIG_LOG_PRINTK=y
CONFIG_LOG_MODE_OVERFLOW=y
# CONFIG_LOG_BLOCK_IN_THREAD is not set
CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD=10
CONFIG_LOG_PROCESS_THREAD=y
CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS=0
CONFIG_LOG_PROCESS_THREAD_SLEEP_MS=1000
CONFIG_LOG_PROCESS_THREAD_STACK_SIZE=2048
# CONFIG_LOG_PROCESS_THREAD_CUSTOM_PRIORITY is not set
CONFIG_LOG_TRACE_SHORT_TIMESTAMP=y
# CONFIG_LOG_TIMESTAMP_64BIT is not set
# CONFIG_LOG_TIMESTAMP_USE_REALTIME is not set
# CONFIG_LOG_SPEED is not set
# end of Processing

#
# Output Formatting
#

#
# Prepend non-hexdump log message with function name
#
# CONFIG_LOG_FUNC_NAME_PREFIX_ERR is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_WRN is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_INF is not set
CONFIG_LOG_FUNC_NAME_PREFIX_DBG=y
# end of Prepend non-hexdump log message with function name

# CONFIG_LOG_MIPI_SYST_ENABLE is not set
# CONFIG_LOG_THREAD_ID_PREFIX is not set
# CONFIG_LOG_CUSTOM_FORMAT_SUPPORT is not set
CONFIG_LOG_BACKEND_SHOW_COLOR=y
# CONFIG_LOG_INFO_COLOR_GREEN is not set
# CONFIG_LOG_DBG_COLOR_BLUE is not set
CONFIG_LOG_TAG_MAX_LEN=0
CONFIG_LOG_OUTPUT_FORMAT_TIME_TIMESTAMP=y
# CONFIG_LOG_OUTPUT_FORMAT_DATE_TIMESTAMP is not set
# CONFIG_LOG_OUTPUT_FORMAT_ISO8601_TIMESTAMP is not set
# CONFIG_LOG_OUTPUT_FORMAT_LINUX_TIMESTAMP is not set
# CONFIG_LOG_OUTPUT_FORMAT_CUSTOM_TIMESTAMP is not set
# end of Output Formatting

#
# Backends
#
# CONFIG_LOG_BACKEND_FS is not set
# CONFIG_LOG_BACKEND_NET is not set
# CONFIG_LOG_BACKEND_SWO is not set
# CONFIG_LOG_BACKEND_UART is not set
# CONFIG_LOG_BACKEND_IPC_SERVICE is not set
# end of Backends

#
# Misc
#
# CONFIG_LOG_CMDS is not set
CONFIG_LOG_USE_VLA=y
CONFIG_LOG_SIMPLE_MSG_OPTIMIZE=y
# CONFIG_LOG_ALWAYS_RUNTIME is not set
# CONFIG_LOG_FMT_SECTION is not set
# CONFIG_LOG_FMT_STRING_VALIDATE is not set
# CONFIG_LOG_MEM_UTILIZATION is not set
CONFIG_LOG_FAILURE_REPORT_PERIOD=1000
# end of Misc

CONFIG_LOG_OUTPUT=y
# end of Logging

CONFIG_MEM_ATTR=y
# CONFIG_MEM_ATTR_HEAP is not set

#
# Device Management
#

#
# Host command handler subsystem
#
# CONFIG_EC_HOST_CMD is not set
# end of Host command handler subsystem

# CONFIG_OSDP is not set
# end of Device Management

# CONFIG_MODBUS is not set
# CONFIG_MODEM_MODULES is not set

#
# Networking
#
CONFIG_NETWORKING=y
CONFIG_NET_HOSTNAME_ENABLE=y
CONFIG_NET_HOSTNAME="zephyr"
CONFIG_NET_HOSTNAME_DYNAMIC=y
CONFIG_NET_HOSTNAME_MAX_LEN=63
# CONFIG_NET_HOSTNAME_UNIQUE is not set
CONFIG_NET_HOSTNAME_LOG_LEVEL=0

#
# Link layer options
#
# CONFIG_NET_L2_DUMMY is not set
# CONFIG_NET_L2_VIRTUAL is not set
CONFIG_NET_L2_ETHERNET_LOG_LEVEL=0
# CONFIG_NET_L2_ETHERNET_RESERVE_HEADER is not set
CONFIG_NET_L2_ETHERNET_MGMT=y
# CONFIG_NET_L2_ETHERNET_ACCEPT_MISMATCH_L3_L2_ADDR is not set
# CONFIG_NET_VLAN is not set
CONFIG_NET_ARP=y
CONFIG_NET_ARP_TABLE_SIZE=2
CONFIG_NET_ARP_GRATUITOUS=y
# CONFIG_NET_ARP_GRATUITOUS_TRANSMISSION is not set
CONFIG_NET_ARP_LOG_LEVEL=0
# CONFIG_NET_GPTP is not set
# CONFIG_NET_LLDP is not set
# CONFIG_NET_ETHERNET_BRIDGE is not set
CONFIG_NET_ETHERNET_FORWARD_UNRECOGNISED_ETHERTYPE=y
# CONFIG_NET_L2_PPP is not set
# CONFIG_NET_L2_OPENTHREAD is not set
# CONFIG_NET_L2_CUSTOM_IEEE802154 is not set
# CONFIG_NET_L2_CANBUS_RAW is not set
CONFIG_NET_L2_WIFI_UTILS=y
CONFIG_NET_L2_WIFI_MGMT=y
CONFIG_NET_L2_WIFI_MGMT_LOG_LEVEL=0
# CONFIG_WIFI_MGMT_RAW_SCAN_RESULTS is not set
CONFIG_WIFI_MGMT_TWT_CHECK_IP=y
# CONFIG_WIFI_MGMT_FORCED_PASSIVE_SCAN is not set
CONFIG_WIFI_SHELL_MAX_AP_STA=1
CONFIG_WIFI_NM=y
CONFIG_WIFI_NM_MAX_MANAGED_INTERFACES=1
CONFIG_WIFI_NM_LOG_LEVEL=0
CONFIG_WIFI_MGMT_AP_STA_INACTIVITY_TIMEOUT=300
# CONFIG_WIFI_MGMT_AP_STA_SKIP_INACTIVITY_POLL is not set
CONFIG_WIFI_MGMT_AP_MAX_NUM_STA=4
CONFIG_WIFI_ENT_IDENTITY_MAX_USERS=8
CONFIG_NET_L2_WIFI_SHELL=y
# CONFIG_NET_L2_PTP is not set
# end of Link layer options

#
# IP stack
#
CONFIG_NET_IP=y
CONFIG_NET_CONNECTION_SOCKETS=y
CONFIG_NET_NATIVE=y
CONFIG_NET_NATIVE_IP=y
CONFIG_NET_NATIVE_IPV6=y
CONFIG_NET_NATIVE_IPV4=y
CONFIG_NET_NATIVE_TCP=y
CONFIG_NET_NATIVE_UDP=y
# CONFIG_NET_OFFLOAD is not set
CONFIG_NET_INIT_PRIO=90
CONFIG_NET_IP_DSCP_ECN=y
CONFIG_NET_IF_MAX_IPV6_COUNT=1
CONFIG_NET_IF_UNICAST_IPV6_ADDR_COUNT=2
CONFIG_NET_IF_MCAST_IPV6_ADDR_COUNT=3
CONFIG_NET_IF_IPV6_PREFIX_COUNT=2
CONFIG_NET_IPV6_MTU=1280
# CONFIG_NET_IPV6_PMTU is not set
CONFIG_NET_INITIAL_HOP_LIMIT=64
CONFIG_NET_INITIAL_MCAST_HOP_LIMIT=1
CONFIG_NET_IPV6_MAX_NEIGHBORS=8
# CONFIG_NET_IPV6_FRAGMENT is not set
CONFIG_NET_IPV6_ND=y
CONFIG_NET_IPV6_DAD=y
CONFIG_NET_IPV6_RS_TIMEOUT=1
CONFIG_NET_IPV6_RA_RDNSS=y
CONFIG_NET_IPV6_IID_EUI_64=y
# CONFIG_NET_IPV6_IID_STABLE is not set
# CONFIG_NET_IPV6_PE is not set
# CONFIG_NET_6LO is not set
CONFIG_NET_IPV6_LOG_LEVEL=0
CONFIG_NET_IPV6_ND_LOG_LEVEL=0
CONFIG_NET_IPV6_PE_LOG_LEVEL=0
CONFIG_NET_ICMPV6_LOG_LEVEL=0
CONFIG_NET_IPV6_NBR_CACHE_LOG_LEVEL=0
CONFIG_NET_IPV4=y
CONFIG_NET_IF_MAX_IPV4_COUNT=1
CONFIG_NET_IF_UNICAST_IPV4_ADDR_COUNT=1
CONFIG_NET_IF_MCAST_IPV4_ADDR_COUNT=1
CONFIG_NET_IPV4_DEFAULT_NETMASK=24
CONFIG_NET_INITIAL_TTL=64
CONFIG_NET_INITIAL_MCAST_TTL=1
CONFIG_NET_IF_MCAST_IPV4_SOURCE_COUNT=1
# CONFIG_NET_ICMPV4_ACCEPT_BROADCAST is not set
# CONFIG_NET_IPV4_ACCEPT_ZERO_BROADCAST is not set
# CONFIG_NET_IPV4_IGMP is not set
# CONFIG_NET_IPV4_ACD is not set
# CONFIG_NET_IPV4_AUTO is not set
# CONFIG_NET_IPV4_HDR_OPTIONS is not set
# CONFIG_NET_IPV4_FRAGMENT is not set
# CONFIG_NET_IPV4_PMTU is not set
CONFIG_NET_IPV4_LOG_LEVEL=0
CONFIG_NET_ICMPV4_LOG_LEVEL=0
CONFIG_NET_IPV4_ACD_LOG_LEVEL=0
# CONFIG_NET_IPV4_MAPPING_TO_IPV6 is not set
# CONFIG_NET_SHELL is not set
CONFIG_NET_TC_RX_COUNT=1
# CONFIG_NET_TC_SKIP_FOR_HIGH_PRIO is not set
CONFIG_NET_TC_THREAD_COOPERATIVE=y
# CONFIG_NET_TC_THREAD_PREEMPTIVE is not set
CONFIG_NET_TC_NUM_PRIORITIES=16
# CONFIG_NET_TC_THREAD_PRIO_CUSTOM is not set
CONFIG_NET_TC_MAPPING_STRICT=y
CONFIG_NET_TX_DEFAULT_PRIORITY=1
CONFIG_NET_RX_DEFAULT_PRIORITY=0
# CONFIG_NET_ALLOW_ANY_PRIORITY is not set
CONFIG_NET_IP_ADDR_CHECK=y
CONFIG_NET_MAX_ROUTERS=2
CONFIG_NET_ROUTE=y
CONFIG_NET_MAX_ROUTES=8
CONFIG_NET_MAX_NEXTHOPS=8
# CONFIG_NET_ROUTE_MCAST is not set
CONFIG_NET_TCP=y
CONFIG_NET_TCP_LOG_LEVEL=0
CONFIG_NET_TCP_WORKER_PRIO=2
CONFIG_NET_TCP_TIME_WAIT_DELAY=1500
CONFIG_NET_TCP_INIT_RETRANSMISSION_TIMEOUT=200
CONFIG_NET_TCP_RANDOMIZED_RTO=y
CONFIG_NET_TCP_RETRY_COUNT=9
CONFIG_NET_TCP_MAX_SEND_WINDOW_SIZE=0
CONFIG_NET_TCP_MAX_RECV_WINDOW_SIZE=0
CONFIG_NET_TCP_RECV_QUEUE_TIMEOUT=2000
CONFIG_NET_TCP_PKT_ALLOC_TIMEOUT=100
CONFIG_NET_TCP_FAST_RETRANSMIT=y
CONFIG_NET_TCP_CONGESTION_AVOIDANCE=y
# CONFIG_NET_TCP_KEEPALIVE is not set
# CONFIG_NET_TCP_ISN_RFC6528 is not set
CONFIG_NET_TCP_REJECT_CONN_WITH_RST=y
# CONFIG_NET_TCP_IPV6_ND_REACHABILITY_HINT is not set
# CONFIG_NET_TEST_PROTOCOL is not set
CONFIG_NET_UDP=y
CONFIG_NET_UDP_MISSING_CHECKSUM=y
CONFIG_NET_UDP_LOG_LEVEL=0
CONFIG_NET_MAX_CONN=8
CONFIG_NET_MAX_CONTEXTS=5
# CONFIG_NET_CONTEXT_NET_PKT_POOL is not set
CONFIG_NET_CONTEXT_SYNC_RECV=y
CONFIG_NET_CONTEXT_CHECK=y
# CONFIG_NET_CONTEXT_PRIORITY is not set
# CONFIG_NET_CONTEXT_TXTIME is not set
# CONFIG_NET_CONTEXT_RCVTIMEO is not set
# CONFIG_NET_CONTEXT_SNDTIMEO is not set
# CONFIG_NET_CONTEXT_RCVBUF is not set
# CONFIG_NET_CONTEXT_SNDBUF is not set
CONFIG_NET_CONTEXT_DSCP_ECN=y
CONFIG_NET_CONTEXT_REUSEADDR=y
CONFIG_NET_CONTEXT_REUSEPORT=y
# CONFIG_NET_CONTEXT_RECV_PKTINFO is not set
# CONFIG_NET_CONTEXT_TIMESTAMPING is not set
# CONFIG_NET_CONTEXT_CLAMP_PORT_RANGE is not set
# CONFIG_NET_TEST is not set
CONFIG_NET_BUF_RX_COUNT=8
CONFIG_NET_BUF_TX_COUNT=16
# CONFIG_NET_BUF_FIXED_DATA_SIZE is not set
CONFIG_NET_BUF_VARIABLE_DATA_SIZE=y
CONFIG_NET_PKT_BUF_RX_DATA_POOL_SIZE=4096
CONFIG_NET_PKT_BUF_TX_DATA_POOL_SIZE=4096
CONFIG_NET_PKT_BUF_USER_DATA_SIZE=4
CONFIG_NET_DEFAULT_IF_FIRST=y
# CONFIG_NET_DEFAULT_IF_UP is not set
# CONFIG_NET_DEFAULT_IF_ETHERNET is not set
# CONFIG_NET_DEFAULT_IF_WIFI is not set
CONFIG_NET_INTERFACE_NAME=y
# CONFIG_NET_PKT_RXTIME_STATS is not set
# CONFIG_NET_PKT_TXTIME_STATS is not set
# CONFIG_NET_PKT_ALLOC_STATS is not set
# CONFIG_NET_PROMISCUOUS_MODE is not set
# CONFIG_NET_DISABLE_ICMP_DESTINATION_UNREACHABLE is not set

#
# Stack usage
#
# end of Stack usage

CONFIG_NET_MGMT=y
CONFIG_NET_MGMT_EVENT=y
CONFIG_NET_MGMT_EVENT_THREAD=y
# CONFIG_NET_MGMT_EVENT_SYSTEM_WORKQUEUE is not set
# CONFIG_NET_MGMT_EVENT_DIRECT is not set
CONFIG_NET_MGMT_EVENT_QUEUE=y
CONFIG_NET_MGMT_EVENT_QUEUE_TIMEOUT=10
CONFIG_NET_MGMT_EVENT_INFO=y
CONFIG_NET_MGMT_EVENT_LOG_LEVEL=0
# CONFIG_NET_DEBUG_MGMT_EVENT_STACK is not set
# CONFIG_NET_MGMT_THREAD_PRIO_CUSTOM is not set
# CONFIG_NET_STATISTICS is not set
# CONFIG_NET_LOG is not set
CONFIG_NET_PKT_LOG_LEVEL=0
# CONFIG_NET_DEBUG_NET_PKT_ALLOC is not set
CONFIG_NET_DEBUG_NET_PKT_EXTERNALS=0
# CONFIG_NET_DEBUG_NET_PKT_NON_FRAGILE_ACCESS is not set
CONFIG_NET_CORE_LOG_LEVEL=0
CONFIG_NET_IF_LOG_LEVEL=0
CONFIG_NET_TC_LOG_LEVEL=0
CONFIG_NET_UTILS_LOG_LEVEL=0
CONFIG_NET_CONTEXT_LOG_LEVEL=0
CONFIG_NET_CONN_LOG_LEVEL=0
CONFIG_NET_ROUTE_LOG_LEVEL=0
# end of IP stack

#
# Network Packet Filtering
#
# CONFIG_NET_PKT_FILTER is not set
# end of Network Packet Filtering

#
# Network Protocols
#
# CONFIG_COAP is not set
CONFIG_DNS_RESOLVER=y
CONFIG_DNS_RESOLVER_AUTO_INIT=y
# CONFIG_MDNS_RESOLVER is not set
# CONFIG_LLMNR_RESOLVER is not set
CONFIG_DNS_RESOLVER_ADDITIONAL_QUERIES=1
CONFIG_DNS_RESOLVER_AI_MAX_ENTRIES=2
CONFIG_DNS_RESOLVER_MAX_SERVERS=1
CONFIG_DNS_RESOLVER_MAX_QUERY_LEN=255
# CONFIG_DNS_SERVER_IP_ADDRESSES is not set
CONFIG_DNS_NUM_CONCUR_QUERIES=1
CONFIG_DNS_RESOLVER_LOG_LEVEL=0
# CONFIG_DNS_RESOLVER_CACHE is not set
# CONFIG_MDNS_RESPONDER is not set
# CONFIG_LLMNR_RESPONDER is not set
# CONFIG_DNS_SD is not set
CONFIG_DNS_SOCKET_DISPATCHER=y
CONFIG_DNS_RESOLVER_ADDITIONAL_BUF_CTR=1
CONFIG_DNS_SOCKET_DISPATCHER_LOG_LEVEL=0
# CONFIG_MQTT_LIB is not set
# CONFIG_MQTT_SN_LIB is not set
# CONFIG_PTP is not set
# CONFIG_TFTP_LIB is not set
# CONFIG_HTTP_PARSER is not set
# CONFIG_HTTP_PARSER_URL is not set
# CONFIG_HTTP_CLIENT is not set
# CONFIG_HTTP_SERVER is not set
CONFIG_NET_HTTP_LOG_LEVEL=0
CONFIG_NET_HTTP_SERVER_LOG_LEVEL=0
# CONFIG_WEBSOCKET_CLIENT is not set
# CONFIG_LWM2M is not set
# CONFIG_SOCKS is not set
# CONFIG_SNTP is not set
# end of Network Protocols

#
# Network Libraries
#
CONFIG_NET_CONFIG_LOG_LEVEL=0
# CONFIG_NET_CONFIG_SETTINGS is not set
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_PRIORITY_DEFAULT=50
CONFIG_NET_SOCKETS_POLL_MAX=0
CONFIG_NET_SOCKETS_CONNECT_TIMEOUT=3000
CONFIG_NET_SOCKETS_DNS_TIMEOUT=30000
CONFIG_NET_SOCKETS_DNS_BACKOFF_INTERVAL=5000
CONFIG_NET_SOCKET_MAX_SEND_WAIT=10000
CONFIG_NET_SOCKETS_SERVICE=y
CONFIG_NET_SOCKETS_SERVICE_THREAD_PRIO=15
CONFIG_NET_SOCKETS_SERVICE_STACK_SIZE=2400
# CONFIG_NET_SOCKETS_SOCKOPT_TLS is not set
CONFIG_NET_SOCKETS_TLS_PRIORITY=45
CONFIG_NET_SOCKETS_TLS_SET_MAX_FRAGMENT_LENGTH=y
# CONFIG_NET_SOCKETS_OFFLOAD is not set
CONFIG_NET_SOCKETS_OFFLOAD_PRIORITY=40
CONFIG_NET_SOCKETS_PACKET=y
CONFIG_NET_SOCKETS_PACKET_DGRAM=y
# CONFIG_NET_SOCKETS_CAN is not set
CONFIG_NET_SOCKETPAIR=y
CONFIG_NET_SOCKETPAIR_BUFFER_SIZE=1024
# CONFIG_NET_SOCKETPAIR_STATIC is not set
CONFIG_NET_SOCKETPAIR_HEAP=y
CONFIG_HEAP_MEM_POOL_ADD_SIZE_SOCKETPAIR=9120
# CONFIG_NET_SOCKETS_NET_MGMT is not set
CONFIG_NET_SOCKETS_LOG_LEVEL=0
# CONFIG_TLS_CREDENTIALS is not set
# end of Network Libraries

#
# Network additional services
#
# CONFIG_NET_CAPTURE is not set
CONFIG_NET_DHCPV4=y
CONFIG_NET_DHCPV4_LOG_LEVEL=0
CONFIG_NET_DHCPV4_INITIAL_DELAY_MAX=10
# CONFIG_NET_DHCPV4_OPTION_CALLBACKS is not set
# CONFIG_NET_DHCPV4_OPTION_CALLBACKS_VENDOR_SPECIFIC is not set
CONFIG_NET_DHCPV4_ACCEPT_UNICAST=y
# CONFIG_NET_DHCPV4_VENDOR_CLASS_IDENTIFIER is not set
CONFIG_NET_DHCPV4_OPTION_DNS_ADDRESS=y
CONFIG_NET_DHCPV4_SERVER=y
CONFIG_NET_DHCPV4_SERVER_LOG_LEVEL=0
CONFIG_NET_DHCPV4_SERVER_INSTANCES=1
CONFIG_NET_DHCPV4_SERVER_ADDR_COUNT=4
CONFIG_NET_DHCPV4_SERVER_ADDR_LEASE_TIME=86400
CONFIG_NET_DHCPV4_SERVER_ADDR_DECLINE_TIME=86400
CONFIG_NET_DHCPV4_SERVER_ICMP_PROBE_TIMEOUT=1000
CONFIG_NET_DHCPV4_SERVER_NAK_UNRECOGNIZED_REQUESTS=y
CONFIG_NET_DHCPV4_SERVER_OPTION_DNS_ADDRESS=""
# CONFIG_NET_DHCPV6 is not set
CONFIG_NET_DHCPV6_DUID_MAX_LEN=22
# CONFIG_NET_TRICKLE is not set
# CONFIG_NET_ZPERF is not set
CONFIG_WIFI_CREDENTIALS=y
# CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_OFF is not set
# CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_ERR is not set
# CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_WRN is not set
# CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_INF is not set
# CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_DBG is not set
CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_DEFAULT=y
CONFIG_WIFI_CREDENTIALS_LOG_LEVEL=3
CONFIG_WIFI_CREDENTIALS_BACKEND_SETTINGS=y
CONFIG_WIFI_CREDENTIALS_MAX_ENTRIES=2
CONFIG_WIFI_CREDENTIALS_SAE_PASSWORD_LENGTH=128
CONFIG_WIFI_CREDENTIALS_SHELL=y
CONFIG_WIFI_CREDENTIALS_CONNECT_STORED=y
CONFIG_WIFI_CREDENTIALS_CONNECT_STORED_CONNECTION_TIMEOUT=30
# CONFIG_WIFI_CREDENTIALS_STATIC is not set
# end of Network additional services

CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_CONNECTION_MANAGER_LOG_LEVEL=0
CONFIG_NET_CONNECTION_MANAGER_MONITOR_PRIORITY=1
CONFIG_NET_CONNECTION_MANAGER_AUTO_IF_DOWN=y
CONFIG_NET_CONNECTION_MANAGER_CONNECTIVITY_WIFI_MGMT=y
CONFIG_CONNECTIVITY_WIFI_MGMT_APPLICATION=y
# end of Networking

#
# Power Management
#
# CONFIG_PM_POLICY_LATENCY_STANDALONE is not set
# end of Power Management

#
# Portability
#
# end of Portability

# CONFIG_PROFILING is not set

#
# Random Number Generators
#
# CONFIG_TEST_RANDOM_GENERATOR is not set
CONFIG_TIMER_RANDOM_INITIAL_STATE=123456789
CONFIG_ENTROPY_DEVICE_RANDOM_GENERATOR=y
# CONFIG_XOSHIRO_RANDOM_GENERATOR is not set
CONFIG_CSPRNG_AVAILABLE=y
CONFIG_CSPRNG_ENABLED=y
CONFIG_HARDWARE_DEVICE_CS_GENERATOR=y
# CONFIG_CTR_DRBG_CSPRNG_GENERATOR is not set
# end of Random Number Generators

# CONFIG_RTIO is not set

#
# SD
#
# CONFIG_MMC_STACK is not set
# CONFIG_SDMMC_STACK is not set
# CONFIG_SDIO_STACK is not set
# end of SD

# CONFIG_SECURE_STORAGE is not set
CONFIG_SETTINGS=y
# CONFIG_SETTINGS_LOG_LEVEL_OFF is not set
# CONFIG_SETTINGS_LOG_LEVEL_ERR is not set
# CONFIG_SETTINGS_LOG_LEVEL_WRN is not set
# CONFIG_SETTINGS_LOG_LEVEL_INF is not set
# CONFIG_SETTINGS_LOG_LEVEL_DBG is not set
CONFIG_SETTINGS_LOG_LEVEL_DEFAULT=y
CONFIG_SETTINGS_LOG_LEVEL=3
# CONFIG_SETTINGS_RUNTIME is not set
CONFIG_SETTINGS_DYNAMIC_HANDLERS=y
CONFIG_SETTINGS_ZMS=y
# CONFIG_SETTINGS_ZMS_LL_CACHE is not set
# CONFIG_SETTINGS_FILE is not set
# CONFIG_SETTINGS_NVS is not set
# CONFIG_SETTINGS_CUSTOM is not set
# CONFIG_SETTINGS_NONE is not set
# CONFIG_SETTINGS_ZMS_CUSTOM_SECTOR_COUNT is not set
CONFIG_SETTINGS_ZMS_MAX_COLLISIONS_BITS=4
# CONFIG_SETTINGS_ZMS_NO_LL_DELETE is not set
# CONFIG_SETTINGS_ZMS_LOAD_SUBTREE_PATH is not set
# CONFIG_SETTINGS_SHELL is not set
CONFIG_SHELL=y
# CONFIG_SHELL_LOG_LEVEL_OFF is not set
# CONFIG_SHELL_LOG_LEVEL_ERR is not set
# CONFIG_SHELL_LOG_LEVEL_WRN is not set
# CONFIG_SHELL_LOG_LEVEL_INF is not set
# CONFIG_SHELL_LOG_LEVEL_DBG is not set
CONFIG_SHELL_LOG_LEVEL_DEFAULT=y
CONFIG_SHELL_LOG_LEVEL=3
CONFIG_SHELL_BACKENDS=y
CONFIG_SHELL_BACKEND_SERIAL_INIT_PRIORITY=90
CONFIG_SHELL_PROMPT_UART="uart:~$ "
CONFIG_SHELL_BACKEND_SERIAL_INTERRUPT_DRIVEN=y
# CONFIG_SHELL_BACKEND_SERIAL_API_POLLING is not set
CONFIG_SHELL_BACKEND_SERIAL_API_INTERRUPT_DRIVEN=y
# CONFIG_SHELL_BACKEND_SERIAL_API_ASYNC is not set
# CONFIG_SHELL_BACKEND_SERIAL_FORCE_TX_BLOCKING_MODE is not set
CONFIG_SHELL_BACKEND_SERIAL_TX_RING_BUFFER_SIZE=8
CONFIG_SHELL_BACKEND_SERIAL_RX_RING_BUFFER_SIZE=64
CONFIG_SHELL_BACKEND_SERIAL_LOG_MESSAGE_QUEUE_TIMEOUT=100
CONFIG_SHELL_BACKEND_SERIAL_LOG_MESSAGE_QUEUE_SIZE=512
CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_DEFAULT=y
# CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_DBG is not set
# CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_INF is not set
# CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_WRN is not set
# CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_ERR is not set
# CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_NONE is not set
CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL=5
# CONFIG_SHELL_BACKEND_MQTT is not set
# CONFIG_SHELL_BACKEND_TELNET is not set
# CONFIG_SHELL_BACKEND_DUMMY is not set
# CONFIG_SHELL_MINIMAL is not set
CONFIG_SHELL_DEVICE_HELPERS=y
# CONFIG_SHELL_THREAD_PRIORITY_OVERRIDE is not set
CONFIG_SHELL_BACKSPACE_MODE_DELETE=y
CONFIG_SHELL_PROMPT_CHANGE=y
CONFIG_SHELL_PROMPT_BUFF_SIZE=20
CONFIG_SHELL_CMD_BUFF_SIZE=256
CONFIG_SHELL_PRINTF_BUFF_SIZE=30
CONFIG_SHELL_DEFAULT_TERMINAL_WIDTH=80
CONFIG_SHELL_DEFAULT_TERMINAL_HEIGHT=24
CONFIG_SHELL_ARGC_MAX=30
CONFIG_SHELL_TAB=y
CONFIG_SHELL_TAB_AUTOCOMPLETION=y
CONFIG_SHELL_ASCII_FILTER=y
CONFIG_SHELL_WILDCARD=y
CONFIG_SHELL_MSG_CMD_NOT_FOUND=y
CONFIG_SHELL_MSG_SPECIFY_SUBCOMMAND=y
CONFIG_SHELL_ECHO_STATUS=y
# CONFIG_SHELL_START_OBSCURED is not set
CONFIG_SHELL_VT100_COMMANDS=y
CONFIG_SHELL_VT100_COLORS=y
CONFIG_SHELL_GETOPT=y
CONFIG_SHELL_METAKEYS=y
CONFIG_SHELL_HELP=y
CONFIG_SHELL_HELP_ON_WRONG_ARGUMENT_COUNT=y
CONFIG_SHELL_HISTORY=y
CONFIG_SHELL_HISTORY_BUFFER=512
CONFIG_SHELL_STATS=y
CONFIG_SHELL_CMDS=y
CONFIG_SHELL_CMDS_RESIZE=y
# CONFIG_SHELL_CMDS_SELECT is not set
CONFIG_SHELL_CMD_ROOT=""
CONFIG_SHELL_LOG_BACKEND=y
# CONFIG_SHELL_LOG_BACKEND_CUSTOM is not set
CONFIG_SHELL_LOG_FORMAT_TIMESTAMP=y
CONFIG_SHELL_AUTOSTART=y
CONFIG_SHELL_CMDS_RETURN_VALUE=y
# CONFIG_SHELL_CUSTOM_HEADER is not set
CONFIG_DEVICE_SHELL=y
CONFIG_DATE_SHELL=y
CONFIG_DEVMEM_SHELL=y
CONFIG_KERNEL_SHELL=y
CONFIG_KERNEL_THREAD_SHELL=y
CONFIG_KERNEL_THREAD_SHELL_LIST=y
CONFIG_KERNEL_THREAD_SHELL_STACKS=y
# CONFIG_STATS is not set

#
# Storage
#
CONFIG_FLASH_MAP=y
# CONFIG_FLASH_MAP_SHELL is not set
# CONFIG_FLASH_AREA_CHECK_INTEGRITY is not set
# CONFIG_FLASH_MAP_LABELS is not set
# CONFIG_STREAM_FLASH is not set
# end of Storage

# CONFIG_TASK_WDT is not set

#
# Testing
#
# CONFIG_ZTEST is not set
# CONFIG_ZTEST_MOCKING is not set
# CONFIG_ZTRESS is not set
# CONFIG_TEST is not set
# CONFIG_FORCE_COVERAGE is not set
# CONFIG_TEST_USERSPACE is not set
# end of Testing

# CONFIG_TIMING_FUNCTIONS is not set
# CONFIG_TRACING is not set
# CONFIG_USB_DEVICE_STACK is not set
# CONFIG_USB_DEVICE_STACK_NEXT is not set
# CONFIG_USB_HOST_STACK is not set
# CONFIG_USBC_STACK is not set
# CONFIG_ZBUS is not set
# CONFIG_MODULES is not set
# end of Subsystems and OS Services

CONFIG_TOOLCHAIN_ZEPHYR_0_17=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_GNU_EXTENSIONS=y

#
# Build and Link Features
#

#
# Linker Options
#
# CONFIG_LINKER_ORPHAN_SECTION_PLACE is not set
CONFIG_LINKER_ORPHAN_SECTION_WARN=y
# CONFIG_LINKER_ORPHAN_SECTION_ERROR is not set
CONFIG_ROM_END_OFFSET=0
CONFIG_LD_LINKER_SCRIPT_SUPPORTED=y
CONFIG_LD_LINKER_TEMPLATE=y
# CONFIG_CMAKE_LINKER_GENERATOR is not set
# CONFIG_HAVE_CUSTOM_LINKER_SCRIPT is not set
CONFIG_LINKER_SORT_BY_ALIGNMENT=y

#
# Linker Sections
#
# CONFIG_LINKER_USE_BOOT_SECTION is not set
# CONFIG_LINKER_USE_PINNED_SECTION is not set
CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT=y
CONFIG_LINKER_LAST_SECTION_ID=y
CONFIG_LINKER_LAST_SECTION_ID_PATTERN=0xE015E015
CONFIG_LINKER_USE_RELAX=y
# end of Linker Sections

CONFIG_LINKER_ITERABLE_SUBALIGN=4
CONFIG_LINKER_DEVNULL_SUPPORT=y
# CONFIG_LINKER_DEVNULL_MEMORY is not set
# end of Linker Options

#
# Compiler Options
#
# CONFIG_STD_C90 is not set
CONFIG_STD_C99=y
# CONFIG_STD_C11 is not set
# CONFIG_STD_C17 is not set
# CONFIG_STD_C23 is not set
CONFIG_TOOLCHAIN_SUPPORTS_GNU_EXTENSIONS=y
# CONFIG_GNU_C_EXTENSIONS is not set
# CONFIG_CODING_GUIDELINE_CHECK is not set
# CONFIG_COMPILER_FREESTANDING is not set
CONFIG_SIZE_OPTIMIZATIONS=y
# CONFIG_SIZE_OPTIMIZATIONS_AGGRESSIVE is not set
# CONFIG_SPEED_OPTIMIZATIONS is not set
# CONFIG_DEBUG_OPTIMIZATIONS is not set
# CONFIG_NO_OPTIMIZATIONS is not set
CONFIG_LTO=y
# CONFIG_COMPILER_WARNINGS_AS_ERRORS is not set
# CONFIG_COMPILER_SAVE_TEMPS is not set
CONFIG_COMPILER_TRACK_MACRO_EXPANSION=y
CONFIG_COMPILER_COLOR_DIAGNOSTICS=y
# CONFIG_FORTIFY_SOURCE_NONE is not set
CONFIG_FORTIFY_SOURCE_COMPILE_TIME=y
# CONFIG_FORTIFY_SOURCE_RUN_TIME is not set
CONFIG_COMPILER_OPT=""
# CONFIG_MISRA_SANE is not set
# end of Compiler Options

# CONFIG_ASSERT_ON_ERRORS is not set
# CONFIG_NO_RUNTIME_CHECKS is not set
CONFIG_RUNTIME_ERROR_CHECKS=y

#
# Build Options
#
CONFIG_KERNEL_BIN_NAME="zephyr"
CONFIG_OUTPUT_STAT=y
# CONFIG_OUTPUT_SYMBOLS is not set
# CONFIG_OUTPUT_DISASSEMBLY is not set
CONFIG_OUTPUT_PRINT_MEMORY_USAGE=y
# CONFIG_CLEANUP_INTERMEDIATE_FILES is not set
CONFIG_BUILD_GAP_FILL_PATTERN=0xFF
# CONFIG_BUILD_NO_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_HEX_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_EXE is not set
# CONFIG_BUILD_OUTPUT_S19_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_UF2 is not set
# CONFIG_BUILD_OUTPUT_STRIPPED is not set
# CONFIG_BUILD_OUTPUT_COMPRESS_DEBUG_SECTIONS is not set
# CONFIG_BUILD_ALIGN_LMA is not set
# CONFIG_APPLICATION_DEFINED_SYSCALL is not set
# CONFIG_MAKEFILE_EXPORTS is not set
# CONFIG_BUILD_OUTPUT_META is not set
CONFIG_BUILD_OUTPUT_STRIP_PATHS=y
CONFIG_CHECK_INIT_PRIORITIES=y
# CONFIG_EMIT_ALL_SYSCALLS is not set
# end of Build Options

CONFIG_DEPRECATED=y
CONFIG_WARN_DEPRECATED=y
CONFIG_EXPERIMENTAL=y
CONFIG_ENFORCE_ZEPHYR_STDINT=y
# end of Build and Link Features

#
# Boot Options
#
# CONFIG_IS_BOOTLOADER is not set
# CONFIG_BOOTLOADER_BOSSA is not set
# end of Boot Options

#
# Compatibility
#
CONFIG_LEGACY_GENERATED_INCLUDE_PATH=y
# end of Compatibility
