ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x6dc95
  Start of program headers:          52 (bytes into file)
  Start of section headers:          14775164 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         6
  Size of section headers:           40 (bytes)
  Number of section headers:         56
  Section header string table index: 55

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 000100 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000158 000258 08c2c4 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0008c41c 08c51c 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0008c424 08c524 000118 00   A  0   0  4
  [ 5] device_area       PROGBITS        0008c53c 08c63c 0000d8 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0008c614 0c41bc 000000 00   W  0   0  1
  [ 7] flash_driver[...] PROGBITS        0008c614 08c714 000030 00   A  0   0  4
  [ 8] gpio_driver_[...] PROGBITS        0008c644 08c744 000024 00   A  0   0  4
  [ 9] spi_driver_a[...] PROGBITS        0008c668 08c768 000008 00   A  0   0  4
  [10] clock_contro[...] PROGBITS        0008c670 08c770 00001c 00   A  0   0  4
  [11] uart_driver_[...] PROGBITS        0008c68c 08c78c 000044 00   A  0   0  4
  [12] net_socket_r[...] PROGBITS        0008c6d0 08c7d0 000020 00   A  0   0  4
  [13] net_socket_s[...] PROGBITS        0008c6f0 08c7f0 000018 00   A  0   0  4
  [14] log_const_area    PROGBITS        0008c708 08c808 000168 00   A  0   0  4
  [15] log_backend_area  PROGBITS        0008c870 08c970 000010 00   A  0   0  4
  [16] shell_area        PROGBITS        0008c880 08c980 000030 00   A  0   0  4
  [17] shell_root_c[...] PROGBITS        0008c8b0 08c9b0 000030 00   A  0   0  4
  [18] shell_subcmd[...] PROGBITS        0008c8e0 08c9e0 0002e4 00   A  0   0  4
  [19] tdata             PROGBITS        0008cbc8 08ccc8 000008 00 WAT  0   0  8
  [20] tbss              NOBITS          0008cbd0 08ccd0 00002c 00 WAT  0   0  4
  [21] rodata            PROGBITS        0008cbd0 08ccd0 035ee8 00  WA  0   0 16
  [22] .ramfunc          PROGBITS        20000000 0c41bc 000000 00   W  0   0  1
  [23] datas             PROGBITS        20000000 0c2bb8 000eb0 00  WA  0   0  8
  [24] device_states     PROGBITS        20000eb0 0c3a68 000012 00  WA  0   0  1
  [25] log_mpsc_pbu[...] PROGBITS        20000ec4 0c3a7c 000044 00  WA  0   0  4
  [26] log_msg_ptr_area  PROGBITS        20000f08 0c3ac0 000004 00  WA  0   0  4
  [27] log_dynamic_area  PROGBITS        20000f0c 0c3ac4 0000b4 00  WA  0   0  4
  [28] k_mem_slab_area   PROGBITS        20000fc0 0c3b78 0000a0 00  WA  0   0  4
  [29] k_heap_area       PROGBITS        20001060 0c3c18 000090 00  WA  0   0  4
  [30] k_mutex_area      PROGBITS        200010f0 0c3ca8 000258 00  WA  0   0  4
  [31] k_msgq_area       PROGBITS        20001348 0c3f00 000034 00  WA  0   0  4
  [32] k_sem_area        PROGBITS        2000137c 0c3f34 000090 00  WA  0   0  4
  [33] k_condvar_area    PROGBITS        2000140c 0c3fc4 000008 00  WA  0   0  4
  [34] net_buf_pool_area PROGBITS        20001414 0c3fcc 000104 00  WA  0   0  4
  [35] net_if_area       PROGBITS        20001518 0c40d0 000090 00  WA  0   0  8
  [36] net_if_dev_area   PROGBITS        200015a8 0c4160 00001c 00  WA  0   0  4
  [37] net_l2_area       PROGBITS        200015c4 0c417c 000014 00   A  0   0  4
  [38] conn_mgr_con[...] PROGBITS        200015d8 0c4190 000018 00  WA  0   0  4
  [39] wifi_nm_inst[...] PROGBITS        200015f0 0c41a8 000010 00  WA  0   0  4
  [40] .comment          PROGBITS        00000000 0c41bc 000067 01  MS  0   0  1
  [41] .debug_aranges    PROGBITS        00000000 0c4228 005c60 00      0   0  8
  [42] .debug_info       PROGBITS        00000000 0c9e88 87b46a 00      0   0  1
  [43] .debug_abbrev     PROGBITS        00000000 9452f2 04723e 00      0   0  1
  [44] .debug_line       PROGBITS        00000000 98c530 172014 00      0   0  1
  [45] .debug_frame      PROGBITS        00000000 afe544 01777c 00      0   0  4
  [46] .debug_str        PROGBITS        00000000 b15cc0 06a4e6 01  MS  0   0  1
  [47] .debug_loc        PROGBITS        00000000 b801a6 1ab935 00      0   0  1
  [48] .debug_ranges     PROGBITS        00000000 d2bae0 03ca88 00      0   0  8
  [49] .ARM.attributes   ARM_ATTRIBUTES  00000000 d68568 000038 00      0   0  1
  [50] .last_section     PROGBITS        000c40b8 0c41b8 000004 00  WA  0   0  4
  [51] bss               NOBITS          20001600 0c41c0 0067b0 00  WA  0   0  8
  [52] noinit            NOBITS          20007db0 0c41c0 03269c 00  WA  0   0  8
  [53] .symtab           SYMTAB          00000000 d685a0 07ec90 10     54 27027  4
  [54] .strtab           STRTAB          00000000 de7230 02fe46 00      0   0  1
  [55] .shstrtab         STRTAB          00000000 e17076 000305 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x08c51c 0x0008c41c 0x0008c41c 0x00008 0x00008 R   0x4
  LOAD           0x000100 0x00000000 0x00000000 0xc2ab8 0xc2ab8 RWE 0x10
  LOAD           0x0c2bb8 0x20000000 0x000c2ab8 0x01600 0x01600 RW  0x8
  LOAD           0x0c41b8 0x000c40b8 0x000c40b8 0x00004 0x00004 RW  0x4
  LOAD           0x000000 0x20001600 0x20001600 0x00000 0x38e4c RW  0x8
  TLS            0x08ccc8 0x0008cbc8 0x0008cbc8 0x00008 0x00034 R   0x8

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area flash_driver_api_area gpio_driver_api_area spi_driver_api_area clock_control_driver_api_area uart_driver_api_area net_socket_register_area net_socket_service_desc_area log_const_area log_backend_area shell_area shell_root_cmds_area shell_subcmds_area tdata rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area log_dynamic_area k_mem_slab_area k_heap_area k_mutex_area k_msgq_area k_sem_area k_condvar_area net_buf_pool_area net_if_area net_if_dev_area net_l2_area conn_mgr_conn_binding_area wifi_nm_instance_area 
   03     .last_section 
   04     bss noinit 
   05     tdata tbss 
