
/* AUTO-GENERATED by gen_isr_tables.py, do not edit! */

#include <zephyr/toolchain.h>
#include <zephyr/linker/sections.h>
#include <zephyr/sw_isr_table.h>
#include <zephyr/arch/cpu.h>

_Z_ISR_DIRECT_TABLE_ENTRY(0, _isr_wrapper, ".irq_spurious.0x0");
_Z_ISR_DIRECT_TABLE_ENTRY(1, _isr_wrapper, ".irq_spurious.0x1");
_Z_ISR_DIRECT_TABLE_ENTRY(2, _isr_wrapper, ".irq_spurious.0x2");
_Z_ISR_DIRECT_TABLE_ENTRY(3, _isr_wrapper, ".irq_spurious.0x3");
_Z_ISR_DIRECT_TABLE_ENTRY(4, _isr_wrapper, ".irq_spurious.0x4");
_Z_ISR_DIRECT_TABLE_ENTRY(5, _isr_wrapper, ".irq_spurious.0x5");
_Z_ISR_DIRECT_TABLE_ENTRY(6, _isr_wrapper, ".irq_spurious.0x6");
_Z_ISR_DIRECT_TABLE_ENTRY(7, _isr_wrapper, ".irq_spurious.0x7");
_Z_ISR_DIRECT_TABLE_ENTRY(8, _isr_wrapper, ".irq_spurious.0x8");
_Z_ISR_DIRECT_TABLE_ENTRY(9, _isr_wrapper, ".irq_spurious.0x9");
_Z_ISR_DIRECT_TABLE_ENTRY(10, _isr_wrapper, ".irq_spurious.0xa");
_Z_ISR_DIRECT_TABLE_ENTRY(11, _isr_wrapper, ".irq_spurious.0xb");
_Z_ISR_DIRECT_TABLE_ENTRY(12, _isr_wrapper, ".irq_spurious.0xc");
_Z_ISR_DIRECT_TABLE_ENTRY(13, _isr_wrapper, ".irq_spurious.0xd");
_Z_ISR_DIRECT_TABLE_ENTRY(14, _isr_wrapper, ".irq_spurious.0xe");
_Z_ISR_DIRECT_TABLE_ENTRY(15, _isr_wrapper, ".irq_spurious.0xf");
_Z_ISR_DIRECT_TABLE_ENTRY(16, _isr_wrapper, ".irq_spurious.0x10");
_Z_ISR_DIRECT_TABLE_ENTRY(17, _isr_wrapper, ".irq_spurious.0x11");
_Z_ISR_DIRECT_TABLE_ENTRY(18, _isr_wrapper, ".irq_spurious.0x12");
_Z_ISR_DIRECT_TABLE_ENTRY(19, _isr_wrapper, ".irq_spurious.0x13");
_Z_ISR_DIRECT_TABLE_ENTRY(20, _isr_wrapper, ".irq_spurious.0x14");
_Z_ISR_DIRECT_TABLE_ENTRY(21, _isr_wrapper, ".irq_spurious.0x15");
_Z_ISR_DIRECT_TABLE_ENTRY(22, _isr_wrapper, ".irq_spurious.0x16");
_Z_ISR_DIRECT_TABLE_ENTRY(23, _isr_wrapper, ".irq_spurious.0x17");
_Z_ISR_DIRECT_TABLE_ENTRY(24, _isr_wrapper, ".irq_spurious.0x18");
_Z_ISR_DIRECT_TABLE_ENTRY(25, _isr_wrapper, ".irq_spurious.0x19");
_Z_ISR_DIRECT_TABLE_ENTRY(26, _isr_wrapper, ".irq_spurious.0x1a");
_Z_ISR_DIRECT_TABLE_ENTRY(27, _isr_wrapper, ".irq_spurious.0x1b");
_Z_ISR_DIRECT_TABLE_ENTRY(28, _isr_wrapper, ".irq_spurious.0x1c");
_Z_ISR_DIRECT_TABLE_ENTRY(29, _isr_wrapper, ".irq_spurious.0x1d");
_Z_ISR_DIRECT_TABLE_ENTRY(30, _isr_wrapper, ".irq_spurious.0x1e");
_Z_ISR_DIRECT_TABLE_ENTRY(31, _isr_wrapper, ".irq_spurious.0x1f");
_Z_ISR_DIRECT_TABLE_ENTRY(32, _isr_wrapper, ".irq_spurious.0x20");
_Z_ISR_DIRECT_TABLE_ENTRY(33, _isr_wrapper, ".irq_spurious.0x21");
_Z_ISR_DIRECT_TABLE_ENTRY(34, _isr_wrapper, ".irq_spurious.0x22");
_Z_ISR_DIRECT_TABLE_ENTRY(35, _isr_wrapper, ".irq_spurious.0x23");
_Z_ISR_DIRECT_TABLE_ENTRY(36, _isr_wrapper, ".irq_spurious.0x24");
_Z_ISR_DIRECT_TABLE_ENTRY(37, _isr_wrapper, ".irq_spurious.0x25");
_Z_ISR_DIRECT_TABLE_ENTRY(38, _isr_wrapper, ".irq_spurious.0x26");
_Z_ISR_DIRECT_TABLE_ENTRY(39, _isr_wrapper, ".irq_spurious.0x27");
_Z_ISR_DIRECT_TABLE_ENTRY(40, _isr_wrapper, ".irq_spurious.0x28");
_Z_ISR_DIRECT_TABLE_ENTRY(41, _isr_wrapper, ".irq_spurious.0x29");
_Z_ISR_DIRECT_TABLE_ENTRY(42, _isr_wrapper, ".irq_spurious.0x2a");
_Z_ISR_DIRECT_TABLE_ENTRY(43, _isr_wrapper, ".irq_spurious.0x2b");
_Z_ISR_DIRECT_TABLE_ENTRY(44, _isr_wrapper, ".irq_spurious.0x2c");
_Z_ISR_DIRECT_TABLE_ENTRY(45, _isr_wrapper, ".irq_spurious.0x2d");
_Z_ISR_DIRECT_TABLE_ENTRY(46, _isr_wrapper, ".irq_spurious.0x2e");
_Z_ISR_DIRECT_TABLE_ENTRY(47, _isr_wrapper, ".irq_spurious.0x2f");
_Z_ISR_DIRECT_TABLE_ENTRY(48, _isr_wrapper, ".irq_spurious.0x30");
_Z_ISR_DIRECT_TABLE_ENTRY(49, _isr_wrapper, ".irq_spurious.0x31");
_Z_ISR_DIRECT_TABLE_ENTRY(50, _isr_wrapper, ".irq_spurious.0x32");
_Z_ISR_DIRECT_TABLE_ENTRY(51, _isr_wrapper, ".irq_spurious.0x33");
_Z_ISR_DIRECT_TABLE_ENTRY(52, _isr_wrapper, ".irq_spurious.0x34");
_Z_ISR_DIRECT_TABLE_ENTRY(53, _isr_wrapper, ".irq_spurious.0x35");
_Z_ISR_DIRECT_TABLE_ENTRY(54, _isr_wrapper, ".irq_spurious.0x36");
_Z_ISR_DIRECT_TABLE_ENTRY(55, _isr_wrapper, ".irq_spurious.0x37");
_Z_ISR_DIRECT_TABLE_ENTRY(56, _isr_wrapper, ".irq_spurious.0x38");
_Z_ISR_DIRECT_TABLE_ENTRY(57, _isr_wrapper, ".irq_spurious.0x39");
_Z_ISR_DIRECT_TABLE_ENTRY(58, _isr_wrapper, ".irq_spurious.0x3a");
_Z_ISR_DIRECT_TABLE_ENTRY(59, _isr_wrapper, ".irq_spurious.0x3b");
_Z_ISR_DIRECT_TABLE_ENTRY(60, _isr_wrapper, ".irq_spurious.0x3c");
_Z_ISR_DIRECT_TABLE_ENTRY(61, _isr_wrapper, ".irq_spurious.0x3d");
_Z_ISR_DIRECT_TABLE_ENTRY(62, _isr_wrapper, ".irq_spurious.0x3e");
_Z_ISR_DIRECT_TABLE_ENTRY(63, _isr_wrapper, ".irq_spurious.0x3f");
_Z_ISR_DIRECT_TABLE_ENTRY(64, _isr_wrapper, ".irq_spurious.0x40");
_Z_ISR_DIRECT_TABLE_ENTRY(65, _isr_wrapper, ".irq_spurious.0x41");
_Z_ISR_DIRECT_TABLE_ENTRY(66, _isr_wrapper, ".irq_spurious.0x42");
_Z_ISR_DIRECT_TABLE_ENTRY(67, _isr_wrapper, ".irq_spurious.0x43");
_Z_ISR_DIRECT_TABLE_ENTRY(68, _isr_wrapper, ".irq_spurious.0x44");
_Z_ISR_TABLE_ENTRY(0, z_irq_spurious, NULL, ".isr_generated.0x0");
_Z_ISR_TABLE_ENTRY(1, z_irq_spurious, NULL, ".isr_generated.0x1");
_Z_ISR_TABLE_ENTRY(2, z_irq_spurious, NULL, ".isr_generated.0x2");
_Z_ISR_TABLE_ENTRY(3, z_irq_spurious, NULL, ".isr_generated.0x3");
_Z_ISR_TABLE_ENTRY(4, z_irq_spurious, NULL, ".isr_generated.0x4");
/* ISR: 5 implemented in app in ".irq.WEST_TOPDIR/zephyr/drivers/clock_control/clock_control_nrf.c.0" section. */
_Z_ISR_TABLE_ENTRY(6, z_irq_spurious, NULL, ".isr_generated.0x6");
_Z_ISR_TABLE_ENTRY(7, z_irq_spurious, NULL, ".isr_generated.0x7");
/* ISR: 8 implemented in app in ".irq.WEST_TOPDIR/zephyr/drivers/serial/uart_nrfx_uarte.c.2" section. */
_Z_ISR_TABLE_ENTRY(9, z_irq_spurious, NULL, ".isr_generated.0x9");
/* ISR: 10 implemented in app in ".irq.WEST_TOPDIR/zephyr/drivers/spi/spi_nrfx_spim.c.0" section. */
_Z_ISR_TABLE_ENTRY(11, z_irq_spurious, NULL, ".isr_generated.0xb");
_Z_ISR_TABLE_ENTRY(12, z_irq_spurious, NULL, ".isr_generated.0xc");
/* ISR: 13 implemented in app in ".irq.WEST_TOPDIR/zephyr/drivers/gpio/gpio_nrfx.c.0" section. */
_Z_ISR_TABLE_ENTRY(14, z_irq_spurious, NULL, ".isr_generated.0xe");
_Z_ISR_TABLE_ENTRY(15, z_irq_spurious, NULL, ".isr_generated.0xf");
_Z_ISR_TABLE_ENTRY(16, z_irq_spurious, NULL, ".isr_generated.0x10");
_Z_ISR_TABLE_ENTRY(17, z_irq_spurious, NULL, ".isr_generated.0x11");
_Z_ISR_TABLE_ENTRY(18, z_irq_spurious, NULL, ".isr_generated.0x12");
_Z_ISR_TABLE_ENTRY(19, z_irq_spurious, NULL, ".isr_generated.0x13");
_Z_ISR_TABLE_ENTRY(20, z_irq_spurious, NULL, ".isr_generated.0x14");
/* ISR: 21 implemented in app in ".irq.WEST_TOPDIR/zephyr/drivers/timer/nrf_rtc_timer.c.0" section. */
_Z_ISR_TABLE_ENTRY(22, z_irq_spurious, NULL, ".isr_generated.0x16");
_Z_ISR_TABLE_ENTRY(23, z_irq_spurious, NULL, ".isr_generated.0x17");
_Z_ISR_TABLE_ENTRY(24, z_irq_spurious, NULL, ".isr_generated.0x18");
_Z_ISR_TABLE_ENTRY(25, z_irq_spurious, NULL, ".isr_generated.0x19");
_Z_ISR_TABLE_ENTRY(26, z_irq_spurious, NULL, ".isr_generated.0x1a");
_Z_ISR_TABLE_ENTRY(27, z_irq_spurious, NULL, ".isr_generated.0x1b");
_Z_ISR_TABLE_ENTRY(28, z_irq_spurious, NULL, ".isr_generated.0x1c");
_Z_ISR_TABLE_ENTRY(29, z_irq_spurious, NULL, ".isr_generated.0x1d");
_Z_ISR_TABLE_ENTRY(30, z_irq_spurious, NULL, ".isr_generated.0x1e");
_Z_ISR_TABLE_ENTRY(31, z_irq_spurious, NULL, ".isr_generated.0x1f");
_Z_ISR_TABLE_ENTRY(32, z_irq_spurious, NULL, ".isr_generated.0x20");
_Z_ISR_TABLE_ENTRY(33, z_irq_spurious, NULL, ".isr_generated.0x21");
_Z_ISR_TABLE_ENTRY(34, z_irq_spurious, NULL, ".isr_generated.0x22");
_Z_ISR_TABLE_ENTRY(35, z_irq_spurious, NULL, ".isr_generated.0x23");
_Z_ISR_TABLE_ENTRY(36, z_irq_spurious, NULL, ".isr_generated.0x24");
_Z_ISR_TABLE_ENTRY(37, z_irq_spurious, NULL, ".isr_generated.0x25");
_Z_ISR_TABLE_ENTRY(38, z_irq_spurious, NULL, ".isr_generated.0x26");
_Z_ISR_TABLE_ENTRY(39, z_irq_spurious, NULL, ".isr_generated.0x27");
_Z_ISR_TABLE_ENTRY(40, z_irq_spurious, NULL, ".isr_generated.0x28");
_Z_ISR_TABLE_ENTRY(41, z_irq_spurious, NULL, ".isr_generated.0x29");
_Z_ISR_TABLE_ENTRY(42, z_irq_spurious, NULL, ".isr_generated.0x2a");
/* ISR: 43 implemented in app in ".irq.WEST_TOPDIR/zephyr/modules/nrf_wifi/bus/qspi_if.c.0" section. */
_Z_ISR_TABLE_ENTRY(44, z_irq_spurious, NULL, ".isr_generated.0x2c");
_Z_ISR_TABLE_ENTRY(45, z_irq_spurious, NULL, ".isr_generated.0x2d");
_Z_ISR_TABLE_ENTRY(46, z_irq_spurious, NULL, ".isr_generated.0x2e");
_Z_ISR_TABLE_ENTRY(47, z_irq_spurious, NULL, ".isr_generated.0x2f");
_Z_ISR_TABLE_ENTRY(48, z_irq_spurious, NULL, ".isr_generated.0x30");
_Z_ISR_TABLE_ENTRY(49, z_irq_spurious, NULL, ".isr_generated.0x31");
_Z_ISR_TABLE_ENTRY(50, z_irq_spurious, NULL, ".isr_generated.0x32");
_Z_ISR_TABLE_ENTRY(51, z_irq_spurious, NULL, ".isr_generated.0x33");
_Z_ISR_TABLE_ENTRY(52, z_irq_spurious, NULL, ".isr_generated.0x34");
_Z_ISR_TABLE_ENTRY(53, z_irq_spurious, NULL, ".isr_generated.0x35");
_Z_ISR_TABLE_ENTRY(54, z_irq_spurious, NULL, ".isr_generated.0x36");
_Z_ISR_TABLE_ENTRY(55, z_irq_spurious, NULL, ".isr_generated.0x37");
_Z_ISR_TABLE_ENTRY(56, z_irq_spurious, NULL, ".isr_generated.0x38");
_Z_ISR_TABLE_ENTRY(57, z_irq_spurious, NULL, ".isr_generated.0x39");
_Z_ISR_TABLE_ENTRY(58, z_irq_spurious, NULL, ".isr_generated.0x3a");
_Z_ISR_TABLE_ENTRY(59, z_irq_spurious, NULL, ".isr_generated.0x3b");
_Z_ISR_TABLE_ENTRY(60, z_irq_spurious, NULL, ".isr_generated.0x3c");
_Z_ISR_TABLE_ENTRY(61, z_irq_spurious, NULL, ".isr_generated.0x3d");
_Z_ISR_TABLE_ENTRY(62, z_irq_spurious, NULL, ".isr_generated.0x3e");
_Z_ISR_TABLE_ENTRY(63, z_irq_spurious, NULL, ".isr_generated.0x3f");
_Z_ISR_TABLE_ENTRY(64, z_irq_spurious, NULL, ".isr_generated.0x40");
_Z_ISR_TABLE_ENTRY(65, z_irq_spurious, NULL, ".isr_generated.0x41");
_Z_ISR_TABLE_ENTRY(66, z_irq_spurious, NULL, ".isr_generated.0x42");
_Z_ISR_TABLE_ENTRY(67, z_irq_spurious, NULL, ".isr_generated.0x43");
_Z_ISR_TABLE_ENTRY(68, z_irq_spurious, NULL, ".isr_generated.0x44");
