# Available runners configured by board.cmake.
runners:
- nrfutil
- nrfjprog
- jlink

# Default flash runner if --runner is not given.
flash-runner: nrfutil

# Default debug runner if --runner is not given.
debug-runner: jlink

# Common runner configuration values.
config:
  board_dir: /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
  # Build outputs:
  elf_file: zephyr.elf
  hex_file: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/merged.hex
  bin_file: zephyr.bin
  # Host tools:
  gdb: /opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb
  openocd: /opt/homebrew/bin/openocd
  openocd_search:
    - /opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/sysroots/arm64-pokysdk-linux/usr/share/openocd/scripts

# Runner specific arguments
args:
  nrfutil:
    - --ext-mem-config-file=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/support/nrf7002dk_spi_nrfutil_config.json
  nrfjprog:
    []

  jlink:
    - --dt-flash=y
    - --device=nrf5340_xxaa_app
    - --speed=4000
