linker.cmd: \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/arch/arm/cortex_m/scripts/linker.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/zephyr/autoconf.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/sections.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/section_tags.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/toolchain.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/toolchain/gcc.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree.h \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/zephyr/devicetree_generated.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/irq_multilevel.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_macro.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_internal.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_loops.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_listify.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_internal_is_eq.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_internal_util_inc.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_internal_util_dec.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/sys/util_internal_util_x2.h \
 /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdbool.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/io-channels.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/clocks.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/gpio.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/spi.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/dma.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/pwms.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/fixed-partitions.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/ordinals.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/pinctrl.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/can.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/reset.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/mbox.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/devicetree/port-endpoint.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/devicetree_regions.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/linker-defs.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/toolchain/common.h \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/zephyr/offsets.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/linker-tool.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/linker-tool-gcc.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/kernel/mm.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/kernel/internal/mm.h \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/pm_config.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/linker-devnull.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/rel-sections.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-rom-start.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/common/rom_start_address.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/common/fill_with_zeros.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/common/rom_start_offset.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/arm/core/vector_table.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/irq-vector-table-section.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/arm/core/cortex_m/vector_table_pad.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/kobject-text.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-kernel-devices.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/iterable_sections.h \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/device-deps.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/device-api-sections.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-ztest.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-init.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-net.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-bt.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-logging.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-debug.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-interrupt-controllers.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-rom/common-rom-misc.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-rom-sections.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/thread-local-storage.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-rodata.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/arm/core/swi_tables.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/kobject-rom.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/cplusplus-rom.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-ram-sections.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../arch/common/ramfunc.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-ramfunc-section.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-rwdata.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/common-ram.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/kobject-data.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/cplusplus-ram.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-data-sections.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../subsys/net/conn_mgr/conn_mgr.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../subsys/net/l2/wifi/wifi_nm.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-sections.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/../soc/nordic/common/arm_platform_init.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/intlist.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/isr-local-drop-unused.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/debug-sections.ld \
 /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/include/generated/snippets-noinit.ld \
 /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/linker/ram-end.ld
