#define CONFIG_WIFI_UDP_LATENCY_UDP_PORT 12345
#define CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS 10000
#define CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS 500
#define CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX 1
#define CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP 1
#define CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID "wifi-latency-test"
#define CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK "testpass123"
#define CONFIG_WIFI 1
#define CONFIG_NET_L2_ETHERNET 1
#define CONFIG_UART_INTERRUPT_DRIVEN 1
#define CONFIG_NET_IPV6 1
#define CONFIG_FLASH_LOAD_SIZE 0x0
#define CONFIG_SRAM_SIZE 448
#define CONFIG_FLASH_LOAD_OFFSET 0x0
#define CONFIG_NUM_IRQS 69
#define CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC 32768
#define CONFIG_FLASH_SIZE 1024
#define CONFIG_FLASH_BASE_ADDRESS 0x0
#define CONFIG_MP_MAX_NUM_CPUS 1
#define CONFIG_SOC_RESET_HOOK 1
#define CONFIG_MAIN_STACK_SIZE 8192
#define CONFIG_IDLE_STACK_SIZE 320
#define CONFIG_ISR_STACK_SIZE 2048
#define CONFIG_CLOCK_CONTROL 1
#define CONFIG_SYS_CLOCK_TICKS_PER_SEC 32768
#define CONFIG_ROM_START_OFFSET 0x0
#define CONFIG_KERNEL_ENTRY "__start"
#define CONFIG_BUILD_OUTPUT_BIN 1
#define CONFIG_XIP 1
#define CONFIG_HAS_FLASH_LOAD_OFFSET 1
#define CONFIG_CPU_HAS_ARM_MPU 1
#define CONFIG_TICKLESS_KERNEL 1
#define CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE 6144
#define CONFIG_CLOCK_CONTROL_INIT_PRIORITY 30
#define CONFIG_PHY_INIT_PRIORITY 70
#define CONFIG_GEN_IRQ_VECTOR_TABLE 1
#define CONFIG_GEN_ISR_TABLES 1
#define CONFIG_INIT_STACKS 1
#define CONFIG_TIMESLICE_SIZE 0
#define CONFIG_SYS_CLOCK_EXISTS 1
#define CONFIG_INIT_ARCH_HW_AT_BOOT 1
#define CONFIG_FLASH_FILL_BUFFER_SIZE 32
#define CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT 1
#define CONFIG_HW_STACK_PROTECTION 1
#define CONFIG_NET_TCP_CHECKSUM 1
#define CONFIG_NET_UDP_CHECKSUM 1
#define CONFIG_GPIO 1
#define CONFIG_SERIAL 1
#define CONFIG_SPI 1
#define CONFIG_LOG 1
#define CONFIG_CMSIS_CORE_HAS_SYSTEM_CORE_CLOCK 1
#define CONFIG_DCACHE_LINE_SIZE 32
#define CONFIG_ARCH_SW_ISR_TABLE_ALIGN 4
#define CONFIG_NRF_RTC_TIMER 1
#define CONFIG_LOG_DOMAIN_NAME ""
#define CONFIG_SHELL_BACKEND_SERIAL 1
#define CONFIG_ASSERT 1
#define CONFIG_BUILD_OUTPUT_HEX 1
#define CONFIG_SOC_HAS_TIMING_FUNCTIONS 1
#define CONFIG_SERIAL_INIT_PRIORITY 50
#define CONFIG_ENTROPY_INIT_PRIORITY 50
#define CONFIG_CONSOLE 1
#define CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE -1
#define CONFIG_SOC_TOOLCHAIN_NAME "amd_acp_6_0_adsp"
#define CONFIG_GEN_SW_ISR_TABLE 1
#define CONFIG_FLASH_INIT_PRIORITY 50
#define CONFIG_GEN_IRQ_START_VECTOR 0
#define CONFIG_SRAM_OFFSET 0x0
#define CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN 4
#define CONFIG_SPI_NOR 1
#define CONFIG_ICACHE_LINE_SIZE 32
#define CONFIG_PRIVILEGED_STACK_SIZE 1024
#define CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED 1
#define CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED 1
#define CONFIG_DT_HAS_ARM_ARMV8M_ITM_ENABLED 1
#define CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED 1
#define CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED 1
#define CONFIG_DT_HAS_ARM_CRYPTOCELL_312_ENABLED 1
#define CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED 1
#define CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_KEYS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_LEDS_ENABLED 1
#define CONFIG_DT_HAS_JEDEC_SPI_NOR_ENABLED 1
#define CONFIG_DT_HAS_MMIO_SRAM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_PWM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_QSPI_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SAADC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SPIM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SPU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_TWIM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_USBD_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_HFXO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_LFXO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_OSCILLATORS_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53X_REGULATOR_HV_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53X_REGULATORS_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF5X_REGULATOR_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF7002_COEX_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF7002_QSPI_ENABLED 1
#define CONFIG_DT_HAS_PWM_LEDS_ENABLED 1
#define CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_BT_HCI_IPC_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED 1
#define CONFIG_NUM_METAIRQ_PRIORITIES 0
#define CONFIG_LOG_BUFFER_SIZE 1024
#define CONFIG_NET_IPV6_NBR_CACHE 1
#define CONFIG_NET_IPV6_MLD 1
#define CONFIG_NET_PKT_RX_COUNT 8
#define CONFIG_NET_PKT_TX_COUNT 8
#define CONFIG_MBEDTLS_ENTROPY_C 1
#define CONFIG_MBEDTLS_CIPHER_MODE_CBC 1
#define CONFIG_MBEDTLS_CIPHER_MODE_CTR 1
#define CONFIG_MBEDTLS_CHACHA20_C 1
#define CONFIG_MBEDTLS_POLY1305_C 1
#define CONFIG_MBEDTLS_CHACHAPOLY_C 1
#define CONFIG_MBEDTLS_DHM_C 1
#define CONFIG_MBEDTLS_SHA512_C 1
#define CONFIG_MBEDTLS_GCM_C 1
#define CONFIG_MBEDTLS_SSL_SRV_C 1
#define CONFIG_MBEDTLS_SSL_COOKIE_C 1
#define CONFIG_MBEDTLS_SSL_CLI_C 1
#define CONFIG_MBEDTLS_SSL_TLS_C 1
#define CONFIG_MBEDTLS_PSA_KEY_SLOT_COUNT 32
#define CONFIG_MPSL_WORK_STACK_SIZE 1024
#define CONFIG_NET_RX_STACK_SIZE 4096
#define CONFIG_NET_TX_STACK_SIZE 4096
#define CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN 16384
#define CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN 16384
#define CONFIG_MBEDTLS_KEY_EXCHANGE_PSK_ENABLED 1
#define CONFIG_ZMS 1
#define CONFIG_NVS 1
#define CONFIG_MBEDTLS_SHA1_C 1
#define CONFIG_MBEDTLS 1
#define CONFIG_MBEDTLS_MAC_SHA256_ENABLED 1
#define CONFIG_MBEDTLS_MD_C 1
#define CONFIG_MBEDTLS_TLS_LIBRARY 1
#define CONFIG_MBEDTLS_X509_LIBRARY 1
#define CONFIG_MBEDTLS_ECP_C 1
#define CONFIG_MBEDTLS_CTR_DRBG_C 1
#define CONFIG_MBEDTLS_CMAC_C 1
#define CONFIG_MBEDTLS_CCM_C 1
#define CONFIG_MBEDTLS_LEGACY_CRYPTO_C 1
#define CONFIG_WARN_EXPERIMENTAL 1
#define CONFIG_BT_BUF_CMD_TX_COUNT 10
#define CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE 4096
#define CONFIG_NRF_SECURITY_ENABLER 1
#define CONFIG_PM_PARTITION_SIZE_PROVISION 0x280
#define CONFIG_PM_PARTITION_SIZE_B0_IMAGE 0x8000
#define CONFIG_SB_VALIDATION_INFO_MAGIC 0x86518483
#define CONFIG_SB_VALIDATION_POINTER_MAGIC 0x6919b47e
#define CONFIG_SB_VALIDATION_INFO_CRYPTO_ID 1
#define CONFIG_SB_VALIDATION_INFO_VERSION 2
#define CONFIG_SB_VALIDATION_METADATA_OFFSET 0
#define CONFIG_SB_VALIDATION_STRUCT_HAS_HASH 1
#define CONFIG_SB_VALIDATION_STRUCT_HAS_PUBLIC_KEY 1
#define CONFIG_SB_VALIDATE_FW_SIGNATURE 1
#define CONFIG_SYSTEM_WORKQUEUE_PRIORITY -1
#define CONFIG_HEAP_MEM_POOL_SIZE 0
#define CONFIG_PCD_VERSION_PAGE_BUF_SIZE 2046
#define CONFIG_WIFI_READY_LIB 1
#define CONFIG_WIFI_READY_LIB_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_READY_LIB_LOG_LEVEL 3
#define CONFIG_WIFI_READY_MAX_CALLBACKS 2
#define CONFIG_WIFI_READY_INIT_PRIORITY 90
#define CONFIG_NRF_WIFI_PATCHES_EXT_FLASH_DISABLED 1
#define CONFIG_CUSTOM_LINKER_SCRIPT "/opt/nordic/ncs/v3.0.2/zephyr/../nrf/subsys/net/lib/nrf70_fw_ext/rpu_fw_patches.ld"
#define CONFIG_HOSTAP_CRYPTO_ALT_LEGACY_PSA 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_WPA3 1
#define CONFIG_L2_WIFI_CONNECTIVITY 1
#define CONFIG_L2_WIFI_CONN_WQ_STACK_SIZE 6144
#define CONFIG_L2_WIFI_CONNECTIVITY_CONNECT_TIMEOUT_SECONDS 0
#define CONFIG_L2_WIFI_CONNECTIVITY_CONNECTION_PERSISTENCE 1
#define CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPSL_FEM_LOG_LEVEL 3
#define CONFIG_MPSL_CX_ANY_SUPPORT 1
#define CONFIG_MPSL_CX_NRF70_SUPPORT 1
#define CONFIG_MPSL_THREAD_COOP_PRIO 8
#define CONFIG_MPSL_TIMESLOT_SESSION_COUNT 0
#define CONFIG_MPSL_LOW_PRIO_IRQN 26
#define CONFIG_MPSL_HFCLK_LATENCY 1400
#define CONFIG_MPSL_INIT_PRIORITY 40
#define CONFIG_MPSL_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPSL_LOG_LEVEL 3
#define CONFIG_PARTITION_MANAGER_ENABLED 1
#define CONFIG_FLASH_MAP_CUSTOM 1
#define CONFIG_SRAM_BASE_ADDRESS 0x20000000
#define CONFIG_NRF_TRUSTZONE_FLASH_REGION_SIZE 0x4000
#define CONFIG_NRF_TRUSTZONE_RAM_REGION_SIZE 0x2000
#define CONFIG_RPMSG_NRF53_SRAM_SIZE 0x10000
#define CONFIG_PM_PARTITION_SIZE_LITTLEFS 0x6000
#define CONFIG_PM_PARTITION_SIZE_SETTINGS_STORAGE 0x2000
#define CONFIG_PM_PARTITION_ALIGN_SETTINGS_STORAGE 0x4000
#define CONFIG_PM_PARTITION_SIZE_NVS_STORAGE 0x6000
#define CONFIG_PM_EXTERNAL_FLASH_HAS_DRIVER 1
#define CONFIG_PM_EXTERNAL_FLASH_BASE 0x0
#define CONFIG_PM_EXTERNAL_FLASH_PATH ""
#define CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS 0
#define CONFIG_PM_SRAM_BASE 0x20000000
#define CONFIG_PM_SRAM_SIZE 0x80000
#define CONFIG_NRF_SECURITY 1
#define CONFIG_PSA_HAS_AEAD_SUPPORT 1
#define CONFIG_PSA_HAS_ASYM_SIGN_SUPPORT 1
#define CONFIG_PSA_HAS_CIPHER_SUPPORT 1
#define CONFIG_PSA_HAS_HASH_SUPPORT 1
#define CONFIG_PSA_HAS_KEY_AGREEMENT 1
#define CONFIG_PSA_HAS_KEY_DERIVATION 1
#define CONFIG_PSA_HAS_KEY_SUPPORT 1
#define CONFIG_PSA_HAS_MAC_SUPPORT 1
#define CONFIG_PSA_WANT_ALG_ECB_NO_PADDING 1
#define CONFIG_PSA_WANT_ALG_CHACHA20 1
#define CONFIG_PSA_WANT_GENERATE_RANDOM 1
#define CONFIG_MBEDTLS_CFG_FILE "nrf-config.h"
#define CONFIG_MBEDTLS_PSA_CRYPTO_CONFIG 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_CONFIG_FILE "nrf-psa-crypto-config.h"
#define CONFIG_MBEDTLS_PSA_CRYPTO_USER_CONFIG_FILE "nrf-psa-crypto-user-config.h"
#define CONFIG_MBEDTLS_X509_USE_C 1
#define CONFIG_MBEDTLS_X509_CHECK_KEY_USAGE 1
#define CONFIG_MBEDTLS_X509_CHECK_EXTENDED_KEY_USAGE 1
#define CONFIG_MBEDTLS_X509_CRL_PARSE_C 1
#define CONFIG_MBEDTLS_X509_CSR_PARSE_C 1
#define CONFIG_MBEDTLS_X509_CRT_PARSE_C 1
#define CONFIG_MBEDTLS_X509_REMOVE_INFO 1
#define CONFIG_MBEDTLS_SSL_PROTO_TLS1_2 1
#define CONFIG_MBEDTLS_SSL_ENCRYPT_THEN_MAC 1
#define CONFIG_MBEDTLS_SSL_EXTENDED_MASTER_SECRET 1
#define CONFIG_MBEDTLS_SSL_ALL_ALERT_MESSAGES 1
#define CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION 1
#define CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE 1
#define CONFIG_MBEDTLS_SSL_EXPORT_KEYS 1
#define CONFIG_MBEDTLS_SSL_CIPHERSUITES ""
#define CONFIG_MBEDTLS_HAS_CBC_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_GCM_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_CCM_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_CHACHAPOLY_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_CIPHER_MODE_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_ECDH_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_ECDSA_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_HAS_ECJPAKE_CIPHERSUITE_REQUIREMENTS 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_C 1
#define CONFIG_PSA_CORE_OBERON 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_DRIVERS 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_CLIENT 1
#define CONFIG_PSA_CRYPTO_DRIVER_OBERON 1
#define CONFIG_PSA_WANT_ALG_CTR_DRBG 1
#define CONFIG_PSA_USE_CTR_DRBG_DRIVER 1
#define CONFIG_PSA_USE_CC3XX_CTR_DRBG_DRIVER 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_128 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_192 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_256 1
#define CONFIG_PSA_MAX_RSA_KEY_BITS 0
#define CONFIG_PSA_ACCEL_GENERATE_RANDOM 1
#define CONFIG_PSA_NEED_CC3XX_CTR_DRBG_DRIVER 1
#define CONFIG_CRACEN_LOG_LEVEL_DEFAULT 1
#define CONFIG_CRACEN_LOG_LEVEL 3
#define CONFIG_PSA_NEED_OBERON_CCM_AES 1
#define CONFIG_PSA_NEED_OBERON_GCM_AES 1
#define CONFIG_PSA_NEED_OBERON_CHACHA20_POLY1305 1
#define CONFIG_PSA_NEED_OBERON_AEAD_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_CTR_AES 1
#define CONFIG_PSA_NEED_OBERON_CBC_NO_PADDING_AES 1
#define CONFIG_PSA_NEED_OBERON_CBC_PKCS7_AES 1
#define CONFIG_PSA_NEED_OBERON_ECB_NO_PADDING_AES 1
#define CONFIG_PSA_NEED_OBERON_STREAM_CIPHER_CHACHA20 1
#define CONFIG_PSA_NEED_OBERON_CIPHER_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_ECDH_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_ECDH 1
#define CONFIG_PSA_NEED_OBERON_KEY_AGREEMENT_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_ECDSA_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_ECDSA_VERIFY 1
#define CONFIG_PSA_NEED_OBERON_ECDSA_SIGN 1
#define CONFIG_PSA_NEED_OBERON_ECDSA_DETERMINISTIC 1
#define CONFIG_PSA_NEED_OBERON_ECDSA_RANDOMIZED 1
#define CONFIG_PSA_NEED_OBERON_SHA_1 1
#define CONFIG_PSA_NEED_OBERON_SHA_224 1
#define CONFIG_PSA_NEED_OBERON_SHA_256 1
#define CONFIG_PSA_NEED_OBERON_SHA_384 1
#define CONFIG_PSA_NEED_OBERON_SHA_512 1
#define CONFIG_PSA_NEED_OBERON_HASH_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP_R1_256 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT 1
#define CONFIG_PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE 1
#define CONFIG_PSA_NEED_OBERON_KEY_MANAGEMENT_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_HMAC 1
#define CONFIG_PSA_NEED_OBERON_CMAC 1
#define CONFIG_PSA_NEED_OBERON_MAC_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_HKDF 1
#define CONFIG_PSA_NEED_OBERON_TLS12_PRF 1
#define CONFIG_PSA_NEED_OBERON_TLS12_PSK_TO_MS 1
#define CONFIG_PSA_NEED_OBERON_PBKDF2_AES_CMAC_PRF_128 1
#define CONFIG_PSA_NEED_OBERON_TLS12_ECJPAKE_TO_PMS 1
#define CONFIG_PSA_NEED_OBERON_KEY_DERIVATION_DRIVER 1
#define CONFIG_PSA_NEED_OBERON_ASYMMETRIC_SIGNATURE_DRIVER 1
#define CONFIG_MBEDTLS_PLATFORM_MEMORY 1
#define CONFIG_MBEDTLS_PLATFORM_C 1
#define CONFIG_MBEDTLS_MEMORY_C 1
#define CONFIG_MBEDTLS_MEMORY_BUFFER_ALLOC_C 1
#define CONFIG_MBEDTLS_THREADING_C 1
#define CONFIG_MBEDTLS_BASE64_C 1
#define CONFIG_MBEDTLS_OID_C 1
#define CONFIG_MBEDTLS_ENTROPY_HARDWARE_ALT 1
#define CONFIG_MBEDTLS_THREADING_ALT 1
#define CONFIG_MBEDTLS_AES_SETKEY_ENC_ALT 1
#define CONFIG_MBEDTLS_AES_SETKEY_DEC_ALT 1
#define CONFIG_MBEDTLS_AES_ENCRYPT_ALT 1
#define CONFIG_MBEDTLS_AES_DECRYPT_ALT 1
#define CONFIG_MBEDTLS_CHACHA20_ALT 1
#define CONFIG_MBEDTLS_POLY1305_ALT 1
#define CONFIG_MBEDTLS_ECDH_GEN_PUBLIC_ALT 1
#define CONFIG_MBEDTLS_ECDH_COMPUTE_SHARED_ALT 1
#define CONFIG_MBEDTLS_ECDSA_GENKEY_ALT 1
#define CONFIG_MBEDTLS_ECDSA_SIGN_ALT 1
#define CONFIG_MBEDTLS_ECDSA_VERIFY_ALT 1
#define CONFIG_MBEDTLS_ECJPAKE_ALT 1
#define CONFIG_MBEDTLS_SHA1_ALT 1
#define CONFIG_MBEDTLS_SHA224_ALT 1
#define CONFIG_MBEDTLS_SHA256_ALT 1
#define CONFIG_MBEDTLS_ENTROPY_FORCE_SHA256 1
#define CONFIG_MBEDTLS_ENTROPY_MAX_SOURCES 1
#define CONFIG_MBEDTLS_NO_PLATFORM_ENTROPY 1
#define CONFIG_OBERON_ONLY_PSA_ENABLED 1
#define CONFIG_OBERON_ONLY_ENABLED 1
#define CONFIG_MBEDTLS_MPI_WINDOW_SIZE 6
#define CONFIG_MBEDTLS_MPI_MAX_SIZE 256
#define CONFIG_MBEDTLS_HMAC_DRBG_C 1
#define CONFIG_MBEDTLS_AES_C 1
#define CONFIG_MBEDTLS_CIPHER_PADDING_PKCS7 1
#define CONFIG_MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS 1
#define CONFIG_MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN 1
#define CONFIG_MBEDTLS_CIPHER_PADDING_ZEROS 1
#define CONFIG_MBEDTLS_ECDH_C 1
#define CONFIG_MBEDTLS_ECDSA_C 1
#define CONFIG_MBEDTLS_ECDSA_DETERMINISTIC 1
#define CONFIG_MBEDTLS_ECJPAKE_C 1
#define CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED 1
#define CONFIG_MBEDTLS_HKDF_C 1
#define CONFIG_MBEDTLS_MD5_C 1
#define CONFIG_MBEDTLS_SHA224_C 1
#define CONFIG_MBEDTLS_SHA256_C 1
#define CONFIG_MBEDTLS_SHA384_C 1
#define CONFIG_MBEDTLS_CIPHER_C 1
#define CONFIG_MBEDTLS_PK_C 1
#define CONFIG_MBEDTLS_PKCS5_C 1
#define CONFIG_MBEDTLS_PK_WRITE_C 1
#define CONFIG_MBEDTLS_PK_PARSE_C 1
#define CONFIG_MBEDTLS_PK_PARSE_EC_EXTENDED 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ALL_ENABLED 1
#define CONFIG_MBEDTLS_LIBRARY_NRF_SECURITY 1
#define CONFIG_MCUBOOT_APPLICATION_IMAGE_NUMBER -1
#define CONFIG_MCUBOOT_NETWORK_CORE_IMAGE_NUMBER -1
#define CONFIG_MCUBOOT_WIFI_PATCHES_IMAGE_NUMBER -1
#define CONFIG_MCUBOOT_QSPI_XIP_IMAGE_NUMBER -1
#define CONFIG_MCUBOOT_MCUBOOT_IMAGE_NUMBER -1
#define CONFIG_SETTINGS_ZMS_SECTOR_SIZE_MULT 1
#define CONFIG_WFA_QT_LOG_LEVEL_DEFAULT 1
#define CONFIG_WFA_QT_LOG_LEVEL 3
#define CONFIG_WFA_QT_THREAD_STACK_SIZE 5200
#define CONFIG_WFA_QT_REBOOT_TIMEOUT_MS 1000
#define CONFIG_WFA_QT_DEFAULT_INTERFACE "wlan0"
#define CONFIG_WPAS_READY_TIMEOUT_MS 10000
#define CONFIG_NET_CONNECTION_MANAGER_MONITOR_STACK_SIZE 512
#define CONFIG_NRF_SPU_FLASH_REGION_SIZE 0x4000
#define CONFIG_FPROTECT_BLOCK_SIZE 0x4000
#define CONFIG_DK_LIBRARY 1
#define CONFIG_DK_LIBRARY_BUTTON_SCAN_INTERVAL 10
#define CONFIG_DK_LIBRARY_DYNAMIC_BUTTON_HANDLERS 1
#define CONFIG_DK_LIBRARY_LOG_LEVEL_DEFAULT 1
#define CONFIG_DK_LIBRARY_LOG_LEVEL 3
#define CONFIG_HW_UNIQUE_KEY_SUPPORTED 1
#define CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE 0x0
#define CONFIG_NCS_BOOT_BANNER 1
#define CONFIG_NCS_NCS_BOOT_BANNER_STRING "nRF Connect SDK"
#define CONFIG_NCS_ZEPHYR_BOOT_BANNER_STRING "Zephyr OS"
#define CONFIG_ENTROPY_CC3XX 1
#define CONFIG_HW_CC3XX 1
#define CONFIG_SOC_FLASH_NRF_RADIO_SYNC_MPSL_TIMESLOT_SESSION_COUNT 0
#define CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS 1
#define CONFIG_ZEPHYR_NRF_MODULE 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT 1
#define CONFIG_HEAP_MEM_POOL_ADD_SIZE_HOSTAP 41808
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_THREAD_STACK_SIZE 5800
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_WQ_STACK_SIZE 4400
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_WQ_PRIO 7
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_PRIO 0
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_LOG_LEVEL 3
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_DEBUG_LEVEL 5
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_CRYPTO_EXT 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_AP 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_INF_MON 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_BSS_MAX_IDLE_TIME 300
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_11AX 1
#define CONFIG_SME 1
#define CONFIG_NO_CONFIG_WRITE 1
#define CONFIG_NO_CONFIG_BLOBS 1
#define CONFIG_CTRL_IFACE 1
#define CONFIG_CTRL_IFACE_ZEPHYR 1
#define CONFIG_NO_RANDOM_POOL 1
#define CONFIG_AP 1
#define CONFIG_WIFI_NM_WPA_SUPPLICANT_NW_SEL_THROUGHPUT 1
#define CONFIG_WIFI_NM_WPA_CTRL_RESP_TIMEOUT_S 15
#define CONFIG_ZEPHYR_HOSTAP_MODULE 1
#define CONFIG_DT_FLASH_WRITE_BLOCK_SIZE 4
#define CONFIG_MCUBOOT_USB_SUPPORT 1
#define CONFIG_ZEPHYR_MCUBOOT_MODULE 1
#define CONFIG_ZEPHYR_MBEDTLS_MODULE 1
#define CONFIG_PSA_CRYPTO_CLIENT 1
#define CONFIG_PSA_WANT_ALG_CBC_NO_PADDING 1
#define CONFIG_PSA_WANT_ALG_CBC_PKCS7 1
#define CONFIG_PSA_WANT_ALG_CCM 1
#define CONFIG_PSA_WANT_ALG_CMAC 1
#define CONFIG_PSA_WANT_ALG_CHACHA20_POLY1305 1
#define CONFIG_PSA_WANT_ALG_CTR 1
#define CONFIG_PSA_WANT_ALG_DETERMINISTIC_ECDSA 1
#define CONFIG_PSA_WANT_ALG_ECDH 1
#define CONFIG_PSA_WANT_ALG_ECDSA 1
#define CONFIG_PSA_WANT_ALG_GCM 1
#define CONFIG_PSA_WANT_ALG_HKDF 1
#define CONFIG_PSA_WANT_ALG_HMAC 1
#define CONFIG_PSA_WANT_ALG_SHA_1 1
#define CONFIG_PSA_WANT_ALG_SHA_224 1
#define CONFIG_PSA_WANT_ALG_SHA_256 1
#define CONFIG_PSA_WANT_ALG_SHA_384 1
#define CONFIG_PSA_WANT_ALG_SHA_512 1
#define CONFIG_PSA_WANT_ALG_STREAM_CIPHER 1
#define CONFIG_PSA_WANT_ALG_TLS12_PRF 1
#define CONFIG_PSA_WANT_ALG_TLS12_PSK_TO_MS 1
#define CONFIG_PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS 1
#define CONFIG_PSA_WANT_ECC_SECP_R1_256 1
#define CONFIG_PSA_WANT_KEY_TYPE_AES 1
#define CONFIG_PSA_WANT_KEY_TYPE_CHACHA20 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE 1
#define CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC 1
#define CONFIG_DISABLE_MBEDTLS_BUILTIN 1
#define CONFIG_MBEDTLS_LOG_LEVEL_DEFAULT 1
#define CONFIG_MBEDTLS_LOG_LEVEL 3
#define CONFIG_MBEDTLS_INIT 1
#define CONFIG_APP_LINK_WITH_MBEDTLS 1
#define CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE 1
#define CONFIG_TFM_BOARD "/opt/nordic/ncs/v3.0.2/zephyr/modules/trusted-firmware-m/nordic/nrf5340_cpuapp"
#define CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE 1
#define CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE 1
#define CONFIG_ZEPHYR_CJSON_MODULE 1
#define CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE 1
#define CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE 1
#define CONFIG_ZEPHYR_OPENTHREAD_MODULE 1
#define CONFIG_SUIT_ENVELOPE_TEMPLATE_FILENAME ""
#define CONFIG_SUIT_ENVELOPE_TARGET ""
#define CONFIG_SUIT_ENVELOPE_OUTPUT_ARTIFACT "merged.hex"
#define CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE 1
#define CONFIG_SUIT_PLATFORM_DRY_RUN_SUPPORT 1
#define CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE 1
#define CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE 1
#define CONFIG_ZEPHYR_COREMARK_MODULE 1
#define CONFIG_ZEPHYR_CANOPENNODE_MODULE 1
#define CONFIG_ZEPHYR_CHRE_MODULE 1
#define CONFIG_ZEPHYR_LZ4_MODULE 1
#define CONFIG_ZEPHYR_NANOPB_MODULE 1
#define CONFIG_ZEPHYR_TF_M_TESTS_MODULE 1
#define CONFIG_ZEPHYR_ZSCILIB_MODULE 1
#define CONFIG_ZEPHYR_CMSIS_MODULE 1
#define CONFIG_HAS_CMSIS_CORE 1
#define CONFIG_HAS_CMSIS_CORE_M 1
#define CONFIG_ZEPHYR_CMSIS_DSP_MODULE 1
#define CONFIG_ZEPHYR_CMSIS_NN_MODULE 1
#define CONFIG_ZEPHYR_FATFS_MODULE 1
#define CONFIG_ZEPHYR_HAL_NORDIC_MODULE 1
#define CONFIG_HAS_NORDIC_DRIVERS 1
#define CONFIG_HAS_NRFX 1
#define CONFIG_NRFX_CLOCK 1
#define CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED 1
#define CONFIG_NRFX_DPPI 1
#define CONFIG_NRFX_DPPI0 1
#define CONFIG_NRFX_GPIOTE 1
#define CONFIG_NRFX_GPIOTE0 1
#define CONFIG_NRFX_GPPI 1
#define CONFIG_NRFX_NVMC 1
#define CONFIG_NRFX_QSPI 1
#define CONFIG_NRFX_SPIM 1
#define CONFIG_NRFX_SPIM4 1
#define CONFIG_NRFX_RESERVED_RESOURCES_HEADER "nrfx_config_reserved_resources_ncs.h"
#define CONFIG_ZEPHYR_HAL_NXP_MODULE 1
#define CONFIG_ZEPHYR_HAL_ST_MODULE 1
#define CONFIG_ZEPHYR_HAL_STM32_MODULE 1
#define CONFIG_ZEPHYR_HAL_TDK_MODULE 1
#define CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE 1
#define CONFIG_ZEPHYR_LIBLC3_MODULE 1
#define CONFIG_ZEPHYR_LIBMETAL_MODULE 1
#define CONFIG_ZEPHYR_LITTLEFS_MODULE 1
#define CONFIG_ZEPHYR_LORAMAC_NODE_MODULE 1
#define CONFIG_ZEPHYR_LVGL_MODULE 1
#define CONFIG_ZEPHYR_MIPI_SYS_T_MODULE 1
#define CONFIG_ZEPHYR_NRF_WIFI_MODULE 1
#define CONFIG_NRF70_BUSLIB 1
#define CONFIG_NRF70_ON_QSPI 1
#define CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_NRF70_BUSLIB_LOG_LEVEL 3
#define CONFIG_NRF70_LOG_VERBOSE 1
#define CONFIG_ZEPHYR_OPEN_AMP_MODULE 1
#define CONFIG_ZEPHYR_PERCEPIO_MODULE 1
#define CONFIG_ZEPHYR_PICOLIBC_MODULE 1
#define CONFIG_ZEPHYR_SEGGER_MODULE 1
#define CONFIG_HAS_SEGGER_RTT 1
#define CONFIG_ZEPHYR_TINYCRYPT_MODULE 1
#define CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE 1
#define CONFIG_ZEPHYR_ZCBOR_MODULE 1
#define CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE 0x4e8
#define CONFIG_NRFXLIB_CRYPTO 1
#define CONFIG_HAS_HW_NRF_CC3XX 1
#define CONFIG_NRF_CC3XX_PLATFORM 1
#define CONFIG_CC3XX_MUTEX_LOCK 1
#define CONFIG_NRF_802154_SOURCE_NRFXLIB 1
#define CONFIG_ZEPHYR_NRFXLIB_MODULE 1
#define CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE 1
#define CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE 1
#define CONFIG_BOARD "nrf7002dk"
#define CONFIG_BOARD_REVISION ""
#define CONFIG_BOARD_TARGET "nrf7002dk/nrf5340/cpuapp"
#define CONFIG_BOARD_NRF7002DK 1
#define CONFIG_BOARD_NRF7002DK_NRF5340_CPUAPP 1
#define CONFIG_BOARD_QUALIFIERS "nrf5340/cpuapp"
#define CONFIG_DOMAIN_CPUNET_BOARD "nrf7002dk/nrf5340/cpunet"
#define CONFIG_SOC "nrf5340"
#define CONFIG_SOC_SERIES "nrf53"
#define CONFIG_SOC_FAMILY "nordic_nrf"
#define CONFIG_SOC_FAMILY_NORDIC_NRF 1
#define CONFIG_SOC_SERIES_NRF53X 1
#define CONFIG_SOC_NRF5340_CPUAPP 1
#define CONFIG_SOC_NRF5340_CPUAPP_QKAA 1
#define CONFIG_HAS_HW_NRF_CC312 1
#define CONFIG_HAS_HW_NRF_CLOCK 1
#define CONFIG_HAS_HW_NRF_CTRLAP 1
#define CONFIG_HAS_HW_NRF_DCNF 1
#define CONFIG_HAS_HW_NRF_DPPIC 1
#define CONFIG_HAS_HW_NRF_EGU0 1
#define CONFIG_HAS_HW_NRF_EGU1 1
#define CONFIG_HAS_HW_NRF_EGU2 1
#define CONFIG_HAS_HW_NRF_EGU3 1
#define CONFIG_HAS_HW_NRF_EGU4 1
#define CONFIG_HAS_HW_NRF_EGU5 1
#define CONFIG_HAS_HW_NRF_GPIO0 1
#define CONFIG_HAS_HW_NRF_GPIO1 1
#define CONFIG_HAS_HW_NRF_GPIOTE0 1
#define CONFIG_HAS_HW_NRF_KMU 1
#define CONFIG_HAS_HW_NRF_MUTEX 1
#define CONFIG_HAS_HW_NRF_NVMC_PE 1
#define CONFIG_HAS_HW_NRF_POWER 1
#define CONFIG_HAS_HW_NRF_PWM0 1
#define CONFIG_HAS_HW_NRF_QSPI 1
#define CONFIG_HAS_HW_NRF_RESET 1
#define CONFIG_HAS_HW_NRF_SAADC 1
#define CONFIG_HAS_HW_NRF_SPIM4 1
#define CONFIG_HAS_HW_NRF_SPU 1
#define CONFIG_HAS_HW_NRF_TWIM1 1
#define CONFIG_HAS_HW_NRF_UARTE0 1
#define CONFIG_HAS_HW_NRF_USBD 1
#define CONFIG_HAS_HW_NRF_USBREG 1
#define CONFIG_HAS_HW_NRF_VMC 1
#define CONFIG_HAS_HW_NRF_WDT0 1
#define CONFIG_HAS_NORDIC_RAM_CTRL 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND 1
#define CONFIG_SOC_NRF53_ANOMALY_168_WORKAROUND 1
#define CONFIG_SOC_NRF53_RTC_PRETICK 1
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET 10
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET 11
#define CONFIG_NRF_SPU_RAM_REGION_SIZE 0x2000
#define CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340 1
#define CONFIG_SOC_NRF53_CPUNET_MGMT 1
#define CONFIG_SOC_HFXO_CAP_DEFAULT 1
#define CONFIG_NRF_ENABLE_CACHE 1
#define CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT 0
#define CONFIG_NRF_SOC_SECURE_SUPPORTED 1
#define CONFIG_NRF_APPROTECT_USE_UICR 1
#define CONFIG_NRF_SECURE_APPROTECT_USE_UICR 1
#define CONFIG_GPIO_INIT_PRIORITY 40
#define CONFIG_SOC_LOG_LEVEL_DEFAULT 1
#define CONFIG_SOC_LOG_LEVEL 3
#define CONFIG_SOC_COMPATIBLE_NRF 1
#define CONFIG_SOC_COMPATIBLE_NRF53X 1
#define CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP 1
#define CONFIG_ARCH "arm"
#define CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT 1
#define CONFIG_CPU_CORTEX 1
#define CONFIG_KOBJECT_TEXT_AREA 256
#define CONFIG_ARM_MPU 1
#define CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE 32
#define CONFIG_MPU_ALLOW_FLASH_WRITE 1
#define CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK 1
#define CONFIG_ARM_ON_EXIT_CPU_IDLE 1
#define CONFIG_CPU_CORTEX_M 1
#define CONFIG_ISA_THUMB2 1
#define CONFIG_ASSEMBLER_ISA_THUMB2 1
#define CONFIG_COMPILER_ISA_THUMB2 1
#define CONFIG_STACK_ALIGN_DOUBLE_WORD 1
#define CONFIG_FAULT_DUMP 2
#define CONFIG_BUILTIN_STACK_GUARD 1
#define CONFIG_ARM_STACK_PROTECTION 1
#define CONFIG_FP16 1
#define CONFIG_FP16_IEEE 1
#define CONFIG_CPU_CORTEX_M33 1
#define CONFIG_CPU_CORTEX_M_HAS_SYSTICK 1
#define CONFIG_CPU_CORTEX_M_HAS_DWT 1
#define CONFIG_CPU_CORTEX_M_HAS_BASEPRI 1
#define CONFIG_CPU_CORTEX_M_HAS_VTOR 1
#define CONFIG_CPU_CORTEX_M_HAS_SPLIM 1
#define CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS 1
#define CONFIG_CPU_CORTEX_M_HAS_CMSE 1
#define CONFIG_ARMV7_M_ARMV8_M_MAINLINE 1
#define CONFIG_ARMV8_M_MAINLINE 1
#define CONFIG_ARMV8_M_SE 1
#define CONFIG_ARMV7_M_ARMV8_M_FP 1
#define CONFIG_ARMV8_M_DSP 1
#define CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE 1
#define CONFIG_ARM_TRUSTZONE_M 1
#define CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE 32
#define CONFIG_CPU_HAS_NRF_IDAU 1
#define CONFIG_HAS_SWO 1
#define CONFIG_ARM 1
#define CONFIG_ARCH_IS_SET 1
#define CONFIG_ARCH_LOG_LEVEL_DEFAULT 1
#define CONFIG_ARCH_LOG_LEVEL 3
#define CONFIG_LITTLE_ENDIAN 1
#define CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT 100
#define CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES 16
#define CONFIG_GEN_PRIV_STACKS 1
#define CONFIG_ISR_TABLES_LOCAL_DECLARATION_SUPPORTED 1
#define CONFIG_ISR_TABLES_LOCAL_DECLARATION 1
#define CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS 1
#define CONFIG_EXCEPTION_DEBUG 1
#define CONFIG_ARCH_HAS_TIMING_FUNCTIONS 1
#define CONFIG_ARCH_HAS_TRUSTED_EXECUTION 1
#define CONFIG_ARCH_HAS_STACK_PROTECTION 1
#define CONFIG_ARCH_HAS_USERSPACE 1
#define CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT 1
#define CONFIG_ARCH_HAS_RAMFUNC_SUPPORT 1
#define CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION 1
#define CONFIG_ARCH_SUPPORTS_COREDUMP 1
#define CONFIG_ARCH_SUPPORTS_COREDUMP_THREADS 1
#define CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT 1
#define CONFIG_ARCH_SUPPORTS_ROM_START 1
#define CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO 1
#define CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE 1
#define CONFIG_ARCH_HAS_SUSPEND_TO_RAM 1
#define CONFIG_ARCH_HAS_THREAD_ABORT 1
#define CONFIG_ARCH_HAS_CODE_DATA_RELOCATION 1
#define CONFIG_CPU_HAS_TEE 1
#define CONFIG_CPU_HAS_FPU 1
#define CONFIG_CPU_HAS_MPU 1
#define CONFIG_MPU 1
#define CONFIG_MPU_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPU_LOG_LEVEL 3
#define CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS 1
#define CONFIG_MPU_GAP_FILLING 1
#define CONFIG_SRAM_REGION_PERMISSIONS 1
#define CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS 1
#define CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN 1
#define CONFIG_KERNEL_LOG_LEVEL_DEFAULT 1
#define CONFIG_KERNEL_LOG_LEVEL 3
#define CONFIG_MULTITHREADING 1
#define CONFIG_NUM_COOP_PRIORITIES 16
#define CONFIG_NUM_PREEMPT_PRIORITIES 15
#define CONFIG_MAIN_THREAD_PRIORITY 0
#define CONFIG_COOP_ENABLED 1
#define CONFIG_PREEMPT_ENABLED 1
#define CONFIG_PRIORITY_CEILING -127
#define CONFIG_THREAD_STACK_INFO 1
#define CONFIG_SCHED_SIMPLE 1
#define CONFIG_WAITQ_SIMPLE 1
#define CONFIG_LIBC_ERRNO 1
#define CONFIG_ERRNO 1
#define CONFIG_CURRENT_THREAD_USE_TLS 1
#define CONFIG_BOOT_DELAY 0
#define CONFIG_THREAD_MONITOR 1
#define CONFIG_THREAD_NAME 1
#define CONFIG_THREAD_MAX_NAME_LEN 32
#define CONFIG_BARRIER_OPERATIONS_ARCH 1
#define CONFIG_ATOMIC_OPERATIONS_BUILTIN 1
#define CONFIG_TIMESLICING 1
#define CONFIG_TIMESLICE_PRIORITY 0
#define CONFIG_POLL 1
#define CONFIG_MEM_SLAB_POINTER_VALIDATE 1
#define CONFIG_NUM_MBOX_ASYNC_MSGS 10
#define CONFIG_PIPES 1
#define CONFIG_KERNEL_MEM_POOL 1
#define CONFIG_SWAP_NONATOMIC 1
#define CONFIG_TIMEOUT_64BIT 1
#define CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS 365
#define CONFIG_STACK_POINTER_RANDOM 0
#define CONFIG_ARCH_MEM_DOMAIN_SUPPORTS_ISOLATED_STACKS 1
#define CONFIG_MEM_DOMAIN_ISOLATED_STACKS 1
#define CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_THREAD_LOCAL_STORAGE 1
#define CONFIG_TOOLCHAIN_SUPPORTS_STATIC_INIT_GNU 1
#define CONFIG_DEVICE_DT_METADATA 1
#define CONFIG_KERNEL_INIT_PRIORITY_OBJECTS 30
#define CONFIG_KERNEL_INIT_PRIORITY_LIBC 35
#define CONFIG_KERNEL_INIT_PRIORITY_DEFAULT 40
#define CONFIG_KERNEL_INIT_PRIORITY_DEVICE 50
#define CONFIG_APPLICATION_INIT_PRIORITY 90
#define CONFIG_FLASH 1
#define CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT 1
#define CONFIG_CLOCK_CONTROL_LOG_LEVEL 3
#define CONFIG_CLOCK_CONTROL_NRF 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM 1
#define CONFIG_CLOCK_CONTROL_NRF_ACCURACY 50
#define CONFIG_CONSOLE_INPUT_MAX_LINE_LEN 128
#define CONFIG_CONSOLE_HAS_DRIVER 1
#define CONFIG_CONSOLE_INIT_PRIORITY 60
#define CONFIG_UART_CONSOLE 1
#define CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT 1
#define CONFIG_UART_CONSOLE_LOG_LEVEL 3
#define CONFIG_WINSTREAM_CONSOLE_STATIC 1
#define CONFIG_WINSTREAM_CONSOLE_STATIC_SIZE 32768
#define CONFIG_ENTROPY_GENERATOR 1
#define CONFIG_ENTROPY_LOG_LEVEL_DEFAULT 1
#define CONFIG_ENTROPY_LOG_LEVEL 3
#define CONFIG_ENTROPY_HAS_DRIVER 1
#define CONFIG_ETH_DRIVER 1
#define CONFIG_ETHERNET_LOG_LEVEL_DEFAULT 1
#define CONFIG_ETHERNET_LOG_LEVEL 3
#define CONFIG_ETH_PHY_DRIVER 1
#define CONFIG_PHY_LOG_LEVEL_DEFAULT 1
#define CONFIG_PHY_LOG_LEVEL 3
#define CONFIG_PHY_AUTONEG_TIMEOUT_MS 4000
#define CONFIG_PHY_MONITOR_PERIOD 500
#define CONFIG_ETH_INIT_PRIORITY 80
#define CONFIG_FLASH_HAS_DRIVER_ENABLED 1
#define CONFIG_FLASH_HAS_EX_OP 1
#define CONFIG_FLASH_HAS_EXPLICIT_ERASE 1
#define CONFIG_FLASH_HAS_PAGE_LAYOUT 1
#define CONFIG_FLASH_JESD216 1
#define CONFIG_FLASH_PAGE_LAYOUT 1
#define CONFIG_SPI_NOR_SFDP_MINIMAL 1
#define CONFIG_SPI_NOR_INIT_PRIORITY 80
#define CONFIG_SPI_NOR_CS_WAIT_DELAY 0
#define CONFIG_SPI_NOR_SLEEP_WHILE_WAITING_UNTIL_READY 1
#define CONFIG_SPI_NOR_FLASH_LAYOUT_PAGE_SIZE 65536
#define CONFIG_SOC_FLASH_NRF 1
#define CONFIG_SOC_FLASH_NRF_RADIO_SYNC_NONE 1
#define CONFIG_FLASH_LOG_LEVEL_DEFAULT 1
#define CONFIG_FLASH_LOG_LEVEL 3
#define CONFIG_GPIO_LOG_LEVEL_DEFAULT 1
#define CONFIG_GPIO_LOG_LEVEL 3
#define CONFIG_GPIO_NRFX 1
#define CONFIG_GPIO_NRFX_INTERRUPT 1
#define CONFIG_INTC_INIT_PRIORITY 40
#define CONFIG_INTC_LOG_LEVEL_DEFAULT 1
#define CONFIG_INTC_LOG_LEVEL 3
#define CONFIG_PINCTRL 1
#define CONFIG_PINCTRL_LOG_LEVEL_DEFAULT 1
#define CONFIG_PINCTRL_LOG_LEVEL 3
#define CONFIG_PINCTRL_STORE_REG 1
#define CONFIG_PINCTRL_NRF 1
#define CONFIG_SERIAL_HAS_DRIVER 1
#define CONFIG_SERIAL_SUPPORT_ASYNC 1
#define CONFIG_SERIAL_SUPPORT_INTERRUPT 1
#define CONFIG_UART_LOG_LEVEL_DEFAULT 1
#define CONFIG_UART_LOG_LEVEL 3
#define CONFIG_UART_NRFX 1
#define CONFIG_UART_NRFX_UARTE 1
#define CONFIG_UART_NRFX_UARTE_LEGACY_SHIM 1
#define CONFIG_UART_0_INTERRUPT_DRIVEN 1
#define CONFIG_UART_0_ENHANCED_POLL_OUT 1
#define CONFIG_UART_0_NRF_TX_BUFFER_SIZE 32
#define CONFIG_SPI_INIT_PRIORITY 50
#define CONFIG_SPI_COMPLETION_TIMEOUT_TOLERANCE 200
#define CONFIG_SPI_LOG_LEVEL_DEFAULT 1
#define CONFIG_SPI_LOG_LEVEL 3
#define CONFIG_SPI_NRFX 1
#define CONFIG_SPI_NRFX_SPIM 1
#define CONFIG_SPI_NRFX_RAM_BUFFER_SIZE 8
#define CONFIG_SPI_NRFX_WAKE_TIMEOUT_US 200
#define CONFIG_SYSTEM_CLOCK_INIT_PRIORITY 0
#define CONFIG_TICKLESS_CAPABLE 1
#define CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT 1
#define CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY 1
#define CONFIG_NRF_USBD_COMMON_LOG_LEVEL_DEFAULT 1
#define CONFIG_NRF_USBD_COMMON_LOG_LEVEL 3
#define CONFIG_USBC_LOG_LEVEL_DEFAULT 1
#define CONFIG_USBC_LOG_LEVEL 3
#define CONFIG_WIFI_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_LOG_LEVEL 3
#define CONFIG_WIFI_INIT_PRIORITY 80
#define CONFIG_WIFI_USE_NATIVE_NETWORKING 1
#define CONFIG_NET_TCP_WORKQ_STACK_SIZE 1024
#define CONFIG_NET_MGMT_EVENT_STACK_SIZE 4600
#define CONFIG_WIFI_MGMT_SCAN_CHAN_MAX_MANUAL 3
#define CONFIG_WIFI_NRF70 1
#define CONFIG_WIFI_NRF7002 1
#define CONFIG_NRF70_QSPI_LOW_POWER 1
#define CONFIG_NRF70_SYSTEM_MODE 1
#define CONFIG_NRF70_STA_MODE 1
#define CONFIG_NRF70_AP_MODE 1
#define CONFIG_NRF70_DATA_TX 1
#define CONFIG_NRF_WIFI_IF_AUTO_START 1
#define CONFIG_NRF_WIFI_PATCHES_EXTERNAL 1
#define CONFIG_NRF_WIFI_LOW_POWER 1
#define CONFIG_NRF70_TCP_IP_CHECKSUM_OFFLOAD 1
#define CONFIG_NRF70_REG_DOMAIN "00"
#define CONFIG_WIFI_NRF70_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_NRF70_LOG_LEVEL 3
#define CONFIG_NRF70_SR_COEX_SLEEP_CTRL_GPIO_CTRL 1
#define CONFIG_NRF70_SR_COEX_SWCTRL1_OUTPUT 0
#define CONFIG_NRF70_SR_COEX_BT_GRANT_ACTIVE_LOW 1
#define CONFIG_NRF70_WORKQ_STACK_SIZE 4096
#define CONFIG_NRF70_WORKQ_MAX_ITEMS 100
#define CONFIG_NRF70_MAX_TX_PENDING_QLEN 18
#define CONFIG_NRF70_PCB_LOSS_2G 0
#define CONFIG_NRF70_PCB_LOSS_5G_BAND1 0
#define CONFIG_NRF70_PCB_LOSS_5G_BAND2 0
#define CONFIG_NRF70_PCB_LOSS_5G_BAND3 0
#define CONFIG_NRF70_ANT_GAIN_2G 0
#define CONFIG_NRF70_ANT_GAIN_5G_BAND1 0
#define CONFIG_NRF70_ANT_GAIN_5G_BAND2 0
#define CONFIG_NRF70_ANT_GAIN_5G_BAND3 0
#define CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_DSSS 0
#define CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_2G_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_DSSS 0
#define CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_2G_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_1_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_1_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_1_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_1_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_2A_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_2A_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_2A_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_2A_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_2C_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_2C_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_2C_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_2C_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_3_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_3_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_3_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_3_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_4_LOWER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_4_LOWER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_BAND_UNII_4_UPPER_EDGE_BACKOFF_HT 0
#define CONFIG_NRF70_BAND_UNII_4_UPPER_EDGE_BACKOFF_HE 0
#define CONFIG_NRF70_RX_NUM_BUFS 16
#define CONFIG_NRF70_MAX_TX_AGGREGATION 4
#define CONFIG_NRF70_MAX_TX_TOKENS 10
#define CONFIG_NRF70_TX_MAX_DATA_SIZE 1600
#define CONFIG_NRF70_RX_MAX_DATA_SIZE 1600
#define CONFIG_NRF70_IRQ_WQ_PRIORITY -15
#define CONFIG_NRF70_BH_WQ_PRIORITY 0
#define CONFIG_NRF70_IRQ_WQ_STACK_SIZE 2048
#define CONFIG_NRF70_BH_WQ_STACK_SIZE 2048
#define CONFIG_NRF70_RPU_PS_IDLE_TIMEOUT_MS 10
#define CONFIG_WIFI_FIXED_MAC_ADDRESS ""
#define CONFIG_WIFI_OTP_MAC_ADDRESS 1
#define CONFIG_NRF70_RSSI_STALE_TIMEOUT_MS 1000
#define CONFIG_NRF_WIFI_CTRL_HEAP_SIZE 20000
#define CONFIG_NRF_WIFI_DATA_HEAP_SIZE 54856
#define CONFIG_NET_TC_TX_COUNT 1
#define CONFIG_SHELL_STACK_SIZE 6144
#define CONFIG_WIFI_MGMT_SCAN_SSID_FILT_MAX 2
#define CONFIG_NRF_WIFI_SCAN_MAX_BSS_CNT 0
#define CONFIG_NRF_WIFI_BEAMFORMING 1
#define CONFIG_WIFI_NRF70_SCAN_TIMEOUT_S 30
#define CONFIG_NRF_WIFI_OP_BAND 3
#define CONFIG_NRF_WIFI_IFACE_MTU 1500
#define CONFIG_NET_INTERFACE_NAME_LEN 15
#define CONFIG_NRF_WIFI_AP_DEAD_DETECT_TIMEOUT 20
#define CONFIG_NET_MGMT_EVENT_QUEUE_SIZE 5
#define CONFIG_NRF_WIFI_FEAT_WMM 1
#define CONFIG_NRF_WIFI_PS_POLL_BASED_RETRIEVAL 1
#define CONFIG_NRF_WIFI_MGMT_BUFF_OFFLOAD 1
#define CONFIG_NRF_WIFI_PS_INT_PS 1
#define CONFIG_NRF_WIFI_DISPLAY_SCAN_BSS_LIMIT 150
#define CONFIG_NRF_WIFI_MAX_PS_POLL_FAIL_CNT 10
#define CONFIG_NRF_WIFI_RX_STBC_HT 1
#define CONFIG_REQUIRES_FULL_LIBC 1
#define CONFIG_FULL_LIBC_SUPPORTED 1
#define CONFIG_MINIMAL_LIBC_SUPPORTED 1
#define CONFIG_NEWLIB_LIBC_SUPPORTED 1
#define CONFIG_PICOLIBC_SUPPORTED 1
#define CONFIG_NATIVE_LIBC_INCOMPATIBLE 1
#define CONFIG_PICOLIBC 1
#define CONFIG_HAS_NEWLIB_LIBC_NANO 1
#define CONFIG_COMMON_LIBC_ABORT 1
#define CONFIG_COMMON_LIBC_MALLOC 1
#define CONFIG_COMMON_LIBC_CALLOC 1
#define CONFIG_COMMON_LIBC_REALLOCARRAY 1
#define CONFIG_COMMON_LIBC_REMOVE 1
#define CONFIG_PICOLIBC_USE_TOOLCHAIN 1
#define CONFIG_PICOLIBC_IO_LONG_LONG 1
#define CONFIG_STDOUT_CONSOLE 1
#define CONFIG_NEED_LIBC_MEM_PARTITION 1
#define CONFIG_CRC 1
#define CONFIG_SYS_HASH_FUNC32 1
#define CONFIG_SYS_HASH_FUNC32_MURMUR3 1
#define CONFIG_SYS_HASH_FUNC32_CHOICE_MURMUR3 1
#define CONFIG_SYS_HEAP_ALLOC_LOOPS 3
#define CONFIG_SYS_HEAP_AUTO 1
#define CONFIG_NET_BUF 1
#define CONFIG_NET_BUF_LOG_LEVEL_DEFAULT 1
#define CONFIG_NET_BUF_LOG_LEVEL 3
#define CONFIG_NET_BUF_ALIGNMENT 0
#define CONFIG_FDTABLE 1
#define CONFIG_ZVFS_OPEN_MAX 16
#define CONFIG_MPSC_PBUF 1
#define CONFIG_HAS_POWEROFF 1
#define CONFIG_CBPRINTF_COMPLETE 1
#define CONFIG_CBPRINTF_FULL_INTEGRAL 1
#define CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT 1
#define CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL 3
#define CONFIG_CBPRINTF_CONVERT_CHECK_PTR 1
#define CONFIG_ZVFS 1
#define CONFIG_ZVFS_EVENTFD 1
#define CONFIG_ZVFS_EVENTFD_MAX 1
#define CONFIG_ZVFS_POLL 1
#define CONFIG_ZVFS_POLL_MAX 6
#define CONFIG_ZVFS_SELECT 1
#define CONFIG_POSIX_API 1
#define CONFIG_POSIX_AEP_CHOICE_NONE 1
#define CONFIG_POSIX_BASE_DEFINITIONS 1
#define CONFIG_POSIX_AEP_REALTIME_MINIMAL 1
#define CONFIG_POSIX_ASYNCHRONOUS_IO 1
#define CONFIG_POSIX_BARRIERS 1
#define CONFIG_MAX_PTHREAD_BARRIER_COUNT 5
#define CONFIG_PTHREAD_BARRIER_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_BARRIER_LOG_LEVEL 3
#define CONFIG_POSIX_C_LANG_SUPPORT_R 1
#define CONFIG_POSIX_C_LIB_EXT 1
#define CONFIG_GETOPT_LONG 1
#define CONFIG_POSIX_DEVICE_IO 1
#define CONFIG_POSIX_OPEN_MAX 16
#define CONFIG_POSIX_FD_MGMT 1
#define CONFIG_POSIX_FILE_SYSTEM_R 1
#define CONFIG_POSIX_FILE_SYSTEM 1
#define CONFIG_POSIX_PAGE_SIZE 0x40
#define CONFIG_POSIX_MESSAGE_PASSING 1
#define CONFIG_POSIX_MQ_OPEN_MAX 16
#define CONFIG_POSIX_MQ_PRIO_MAX 32
#define CONFIG_MSG_SIZE_MAX 16
#define CONFIG_MQUEUE_NAMELEN_MAX 16
#define CONFIG_HEAP_MEM_POOL_ADD_SIZE_MQUEUE 1024
#define CONFIG_POSIX_NETWORKING 1
#define CONFIG_POSIX_HOST_NAME_MAX 63
#define CONFIG_POSIX_SINGLE_PROCESS 1
#define CONFIG_POSIX_SYSCONF_IMPL_MACRO 1
#define CONFIG_POSIX_UNAME_VERSION_LEN 70
#define CONFIG_POSIX_UNAME_NODENAME_LEN 6
#define CONFIG_POSIX_ENV_LOG_LEVEL_DEFAULT 1
#define CONFIG_POSIX_ENV_LOG_LEVEL 3
#define CONFIG_POSIX_MULTI_PROCESS 1
#define CONFIG_POSIX_MULTI_PROCESS_ALIAS_GETPID 1
#define CONFIG_POSIX_THREADS 1
#define CONFIG_POSIX_THREAD_THREADS_MAX 5
#define CONFIG_MAX_PTHREAD_MUTEX_COUNT 5
#define CONFIG_MAX_PTHREAD_COND_COUNT 5
#define CONFIG_POSIX_THREAD_KEYS_MAX 5
#define CONFIG_PTHREAD_RECYCLER_DELAY_MS 100
#define CONFIG_POSIX_THREAD_ATTR_STACKADDR 1
#define CONFIG_POSIX_THREAD_ATTR_STACKSIZE 1
#define CONFIG_POSIX_THREAD_PRIORITY_SCHEDULING 1
#define CONFIG_POSIX_PTHREAD_ATTR_STACKSIZE_BITS 23
#define CONFIG_POSIX_PTHREAD_ATTR_GUARDSIZE_BITS 9
#define CONFIG_POSIX_PTHREAD_ATTR_GUARDSIZE_DEFAULT 0
#define CONFIG_POSIX_THREAD_PRIO_INHERIT 1
#define CONFIG_POSIX_THREAD_PRIO_PROTECT 1
#define CONFIG_POSIX_THREAD_SAFE_FUNCTIONS 1
#define CONFIG_PTHREAD_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_LOG_LEVEL 3
#define CONFIG_PTHREAD_MUTEX_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_MUTEX_LOG_LEVEL 3
#define CONFIG_PTHREAD_COND_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_COND_LOG_LEVEL 3
#define CONFIG_PTHREAD_KEY_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_KEY_LOG_LEVEL 3
#define CONFIG_POSIX_READER_WRITER_LOCKS 1
#define CONFIG_MAX_PTHREAD_RWLOCK_COUNT 5
#define CONFIG_PTHREAD_RWLOCK_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_RWLOCK_LOG_LEVEL 3
#define CONFIG_POSIX_SEMAPHORES 1
#define CONFIG_POSIX_SEM_VALUE_MAX 32767
#define CONFIG_POSIX_SEM_NSEMS_MAX 256
#define CONFIG_POSIX_SEM_NAMELEN_MAX 16
#define CONFIG_POSIX_REALTIME_SIGNALS 1
#define CONFIG_POSIX_RTSIG_MAX 8
#define CONFIG_POSIX_SIGNALS 1
#define CONFIG_POSIX_SIGNAL_STRING_DESC 1
#define CONFIG_POSIX_SPIN_LOCKS 1
#define CONFIG_MAX_PTHREAD_SPINLOCK_COUNT 5
#define CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL_DEFAULT 1
#define CONFIG_PTHREAD_SPINLOCK_LOG_LEVEL 3
#define CONFIG_POSIX_FSYNC 1
#define CONFIG_POSIX_SYNCHRONIZED_IO 1
#define CONFIG_POSIX_TIMERS 1
#define CONFIG_POSIX_THREAD_CPUTIME 1
#define CONFIG_POSIX_MONOTONIC_CLOCK 1
#define CONFIG_POSIX_CLOCK_SELECTION 1
#define CONFIG_POSIX_DELAYTIMER_MAX 32
#define CONFIG_POSIX_TIMER_MAX 32
#define CONFIG_POSIX_TIMEOUTS 1
#define CONFIG_TIMER_CREATE_WAIT 100
#define CONFIG_TIMER_LOG_LEVEL_DEFAULT 1
#define CONFIG_TIMER_LOG_LEVEL 3
#define CONFIG_XSI_THREADS_EXT 1
#define CONFIG_EVENTFD 1
#define CONFIG_EVENTFD_MAX 1
#define CONFIG_MAX_PTHREAD_COUNT 5
#define CONFIG_MAX_PTHREAD_KEY_COUNT 5
#define CONFIG_MAX_TIMER_COUNT 32
#define CONFIG_MSG_COUNT_MAX 16
#define CONFIG_POSIX_LIMITS_RTSIG_MAX 8
#define CONFIG_POSIX_MAX_FDS 16
#define CONFIG_POSIX_MAX_OPEN_FILES 16
#define CONFIG_TIMER_DELAYTIMER_MAX 32
#define CONFIG_SEM_NAMELEN_MAX 16
#define CONFIG_SEM_VALUE_MAX 32767
#define CONFIG_TC_PROVIDES_POSIX_C_LANG_SUPPORT_R 1
#define CONFIG_LIBGCC_RTLIB 1
#define CONFIG_RING_BUFFER 1
#define CONFIG_NOTIFY 1
#define CONFIG_ONOFF 1
#define CONFIG_STACK_SENTINEL 1
#define CONFIG_PRINTK 1
#define CONFIG_EARLY_CONSOLE 1
#define CONFIG_ASSERT_LEVEL 2
#define CONFIG_SPIN_VALIDATE 1
#define CONFIG_ASSERT_VERBOSE 1
#define CONFIG_DEBUG_COREDUMP 1
#define CONFIG_DEBUG_COREDUMP_BACKEND_LOGGING 1
#define CONFIG_DEBUG_COREDUMP_MEMORY_DUMP_MIN 1
#define CONFIG_FILE_SYSTEM_LIB_LINK 1
#define CONFIG_FILE_SYSTEM 1
#define CONFIG_FS_LOG_LEVEL_DEFAULT 1
#define CONFIG_FS_LOG_LEVEL 3
#define CONFIG_APP_LINK_WITH_FS 1
#define CONFIG_FILE_SYSTEM_MAX_TYPES 2
#define CONFIG_FILE_SYSTEM_MAX_FILE_NAME -1
#define CONFIG_FILE_SYSTEM_INIT_PRIORITY 99
#define CONFIG_FILE_SYSTEM_LITTLEFS 1
#define CONFIG_FS_LITTLEFS_NUM_FILES 4
#define CONFIG_FS_LITTLEFS_NUM_DIRS 4
#define CONFIG_FS_LITTLEFS_READ_SIZE 16
#define CONFIG_FS_LITTLEFS_PROG_SIZE 16
#define CONFIG_FS_LITTLEFS_CACHE_SIZE 64
#define CONFIG_FS_LITTLEFS_LOOKAHEAD_SIZE 32
#define CONFIG_FS_LITTLEFS_BLOCK_CYCLES 512
#define CONFIG_FS_LITTLEFS_FC_HEAP_SIZE 0
#define CONFIG_FS_LITTLEFS_HEAP_PER_ALLOC_OVERHEAD_SIZE 32
#define CONFIG_FS_LITTLEFS_FMP_DEV 1
#define CONFIG_EXT2_LOG_LEVEL_DEFAULT 1
#define CONFIG_EXT2_LOG_LEVEL 3
#define CONFIG_NVS_LOG_LEVEL_DEFAULT 1
#define CONFIG_NVS_LOG_LEVEL 3
#define CONFIG_ZMS_LOG_LEVEL_DEFAULT 1
#define CONFIG_ZMS_LOG_LEVEL 3
#define CONFIG_LLEXT_EDK_NAME "llext-edk"
#define CONFIG_LOG_CORE_INIT_PRIORITY 0
#define CONFIG_LOG_MODE_DEFERRED 1
#define CONFIG_LOG_RUNTIME_FILTERING 1
#define CONFIG_LOG_DEFAULT_LEVEL 3
#define CONFIG_LOG_OVERRIDE_LEVEL 0
#define CONFIG_LOG_MAX_LEVEL 4
#define CONFIG_LOG_PRINTK 1
#define CONFIG_LOG_MODE_OVERFLOW 1
#define CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD 10
#define CONFIG_LOG_PROCESS_THREAD 1
#define CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS 0
#define CONFIG_LOG_PROCESS_THREAD_SLEEP_MS 1000
#define CONFIG_LOG_PROCESS_THREAD_STACK_SIZE 2048
#define CONFIG_LOG_TRACE_SHORT_TIMESTAMP 1
#define CONFIG_LOG_FUNC_NAME_PREFIX_DBG 1
#define CONFIG_LOG_BACKEND_SHOW_COLOR 1
#define CONFIG_LOG_TAG_MAX_LEN 0
#define CONFIG_LOG_OUTPUT_FORMAT_TIME_TIMESTAMP 1
#define CONFIG_LOG_USE_VLA 1
#define CONFIG_LOG_SIMPLE_MSG_OPTIMIZE 1
#define CONFIG_LOG_FAILURE_REPORT_PERIOD 1000
#define CONFIG_LOG_OUTPUT 1
#define CONFIG_MEM_ATTR 1
#define CONFIG_NETWORKING 1
#define CONFIG_NET_HOSTNAME_ENABLE 1
#define CONFIG_NET_HOSTNAME "zephyr"
#define CONFIG_NET_HOSTNAME_DYNAMIC 1
#define CONFIG_NET_HOSTNAME_MAX_LEN 63
#define CONFIG_NET_HOSTNAME_LOG_LEVEL 0
#define CONFIG_NET_L2_ETHERNET_LOG_LEVEL 0
#define CONFIG_NET_L2_ETHERNET_MGMT 1
#define CONFIG_NET_ARP 1
#define CONFIG_NET_ARP_TABLE_SIZE 2
#define CONFIG_NET_ARP_GRATUITOUS 1
#define CONFIG_NET_ARP_LOG_LEVEL 0
#define CONFIG_NET_ETHERNET_FORWARD_UNRECOGNISED_ETHERTYPE 1
#define CONFIG_NET_L2_WIFI_UTILS 1
#define CONFIG_NET_L2_WIFI_MGMT 1
#define CONFIG_NET_L2_WIFI_MGMT_LOG_LEVEL 0
#define CONFIG_WIFI_MGMT_TWT_CHECK_IP 1
#define CONFIG_WIFI_SHELL_MAX_AP_STA 1
#define CONFIG_WIFI_NM 1
#define CONFIG_WIFI_NM_MAX_MANAGED_INTERFACES 1
#define CONFIG_WIFI_NM_LOG_LEVEL 0
#define CONFIG_WIFI_MGMT_AP_STA_INACTIVITY_TIMEOUT 300
#define CONFIG_WIFI_MGMT_AP_MAX_NUM_STA 4
#define CONFIG_WIFI_ENT_IDENTITY_MAX_USERS 8
#define CONFIG_NET_L2_WIFI_SHELL 1
#define CONFIG_NET_IP 1
#define CONFIG_NET_CONNECTION_SOCKETS 1
#define CONFIG_NET_NATIVE 1
#define CONFIG_NET_NATIVE_IP 1
#define CONFIG_NET_NATIVE_IPV6 1
#define CONFIG_NET_NATIVE_IPV4 1
#define CONFIG_NET_NATIVE_TCP 1
#define CONFIG_NET_NATIVE_UDP 1
#define CONFIG_NET_INIT_PRIO 90
#define CONFIG_NET_IP_DSCP_ECN 1
#define CONFIG_NET_IF_MAX_IPV6_COUNT 1
#define CONFIG_NET_IF_UNICAST_IPV6_ADDR_COUNT 2
#define CONFIG_NET_IF_MCAST_IPV6_ADDR_COUNT 3
#define CONFIG_NET_IF_IPV6_PREFIX_COUNT 2
#define CONFIG_NET_IPV6_MTU 1280
#define CONFIG_NET_INITIAL_HOP_LIMIT 64
#define CONFIG_NET_INITIAL_MCAST_HOP_LIMIT 1
#define CONFIG_NET_IPV6_MAX_NEIGHBORS 8
#define CONFIG_NET_IPV6_ND 1
#define CONFIG_NET_IPV6_DAD 1
#define CONFIG_NET_IPV6_RS_TIMEOUT 1
#define CONFIG_NET_IPV6_RA_RDNSS 1
#define CONFIG_NET_IPV6_IID_EUI_64 1
#define CONFIG_NET_IPV6_LOG_LEVEL 0
#define CONFIG_NET_IPV6_ND_LOG_LEVEL 0
#define CONFIG_NET_IPV6_PE_LOG_LEVEL 0
#define CONFIG_NET_ICMPV6_LOG_LEVEL 0
#define CONFIG_NET_IPV6_NBR_CACHE_LOG_LEVEL 0
#define CONFIG_NET_IPV4 1
#define CONFIG_NET_IF_MAX_IPV4_COUNT 1
#define CONFIG_NET_IF_UNICAST_IPV4_ADDR_COUNT 1
#define CONFIG_NET_IF_MCAST_IPV4_ADDR_COUNT 1
#define CONFIG_NET_IPV4_DEFAULT_NETMASK 24
#define CONFIG_NET_INITIAL_TTL 64
#define CONFIG_NET_INITIAL_MCAST_TTL 1
#define CONFIG_NET_IF_MCAST_IPV4_SOURCE_COUNT 1
#define CONFIG_NET_IPV4_LOG_LEVEL 0
#define CONFIG_NET_ICMPV4_LOG_LEVEL 0
#define CONFIG_NET_IPV4_ACD_LOG_LEVEL 0
#define CONFIG_NET_TC_RX_COUNT 1
#define CONFIG_NET_TC_THREAD_COOPERATIVE 1
#define CONFIG_NET_TC_NUM_PRIORITIES 16
#define CONFIG_NET_TC_MAPPING_STRICT 1
#define CONFIG_NET_TX_DEFAULT_PRIORITY 1
#define CONFIG_NET_RX_DEFAULT_PRIORITY 0
#define CONFIG_NET_IP_ADDR_CHECK 1
#define CONFIG_NET_MAX_ROUTERS 2
#define CONFIG_NET_ROUTE 1
#define CONFIG_NET_MAX_ROUTES 8
#define CONFIG_NET_MAX_NEXTHOPS 8
#define CONFIG_NET_TCP 1
#define CONFIG_NET_TCP_LOG_LEVEL 0
#define CONFIG_NET_TCP_WORKER_PRIO 2
#define CONFIG_NET_TCP_TIME_WAIT_DELAY 1500
#define CONFIG_NET_TCP_INIT_RETRANSMISSION_TIMEOUT 200
#define CONFIG_NET_TCP_RANDOMIZED_RTO 1
#define CONFIG_NET_TCP_RETRY_COUNT 9
#define CONFIG_NET_TCP_MAX_SEND_WINDOW_SIZE 0
#define CONFIG_NET_TCP_MAX_RECV_WINDOW_SIZE 0
#define CONFIG_NET_TCP_RECV_QUEUE_TIMEOUT 2000
#define CONFIG_NET_TCP_PKT_ALLOC_TIMEOUT 100
#define CONFIG_NET_TCP_FAST_RETRANSMIT 1
#define CONFIG_NET_TCP_CONGESTION_AVOIDANCE 1
#define CONFIG_NET_TCP_REJECT_CONN_WITH_RST 1
#define CONFIG_NET_UDP 1
#define CONFIG_NET_UDP_MISSING_CHECKSUM 1
#define CONFIG_NET_UDP_LOG_LEVEL 0
#define CONFIG_NET_MAX_CONN 8
#define CONFIG_NET_MAX_CONTEXTS 5
#define CONFIG_NET_CONTEXT_SYNC_RECV 1
#define CONFIG_NET_CONTEXT_CHECK 1
#define CONFIG_NET_CONTEXT_DSCP_ECN 1
#define CONFIG_NET_CONTEXT_REUSEADDR 1
#define CONFIG_NET_CONTEXT_REUSEPORT 1
#define CONFIG_NET_BUF_RX_COUNT 8
#define CONFIG_NET_BUF_TX_COUNT 16
#define CONFIG_NET_BUF_VARIABLE_DATA_SIZE 1
#define CONFIG_NET_PKT_BUF_RX_DATA_POOL_SIZE 4096
#define CONFIG_NET_PKT_BUF_TX_DATA_POOL_SIZE 4096
#define CONFIG_NET_PKT_BUF_USER_DATA_SIZE 4
#define CONFIG_NET_DEFAULT_IF_FIRST 1
#define CONFIG_NET_INTERFACE_NAME 1
#define CONFIG_NET_MGMT 1
#define CONFIG_NET_MGMT_EVENT 1
#define CONFIG_NET_MGMT_EVENT_THREAD 1
#define CONFIG_NET_MGMT_EVENT_QUEUE 1
#define CONFIG_NET_MGMT_EVENT_QUEUE_TIMEOUT 5000
#define CONFIG_NET_MGMT_EVENT_INFO 1
#define CONFIG_NET_MGMT_EVENT_LOG_LEVEL 0
#define CONFIG_NET_PKT_LOG_LEVEL 0
#define CONFIG_NET_DEBUG_NET_PKT_EXTERNALS 0
#define CONFIG_NET_CORE_LOG_LEVEL 0
#define CONFIG_NET_IF_LOG_LEVEL 0
#define CONFIG_NET_TC_LOG_LEVEL 0
#define CONFIG_NET_UTILS_LOG_LEVEL 0
#define CONFIG_NET_CONTEXT_LOG_LEVEL 0
#define CONFIG_NET_CONN_LOG_LEVEL 0
#define CONFIG_NET_ROUTE_LOG_LEVEL 0
#define CONFIG_DNS_RESOLVER 1
#define CONFIG_DNS_RESOLVER_AUTO_INIT 1
#define CONFIG_DNS_RESOLVER_ADDITIONAL_QUERIES 1
#define CONFIG_DNS_RESOLVER_AI_MAX_ENTRIES 2
#define CONFIG_DNS_RESOLVER_MAX_SERVERS 1
#define CONFIG_DNS_RESOLVER_MAX_QUERY_LEN 255
#define CONFIG_DNS_NUM_CONCUR_QUERIES 1
#define CONFIG_DNS_RESOLVER_LOG_LEVEL 0
#define CONFIG_DNS_SOCKET_DISPATCHER 1
#define CONFIG_DNS_RESOLVER_ADDITIONAL_BUF_CTR 1
#define CONFIG_DNS_SOCKET_DISPATCHER_LOG_LEVEL 0
#define CONFIG_NET_HTTP_LOG_LEVEL 0
#define CONFIG_NET_HTTP_SERVER_LOG_LEVEL 0
#define CONFIG_NET_CONFIG_LOG_LEVEL 0
#define CONFIG_NET_CONFIG_SETTINGS 1
#define CONFIG_NET_CONFIG_INIT_TIMEOUT 0
#define CONFIG_NET_CONFIG_MY_IPV6_ADDR ""
#define CONFIG_NET_CONFIG_PEER_IPV6_ADDR ""
#define CONFIG_NET_CONFIG_MY_IPV4_ADDR "***********"
#define CONFIG_NET_CONFIG_MY_IPV4_NETMASK "*************"
#define CONFIG_NET_CONFIG_MY_IPV4_GW "***********"
#define CONFIG_NET_CONFIG_PEER_IPV4_ADDR ""
#define CONFIG_NET_SOCKETS 1
#define CONFIG_NET_SOCKETS_PRIORITY_DEFAULT 50
#define CONFIG_NET_SOCKETS_POLL_MAX 0
#define CONFIG_NET_SOCKETS_CONNECT_TIMEOUT 3000
#define CONFIG_NET_SOCKETS_DNS_TIMEOUT 30000
#define CONFIG_NET_SOCKETS_DNS_BACKOFF_INTERVAL 5000
#define CONFIG_NET_SOCKET_MAX_SEND_WAIT 10000
#define CONFIG_NET_SOCKETS_SERVICE 1
#define CONFIG_NET_SOCKETS_SERVICE_THREAD_PRIO 15
#define CONFIG_NET_SOCKETS_SERVICE_STACK_SIZE 2400
#define CONFIG_NET_SOCKETS_TLS_PRIORITY 45
#define CONFIG_NET_SOCKETS_TLS_SET_MAX_FRAGMENT_LENGTH 1
#define CONFIG_NET_SOCKETS_OFFLOAD_PRIORITY 40
#define CONFIG_NET_SOCKETS_PACKET 1
#define CONFIG_NET_SOCKETS_PACKET_DGRAM 1
#define CONFIG_NET_SOCKETPAIR 1
#define CONFIG_NET_SOCKETPAIR_BUFFER_SIZE 1024
#define CONFIG_NET_SOCKETPAIR_HEAP 1
#define CONFIG_HEAP_MEM_POOL_ADD_SIZE_SOCKETPAIR 9120
#define CONFIG_NET_SOCKETS_LOG_LEVEL 0
#define CONFIG_NET_DHCPV4 1
#define CONFIG_NET_DHCPV4_LOG_LEVEL 0
#define CONFIG_NET_DHCPV4_INITIAL_DELAY_MAX 10
#define CONFIG_NET_DHCPV4_ACCEPT_UNICAST 1
#define CONFIG_NET_DHCPV4_OPTION_DNS_ADDRESS 1
#define CONFIG_NET_DHCPV4_SERVER 1
#define CONFIG_NET_DHCPV4_SERVER_LOG_LEVEL 0
#define CONFIG_NET_DHCPV4_SERVER_INSTANCES 1
#define CONFIG_NET_DHCPV4_SERVER_ADDR_COUNT 4
#define CONFIG_NET_DHCPV4_SERVER_ADDR_LEASE_TIME 86400
#define CONFIG_NET_DHCPV4_SERVER_ADDR_DECLINE_TIME 86400
#define CONFIG_NET_DHCPV4_SERVER_ICMP_PROBE_TIMEOUT 1000
#define CONFIG_NET_DHCPV4_SERVER_NAK_UNRECOGNIZED_REQUESTS 1
#define CONFIG_NET_DHCPV4_SERVER_OPTION_DNS_ADDRESS ""
#define CONFIG_NET_DHCPV6_DUID_MAX_LEN 22
#define CONFIG_WIFI_CREDENTIALS 1
#define CONFIG_WIFI_CREDENTIALS_LOG_LEVEL_DEFAULT 1
#define CONFIG_WIFI_CREDENTIALS_LOG_LEVEL 3
#define CONFIG_WIFI_CREDENTIALS_BACKEND_SETTINGS 1
#define CONFIG_WIFI_CREDENTIALS_MAX_ENTRIES 2
#define CONFIG_WIFI_CREDENTIALS_SAE_PASSWORD_LENGTH 128
#define CONFIG_WIFI_CREDENTIALS_SHELL 1
#define CONFIG_WIFI_CREDENTIALS_CONNECT_STORED 1
#define CONFIG_WIFI_CREDENTIALS_CONNECT_STORED_CONNECTION_TIMEOUT 30
#define CONFIG_NET_CONNECTION_MANAGER 1
#define CONFIG_NET_CONNECTION_MANAGER_LOG_LEVEL 0
#define CONFIG_NET_CONNECTION_MANAGER_MONITOR_PRIORITY 1
#define CONFIG_NET_CONNECTION_MANAGER_AUTO_IF_DOWN 1
#define CONFIG_NET_CONNECTION_MANAGER_CONNECTIVITY_WIFI_MGMT 1
#define CONFIG_CONNECTIVITY_WIFI_MGMT_APPLICATION 1
#define CONFIG_TIMER_RANDOM_INITIAL_STATE 123456789
#define CONFIG_ENTROPY_DEVICE_RANDOM_GENERATOR 1
#define CONFIG_CSPRNG_AVAILABLE 1
#define CONFIG_CSPRNG_ENABLED 1
#define CONFIG_HARDWARE_DEVICE_CS_GENERATOR 1
#define CONFIG_SETTINGS 1
#define CONFIG_SETTINGS_LOG_LEVEL_DEFAULT 1
#define CONFIG_SETTINGS_LOG_LEVEL 3
#define CONFIG_SETTINGS_DYNAMIC_HANDLERS 1
#define CONFIG_SETTINGS_ZMS 1
#define CONFIG_SETTINGS_ZMS_MAX_COLLISIONS_BITS 4
#define CONFIG_SHELL 1
#define CONFIG_SHELL_LOG_LEVEL_DEFAULT 1
#define CONFIG_SHELL_LOG_LEVEL 3
#define CONFIG_SHELL_BACKENDS 1
#define CONFIG_SHELL_BACKEND_SERIAL_INIT_PRIORITY 90
#define CONFIG_SHELL_PROMPT_UART "uart:~$ "
#define CONFIG_SHELL_BACKEND_SERIAL_INTERRUPT_DRIVEN 1
#define CONFIG_SHELL_BACKEND_SERIAL_API_INTERRUPT_DRIVEN 1
#define CONFIG_SHELL_BACKEND_SERIAL_TX_RING_BUFFER_SIZE 8
#define CONFIG_SHELL_BACKEND_SERIAL_RX_RING_BUFFER_SIZE 64
#define CONFIG_SHELL_BACKEND_SERIAL_LOG_MESSAGE_QUEUE_TIMEOUT 100
#define CONFIG_SHELL_BACKEND_SERIAL_LOG_MESSAGE_QUEUE_SIZE 512
#define CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL_DEFAULT 1
#define CONFIG_SHELL_BACKEND_SERIAL_LOG_LEVEL 5
#define CONFIG_SHELL_DEVICE_HELPERS 1
#define CONFIG_SHELL_BACKSPACE_MODE_DELETE 1
#define CONFIG_SHELL_PROMPT_CHANGE 1
#define CONFIG_SHELL_PROMPT_BUFF_SIZE 20
#define CONFIG_SHELL_CMD_BUFF_SIZE 256
#define CONFIG_SHELL_PRINTF_BUFF_SIZE 30
#define CONFIG_SHELL_DEFAULT_TERMINAL_WIDTH 80
#define CONFIG_SHELL_DEFAULT_TERMINAL_HEIGHT 24
#define CONFIG_SHELL_ARGC_MAX 30
#define CONFIG_SHELL_TAB 1
#define CONFIG_SHELL_TAB_AUTOCOMPLETION 1
#define CONFIG_SHELL_ASCII_FILTER 1
#define CONFIG_SHELL_WILDCARD 1
#define CONFIG_SHELL_MSG_CMD_NOT_FOUND 1
#define CONFIG_SHELL_MSG_SPECIFY_SUBCOMMAND 1
#define CONFIG_SHELL_ECHO_STATUS 1
#define CONFIG_SHELL_VT100_COMMANDS 1
#define CONFIG_SHELL_VT100_COLORS 1
#define CONFIG_SHELL_GETOPT 1
#define CONFIG_SHELL_METAKEYS 1
#define CONFIG_SHELL_HELP 1
#define CONFIG_SHELL_HELP_ON_WRONG_ARGUMENT_COUNT 1
#define CONFIG_SHELL_HISTORY 1
#define CONFIG_SHELL_HISTORY_BUFFER 512
#define CONFIG_SHELL_STATS 1
#define CONFIG_SHELL_CMDS 1
#define CONFIG_SHELL_CMDS_RESIZE 1
#define CONFIG_SHELL_CMD_ROOT ""
#define CONFIG_SHELL_LOG_BACKEND 1
#define CONFIG_SHELL_LOG_FORMAT_TIMESTAMP 1
#define CONFIG_SHELL_AUTOSTART 1
#define CONFIG_SHELL_CMDS_RETURN_VALUE 1
#define CONFIG_DEVICE_SHELL 1
#define CONFIG_DATE_SHELL 1
#define CONFIG_DEVMEM_SHELL 1
#define CONFIG_KERNEL_SHELL 1
#define CONFIG_KERNEL_THREAD_SHELL 1
#define CONFIG_KERNEL_THREAD_SHELL_LIST 1
#define CONFIG_KERNEL_THREAD_SHELL_STACKS 1
#define CONFIG_FLASH_MAP 1
#define CONFIG_TOOLCHAIN_ZEPHYR_0_17 1
#define CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_GNU_EXTENSIONS 1
#define CONFIG_LINKER_ORPHAN_SECTION_WARN 1
#define CONFIG_ROM_END_OFFSET 0x0
#define CONFIG_LD_LINKER_SCRIPT_SUPPORTED 1
#define CONFIG_LD_LINKER_TEMPLATE 1
#define CONFIG_LINKER_SORT_BY_ALIGNMENT 1
#define CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT 1
#define CONFIG_LINKER_LAST_SECTION_ID 1
#define CONFIG_LINKER_LAST_SECTION_ID_PATTERN 0xE015E015
#define CONFIG_LINKER_USE_RELAX 1
#define CONFIG_LINKER_ITERABLE_SUBALIGN 4
#define CONFIG_LINKER_DEVNULL_SUPPORT 1
#define CONFIG_STD_C99 1
#define CONFIG_TOOLCHAIN_SUPPORTS_GNU_EXTENSIONS 1
#define CONFIG_SIZE_OPTIMIZATIONS 1
#define CONFIG_LTO 1
#define CONFIG_COMPILER_TRACK_MACRO_EXPANSION 1
#define CONFIG_COMPILER_COLOR_DIAGNOSTICS 1
#define CONFIG_FORTIFY_SOURCE_COMPILE_TIME 1
#define CONFIG_COMPILER_OPT ""
#define CONFIG_RUNTIME_ERROR_CHECKS 1
#define CONFIG_KERNEL_BIN_NAME "zephyr"
#define CONFIG_OUTPUT_STAT 1
#define CONFIG_OUTPUT_PRINT_MEMORY_USAGE 1
#define CONFIG_BUILD_GAP_FILL_PATTERN 0xFF
#define CONFIG_BUILD_OUTPUT_STRIP_PATHS 1
#define CONFIG_CHECK_INIT_PRIORITIES 1
#define CONFIG_DEPRECATED 1
#define CONFIG_WARN_DEPRECATED 1
#define CONFIG_EXPERIMENTAL 1
#define CONFIG_ENFORCE_ZEPHYR_STDINT 1
#define CONFIG_LEGACY_GENERATED_INCLUDE_PATH 1
