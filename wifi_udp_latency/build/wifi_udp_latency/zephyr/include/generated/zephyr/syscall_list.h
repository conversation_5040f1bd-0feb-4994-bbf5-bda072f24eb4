/* auto-generated by gen_syscalls.py, don't edit */

#ifndef ZEPHYR_SYSCALL_LIST_H
#define ZEPHYR_SYSCALL_LIST_H

#define K_SYSCALL_DEVICE_GET_BINDING 0
#define K_SYSCALL_DEVICE_GET_BY_DT_NODELABEL 1
#define K_SYSCALL_DEVICE_INIT 2
#define K_SYSCALL_DEVICE_IS_READY 3
#define K_SYSCALL_ENTROPY_GET_ENTROPY 4
#define K_SYSCALL_FLASH_COPY 5
#define K_SYSCALL_FLASH_ERASE 6
#define K_SYSCALL_FLASH_EX_OP 7
#define K_SYSCALL_FLASH_FILL 8
#define K_SYSCALL_FLASH_FLATTEN 9
#define K_SYSCALL_FLASH_GET_PAGE_COUNT 10
#define K_SYSCALL_FLASH_GET_PAGE_INFO_BY_IDX 11
#define K_SYSCALL_FLASH_GET_PAGE_INFO_BY_OFFS 12
#define K_SYSCALL_FLASH_GET_PARAMETERS 13
#define K_SYSCALL_FLASH_GET_SIZE 14
#define K_SYSCALL_FLASH_GET_WRITE_BLOCK_SIZE 15
#define K_SYSCALL_FLASH_READ 16
#define K_SYSCALL_FLASH_READ_JEDEC_ID 17
#define K_SYSCALL_FLASH_SFDP_READ 18
#define K_SYSCALL_FLASH_WRITE 19
#define K_SYSCALL_GPIO_GET_PENDING_INT 20
#define K_SYSCALL_GPIO_PIN_CONFIGURE 21
#define K_SYSCALL_GPIO_PIN_GET_CONFIG 22
#define K_SYSCALL_GPIO_PIN_INTERRUPT_CONFIGURE 23
#define K_SYSCALL_GPIO_PORT_CLEAR_BITS_RAW 24
#define K_SYSCALL_GPIO_PORT_GET_DIRECTION 25
#define K_SYSCALL_GPIO_PORT_GET_RAW 26
#define K_SYSCALL_GPIO_PORT_SET_BITS_RAW 27
#define K_SYSCALL_GPIO_PORT_SET_MASKED_RAW 28
#define K_SYSCALL_GPIO_PORT_TOGGLE_BITS 29
#define K_SYSCALL_K_BUSY_WAIT 30
#define K_SYSCALL_K_CONDVAR_BROADCAST 31
#define K_SYSCALL_K_CONDVAR_INIT 32
#define K_SYSCALL_K_CONDVAR_SIGNAL 33
#define K_SYSCALL_K_CONDVAR_WAIT 34
#define K_SYSCALL_K_EVENT_CLEAR 35
#define K_SYSCALL_K_EVENT_INIT 36
#define K_SYSCALL_K_EVENT_POST 37
#define K_SYSCALL_K_EVENT_SET 38
#define K_SYSCALL_K_EVENT_SET_MASKED 39
#define K_SYSCALL_K_EVENT_WAIT 40
#define K_SYSCALL_K_EVENT_WAIT_ALL 41
#define K_SYSCALL_K_FLOAT_DISABLE 42
#define K_SYSCALL_K_FLOAT_ENABLE 43
#define K_SYSCALL_K_FUTEX_WAIT 44
#define K_SYSCALL_K_FUTEX_WAKE 45
#define K_SYSCALL_K_IS_PREEMPT_THREAD 46
#define K_SYSCALL_K_MSGQ_ALLOC_INIT 47
#define K_SYSCALL_K_MSGQ_GET 48
#define K_SYSCALL_K_MSGQ_GET_ATTRS 49
#define K_SYSCALL_K_MSGQ_NUM_FREE_GET 50
#define K_SYSCALL_K_MSGQ_NUM_USED_GET 51
#define K_SYSCALL_K_MSGQ_PEEK 52
#define K_SYSCALL_K_MSGQ_PEEK_AT 53
#define K_SYSCALL_K_MSGQ_PURGE 54
#define K_SYSCALL_K_MSGQ_PUT 55
#define K_SYSCALL_K_MUTEX_INIT 56
#define K_SYSCALL_K_MUTEX_LOCK 57
#define K_SYSCALL_K_MUTEX_UNLOCK 58
#define K_SYSCALL_K_OBJECT_ACCESS_GRANT 59
#define K_SYSCALL_K_OBJECT_ALLOC 60
#define K_SYSCALL_K_OBJECT_ALLOC_SIZE 61
#define K_SYSCALL_K_OBJECT_RELEASE 62
#define K_SYSCALL_K_PIPE_ALLOC_INIT 63
#define K_SYSCALL_K_PIPE_BUFFER_FLUSH 64
#define K_SYSCALL_K_PIPE_FLUSH 65
#define K_SYSCALL_K_PIPE_GET 66
#define K_SYSCALL_K_PIPE_PUT 67
#define K_SYSCALL_K_PIPE_READ_AVAIL 68
#define K_SYSCALL_K_PIPE_WRITE_AVAIL 69
#define K_SYSCALL_K_POLL 70
#define K_SYSCALL_K_POLL_SIGNAL_CHECK 71
#define K_SYSCALL_K_POLL_SIGNAL_INIT 72
#define K_SYSCALL_K_POLL_SIGNAL_RAISE 73
#define K_SYSCALL_K_POLL_SIGNAL_RESET 74
#define K_SYSCALL_K_QUEUE_ALLOC_APPEND 75
#define K_SYSCALL_K_QUEUE_ALLOC_PREPEND 76
#define K_SYSCALL_K_QUEUE_CANCEL_WAIT 77
#define K_SYSCALL_K_QUEUE_GET 78
#define K_SYSCALL_K_QUEUE_INIT 79
#define K_SYSCALL_K_QUEUE_IS_EMPTY 80
#define K_SYSCALL_K_QUEUE_PEEK_HEAD 81
#define K_SYSCALL_K_QUEUE_PEEK_TAIL 82
#define K_SYSCALL_K_RESCHEDULE 83
#define K_SYSCALL_K_SCHED_CURRENT_THREAD_QUERY 84
#define K_SYSCALL_K_SEM_COUNT_GET 85
#define K_SYSCALL_K_SEM_GIVE 86
#define K_SYSCALL_K_SEM_INIT 87
#define K_SYSCALL_K_SEM_RESET 88
#define K_SYSCALL_K_SEM_TAKE 89
#define K_SYSCALL_K_SLEEP 90
#define K_SYSCALL_K_STACK_ALLOC_INIT 91
#define K_SYSCALL_K_STACK_POP 92
#define K_SYSCALL_K_STACK_PUSH 93
#define K_SYSCALL_K_STR_OUT 94
#define K_SYSCALL_K_THREAD_ABORT 95
#define K_SYSCALL_K_THREAD_CREATE 96
#define K_SYSCALL_K_THREAD_CUSTOM_DATA_GET 97
#define K_SYSCALL_K_THREAD_CUSTOM_DATA_SET 98
#define K_SYSCALL_K_THREAD_DEADLINE_SET 99
#define K_SYSCALL_K_THREAD_JOIN 100
#define K_SYSCALL_K_THREAD_NAME_COPY 101
#define K_SYSCALL_K_THREAD_NAME_SET 102
#define K_SYSCALL_K_THREAD_PRIORITY_GET 103
#define K_SYSCALL_K_THREAD_PRIORITY_SET 104
#define K_SYSCALL_K_THREAD_RESUME 105
#define K_SYSCALL_K_THREAD_STACK_ALLOC 106
#define K_SYSCALL_K_THREAD_STACK_FREE 107
#define K_SYSCALL_K_THREAD_STACK_SPACE_GET 108
#define K_SYSCALL_K_THREAD_SUSPEND 109
#define K_SYSCALL_K_THREAD_TIMEOUT_EXPIRES_TICKS 110
#define K_SYSCALL_K_THREAD_TIMEOUT_REMAINING_TICKS 111
#define K_SYSCALL_K_TIMER_EXPIRES_TICKS 112
#define K_SYSCALL_K_TIMER_REMAINING_TICKS 113
#define K_SYSCALL_K_TIMER_START 114
#define K_SYSCALL_K_TIMER_STATUS_GET 115
#define K_SYSCALL_K_TIMER_STATUS_SYNC 116
#define K_SYSCALL_K_TIMER_STOP 117
#define K_SYSCALL_K_TIMER_USER_DATA_GET 118
#define K_SYSCALL_K_TIMER_USER_DATA_SET 119
#define K_SYSCALL_K_UPTIME_TICKS 120
#define K_SYSCALL_K_USLEEP 121
#define K_SYSCALL_K_WAKEUP 122
#define K_SYSCALL_K_YIELD 123
#define K_SYSCALL_LOG_BUFFERED_CNT 124
#define K_SYSCALL_LOG_FILTER_SET 125
#define K_SYSCALL_LOG_FRONTEND_FILTER_SET 126
#define K_SYSCALL_LOG_PANIC 127
#define K_SYSCALL_LOG_PROCESS 128
#define K_SYSCALL_NET_ADDR_NTOP 129
#define K_SYSCALL_NET_ADDR_PTON 130
#define K_SYSCALL_NET_ETH_GET_PTP_CLOCK_BY_INDEX 131
#define K_SYSCALL_NET_IF_GET_BY_INDEX 132
#define K_SYSCALL_NET_IF_IPV4_ADDR_ADD_BY_INDEX 133
#define K_SYSCALL_NET_IF_IPV4_ADDR_LOOKUP_BY_INDEX 134
#define K_SYSCALL_NET_IF_IPV4_ADDR_RM_BY_INDEX 135
#define K_SYSCALL_NET_IF_IPV4_SET_GW_BY_INDEX 136
#define K_SYSCALL_NET_IF_IPV4_SET_NETMASK_BY_ADDR_BY_INDEX 137
#define K_SYSCALL_NET_IF_IPV4_SET_NETMASK_BY_INDEX 138
#define K_SYSCALL_NET_IF_IPV6_ADDR_ADD_BY_INDEX 139
#define K_SYSCALL_NET_IF_IPV6_ADDR_LOOKUP_BY_INDEX 140
#define K_SYSCALL_NET_IF_IPV6_ADDR_RM_BY_INDEX 141
#define K_SYSCALL_SPI_RELEASE 142
#define K_SYSCALL_SPI_TRANSCEIVE 143
#define K_SYSCALL_SYS_CLOCK_HW_CYCLES_PER_SEC_RUNTIME_GET 144
#define K_SYSCALL_SYS_CSRAND_GET 145
#define K_SYSCALL_SYS_RAND_GET 146
#define K_SYSCALL_UART_CONFIGURE 147
#define K_SYSCALL_UART_CONFIG_GET 148
#define K_SYSCALL_UART_DRV_CMD 149
#define K_SYSCALL_UART_ERR_CHECK 150
#define K_SYSCALL_UART_IRQ_ERR_DISABLE 151
#define K_SYSCALL_UART_IRQ_ERR_ENABLE 152
#define K_SYSCALL_UART_IRQ_IS_PENDING 153
#define K_SYSCALL_UART_IRQ_RX_DISABLE 154
#define K_SYSCALL_UART_IRQ_RX_ENABLE 155
#define K_SYSCALL_UART_IRQ_TX_DISABLE 156
#define K_SYSCALL_UART_IRQ_TX_ENABLE 157
#define K_SYSCALL_UART_IRQ_UPDATE 158
#define K_SYSCALL_UART_LINE_CTRL_GET 159
#define K_SYSCALL_UART_LINE_CTRL_SET 160
#define K_SYSCALL_UART_POLL_IN 161
#define K_SYSCALL_UART_POLL_IN_U16 162
#define K_SYSCALL_UART_POLL_OUT 163
#define K_SYSCALL_UART_POLL_OUT_U16 164
#define K_SYSCALL_UART_RX_DISABLE 165
#define K_SYSCALL_UART_RX_ENABLE 166
#define K_SYSCALL_UART_RX_ENABLE_U16 167
#define K_SYSCALL_UART_TX 168
#define K_SYSCALL_UART_TX_ABORT 169
#define K_SYSCALL_UART_TX_U16 170
#define K_SYSCALL_ZEPHYR_FPUTC 171
#define K_SYSCALL_ZEPHYR_FWRITE 172
#define K_SYSCALL_ZEPHYR_READ_STDIN 173
#define K_SYSCALL_ZEPHYR_WRITE_STDOUT 174
#define K_SYSCALL_ZSOCK_ACCEPT 175
#define K_SYSCALL_ZSOCK_BIND 176
#define K_SYSCALL_ZSOCK_CLOSE 177
#define K_SYSCALL_ZSOCK_CONNECT 178
#define K_SYSCALL_ZSOCK_FCNTL_IMPL 179
#define K_SYSCALL_ZSOCK_GETHOSTNAME 180
#define K_SYSCALL_ZSOCK_GETPEERNAME 181
#define K_SYSCALL_ZSOCK_GETSOCKNAME 182
#define K_SYSCALL_ZSOCK_GETSOCKOPT 183
#define K_SYSCALL_ZSOCK_GET_CONTEXT_OBJECT 184
#define K_SYSCALL_ZSOCK_INET_PTON 185
#define K_SYSCALL_ZSOCK_IOCTL_IMPL 186
#define K_SYSCALL_ZSOCK_LISTEN 187
#define K_SYSCALL_ZSOCK_RECVFROM 188
#define K_SYSCALL_ZSOCK_RECVMSG 189
#define K_SYSCALL_ZSOCK_SENDMSG 190
#define K_SYSCALL_ZSOCK_SENDTO 191
#define K_SYSCALL_ZSOCK_SETSOCKOPT 192
#define K_SYSCALL_ZSOCK_SHUTDOWN 193
#define K_SYSCALL_ZSOCK_SOCKET 194
#define K_SYSCALL_ZSOCK_SOCKETPAIR 195
#define K_SYSCALL_ZVFS_POLL 196
#define K_SYSCALL_ZVFS_SELECT 197
#define K_SYSCALL_Z_LOG_MSG_SIMPLE_CREATE_0 198
#define K_SYSCALL_Z_LOG_MSG_SIMPLE_CREATE_1 199
#define K_SYSCALL_Z_LOG_MSG_SIMPLE_CREATE_2 200
#define K_SYSCALL_Z_LOG_MSG_STATIC_CREATE 201
#define K_SYSCALL_Z_SYS_MUTEX_KERNEL_LOCK 202
#define K_SYSCALL_Z_SYS_MUTEX_KERNEL_UNLOCK 203
#define K_SYSCALL_Z_ZSOCK_GETADDRINFO_INTERNAL 204
#define K_SYSCALL___POSIX_CLOCK_GET_BASE 205
#define K_SYSCALL_BAD 206
#define K_SYSCALL_LIMIT 207


/* Following syscalls are not used in image */
#define K_SYSCALL_ADC_CHANNEL_SETUP 208
#define K_SYSCALL_ADC_READ 209
#define K_SYSCALL_ADC_READ_ASYNC 210
#define K_SYSCALL_ATOMIC_ADD 211
#define K_SYSCALL_ATOMIC_AND 212
#define K_SYSCALL_ATOMIC_CAS 213
#define K_SYSCALL_ATOMIC_NAND 214
#define K_SYSCALL_ATOMIC_OR 215
#define K_SYSCALL_ATOMIC_PTR_CAS 216
#define K_SYSCALL_ATOMIC_PTR_SET 217
#define K_SYSCALL_ATOMIC_SET 218
#define K_SYSCALL_ATOMIC_SUB 219
#define K_SYSCALL_ATOMIC_XOR 220
#define K_SYSCALL_AUXDISPLAY_BACKLIGHT_GET 221
#define K_SYSCALL_AUXDISPLAY_BACKLIGHT_SET 222
#define K_SYSCALL_AUXDISPLAY_BRIGHTNESS_GET 223
#define K_SYSCALL_AUXDISPLAY_BRIGHTNESS_SET 224
#define K_SYSCALL_AUXDISPLAY_CAPABILITIES_GET 225
#define K_SYSCALL_AUXDISPLAY_CLEAR 226
#define K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_GET 227
#define K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_SET 228
#define K_SYSCALL_AUXDISPLAY_CURSOR_SET_ENABLED 229
#define K_SYSCALL_AUXDISPLAY_CURSOR_SHIFT_SET 230
#define K_SYSCALL_AUXDISPLAY_CUSTOM_CHARACTER_SET 231
#define K_SYSCALL_AUXDISPLAY_CUSTOM_COMMAND 232
#define K_SYSCALL_AUXDISPLAY_DISPLAY_OFF 233
#define K_SYSCALL_AUXDISPLAY_DISPLAY_ON 234
#define K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_GET 235
#define K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_SET 236
#define K_SYSCALL_AUXDISPLAY_IS_BUSY 237
#define K_SYSCALL_AUXDISPLAY_POSITION_BLINKING_SET_ENABLED 238
#define K_SYSCALL_AUXDISPLAY_WRITE 239
#define K_SYSCALL_BBRAM_CHECK_INVALID 240
#define K_SYSCALL_BBRAM_CHECK_POWER 241
#define K_SYSCALL_BBRAM_CHECK_STANDBY_POWER 242
#define K_SYSCALL_BBRAM_GET_SIZE 243
#define K_SYSCALL_BBRAM_READ 244
#define K_SYSCALL_BBRAM_WRITE 245
#define K_SYSCALL_BC12_SET_RESULT_CB 246
#define K_SYSCALL_BC12_SET_ROLE 247
#define K_SYSCALL_CAN_ADD_RX_FILTER_MSGQ 248
#define K_SYSCALL_CAN_CALC_TIMING 249
#define K_SYSCALL_CAN_CALC_TIMING_DATA 250
#define K_SYSCALL_CAN_GET_BITRATE_MAX 251
#define K_SYSCALL_CAN_GET_BITRATE_MIN 252
#define K_SYSCALL_CAN_GET_CAPABILITIES 253
#define K_SYSCALL_CAN_GET_CORE_CLOCK 254
#define K_SYSCALL_CAN_GET_MAX_FILTERS 255
#define K_SYSCALL_CAN_GET_MODE 256
#define K_SYSCALL_CAN_GET_STATE 257
#define K_SYSCALL_CAN_GET_TIMING_DATA_MAX 258
#define K_SYSCALL_CAN_GET_TIMING_DATA_MIN 259
#define K_SYSCALL_CAN_GET_TIMING_MAX 260
#define K_SYSCALL_CAN_GET_TIMING_MIN 261
#define K_SYSCALL_CAN_GET_TRANSCEIVER 262
#define K_SYSCALL_CAN_RECOVER 263
#define K_SYSCALL_CAN_REMOVE_RX_FILTER 264
#define K_SYSCALL_CAN_SEND 265
#define K_SYSCALL_CAN_SET_BITRATE 266
#define K_SYSCALL_CAN_SET_BITRATE_DATA 267
#define K_SYSCALL_CAN_SET_MODE 268
#define K_SYSCALL_CAN_SET_TIMING 269
#define K_SYSCALL_CAN_SET_TIMING_DATA 270
#define K_SYSCALL_CAN_START 271
#define K_SYSCALL_CAN_STATS_GET_ACK_ERRORS 272
#define K_SYSCALL_CAN_STATS_GET_BIT0_ERRORS 273
#define K_SYSCALL_CAN_STATS_GET_BIT1_ERRORS 274
#define K_SYSCALL_CAN_STATS_GET_BIT_ERRORS 275
#define K_SYSCALL_CAN_STATS_GET_CRC_ERRORS 276
#define K_SYSCALL_CAN_STATS_GET_FORM_ERRORS 277
#define K_SYSCALL_CAN_STATS_GET_RX_OVERRUNS 278
#define K_SYSCALL_CAN_STATS_GET_STUFF_ERRORS 279
#define K_SYSCALL_CAN_STOP 280
#define K_SYSCALL_CHARGER_CHARGE_ENABLE 281
#define K_SYSCALL_CHARGER_GET_PROP 282
#define K_SYSCALL_CHARGER_SET_PROP 283
#define K_SYSCALL_COMPARATOR_GET_OUTPUT 284
#define K_SYSCALL_COMPARATOR_SET_TRIGGER 285
#define K_SYSCALL_COMPARATOR_TRIGGER_IS_PENDING 286
#define K_SYSCALL_COUNTER_CANCEL_CHANNEL_ALARM 287
#define K_SYSCALL_COUNTER_GET_FREQUENCY 288
#define K_SYSCALL_COUNTER_GET_GUARD_PERIOD 289
#define K_SYSCALL_COUNTER_GET_MAX_TOP_VALUE 290
#define K_SYSCALL_COUNTER_GET_NUM_OF_CHANNELS 291
#define K_SYSCALL_COUNTER_GET_PENDING_INT 292
#define K_SYSCALL_COUNTER_GET_TOP_VALUE 293
#define K_SYSCALL_COUNTER_GET_VALUE 294
#define K_SYSCALL_COUNTER_GET_VALUE_64 295
#define K_SYSCALL_COUNTER_IS_COUNTING_UP 296
#define K_SYSCALL_COUNTER_SET_CHANNEL_ALARM 297
#define K_SYSCALL_COUNTER_SET_GUARD_PERIOD 298
#define K_SYSCALL_COUNTER_SET_TOP_VALUE 299
#define K_SYSCALL_COUNTER_START 300
#define K_SYSCALL_COUNTER_STOP 301
#define K_SYSCALL_COUNTER_TICKS_TO_US 302
#define K_SYSCALL_COUNTER_US_TO_TICKS 303
#define K_SYSCALL_DAC_CHANNEL_SETUP 304
#define K_SYSCALL_DAC_WRITE_VALUE 305
#define K_SYSCALL_DEVMUX_SELECT_GET 306
#define K_SYSCALL_DEVMUX_SELECT_SET 307
#define K_SYSCALL_DMA_CHAN_FILTER 308
#define K_SYSCALL_DMA_RELEASE_CHANNEL 309
#define K_SYSCALL_DMA_REQUEST_CHANNEL 310
#define K_SYSCALL_DMA_RESUME 311
#define K_SYSCALL_DMA_START 312
#define K_SYSCALL_DMA_STOP 313
#define K_SYSCALL_DMA_SUSPEND 314
#define K_SYSCALL_EEPROM_GET_SIZE 315
#define K_SYSCALL_EEPROM_READ 316
#define K_SYSCALL_EEPROM_WRITE 317
#define K_SYSCALL_EMUL_FUEL_GAUGE_IS_BATTERY_CUTOFF 318
#define K_SYSCALL_EMUL_FUEL_GAUGE_SET_BATTERY_CHARGING 319
#define K_SYSCALL_ESPI_CONFIG 320
#define K_SYSCALL_ESPI_FLASH_ERASE 321
#define K_SYSCALL_ESPI_GET_CHANNEL_STATUS 322
#define K_SYSCALL_ESPI_READ_FLASH 323
#define K_SYSCALL_ESPI_READ_LPC_REQUEST 324
#define K_SYSCALL_ESPI_READ_REQUEST 325
#define K_SYSCALL_ESPI_RECEIVE_OOB 326
#define K_SYSCALL_ESPI_RECEIVE_VWIRE 327
#define K_SYSCALL_ESPI_SAF_ACTIVATE 328
#define K_SYSCALL_ESPI_SAF_CONFIG 329
#define K_SYSCALL_ESPI_SAF_FLASH_ERASE 330
#define K_SYSCALL_ESPI_SAF_FLASH_READ 331
#define K_SYSCALL_ESPI_SAF_FLASH_UNSUCCESS 332
#define K_SYSCALL_ESPI_SAF_FLASH_WRITE 333
#define K_SYSCALL_ESPI_SAF_GET_CHANNEL_STATUS 334
#define K_SYSCALL_ESPI_SAF_SET_PROTECTION_REGIONS 335
#define K_SYSCALL_ESPI_SEND_OOB 336
#define K_SYSCALL_ESPI_SEND_VWIRE 337
#define K_SYSCALL_ESPI_WRITE_FLASH 338
#define K_SYSCALL_ESPI_WRITE_LPC_REQUEST 339
#define K_SYSCALL_ESPI_WRITE_REQUEST 340
#define K_SYSCALL_FLASH_SIMULATOR_GET_MEMORY 341
#define K_SYSCALL_FUEL_GAUGE_BATTERY_CUTOFF 342
#define K_SYSCALL_FUEL_GAUGE_GET_BUFFER_PROP 343
#define K_SYSCALL_FUEL_GAUGE_GET_PROP 344
#define K_SYSCALL_FUEL_GAUGE_GET_PROPS 345
#define K_SYSCALL_FUEL_GAUGE_SET_PROP 346
#define K_SYSCALL_FUEL_GAUGE_SET_PROPS 347
#define K_SYSCALL_GNSS_GET_ENABLED_SYSTEMS 348
#define K_SYSCALL_GNSS_GET_FIX_RATE 349
#define K_SYSCALL_GNSS_GET_LATEST_TIMEPULSE 350
#define K_SYSCALL_GNSS_GET_NAVIGATION_MODE 351
#define K_SYSCALL_GNSS_GET_SUPPORTED_SYSTEMS 352
#define K_SYSCALL_GNSS_SET_ENABLED_SYSTEMS 353
#define K_SYSCALL_GNSS_SET_FIX_RATE 354
#define K_SYSCALL_GNSS_SET_NAVIGATION_MODE 355
#define K_SYSCALL_HAPTICS_START_OUTPUT 356
#define K_SYSCALL_HAPTICS_STOP_OUTPUT 357
#define K_SYSCALL_HWINFO_CLEAR_RESET_CAUSE 358
#define K_SYSCALL_HWINFO_GET_DEVICE_EUI64 359
#define K_SYSCALL_HWINFO_GET_DEVICE_ID 360
#define K_SYSCALL_HWINFO_GET_RESET_CAUSE 361
#define K_SYSCALL_HWINFO_GET_SUPPORTED_RESET_CAUSE 362
#define K_SYSCALL_HWSPINLOCK_GET_MAX_ID 363
#define K_SYSCALL_HWSPINLOCK_LOCK 364
#define K_SYSCALL_HWSPINLOCK_TRYLOCK 365
#define K_SYSCALL_HWSPINLOCK_UNLOCK 366
#define K_SYSCALL_I2C_CONFIGURE 367
#define K_SYSCALL_I2C_GET_CONFIG 368
#define K_SYSCALL_I2C_RECOVER_BUS 369
#define K_SYSCALL_I2C_TARGET_DRIVER_REGISTER 370
#define K_SYSCALL_I2C_TARGET_DRIVER_UNREGISTER 371
#define K_SYSCALL_I2C_TRANSFER 372
#define K_SYSCALL_I2S_BUF_READ 373
#define K_SYSCALL_I2S_BUF_WRITE 374
#define K_SYSCALL_I2S_CONFIGURE 375
#define K_SYSCALL_I2S_TRIGGER 376
#define K_SYSCALL_I3C_DO_CCC 377
#define K_SYSCALL_I3C_TRANSFER 378
#define K_SYSCALL_IPM_COMPLETE 379
#define K_SYSCALL_IPM_MAX_DATA_SIZE_GET 380
#define K_SYSCALL_IPM_MAX_ID_VAL_GET 381
#define K_SYSCALL_IPM_SEND 382
#define K_SYSCALL_IPM_SET_ENABLED 383
#define K_SYSCALL_IVSHMEM_ENABLE_INTERRUPTS 384
#define K_SYSCALL_IVSHMEM_GET_ID 385
#define K_SYSCALL_IVSHMEM_GET_MAX_PEERS 386
#define K_SYSCALL_IVSHMEM_GET_MEM 387
#define K_SYSCALL_IVSHMEM_GET_OUTPUT_MEM_SECTION 388
#define K_SYSCALL_IVSHMEM_GET_PROTOCOL 389
#define K_SYSCALL_IVSHMEM_GET_RW_MEM_SECTION 390
#define K_SYSCALL_IVSHMEM_GET_STATE 391
#define K_SYSCALL_IVSHMEM_GET_VECTORS 392
#define K_SYSCALL_IVSHMEM_INT_PEER 393
#define K_SYSCALL_IVSHMEM_REGISTER_HANDLER 394
#define K_SYSCALL_IVSHMEM_SET_STATE 395
#define K_SYSCALL_KSCAN_CONFIG 396
#define K_SYSCALL_KSCAN_DISABLE_CALLBACK 397
#define K_SYSCALL_KSCAN_ENABLE_CALLBACK 398
#define K_SYSCALL_K_MEM_PAGING_HISTOGRAM_BACKING_STORE_PAGE_IN_GET 399
#define K_SYSCALL_K_MEM_PAGING_HISTOGRAM_BACKING_STORE_PAGE_OUT_GET 400
#define K_SYSCALL_K_MEM_PAGING_HISTOGRAM_EVICTION_GET 401
#define K_SYSCALL_K_MEM_PAGING_STATS_GET 402
#define K_SYSCALL_K_MEM_PAGING_THREAD_STATS_GET 403
#define K_SYSCALL_LED_BLINK 404
#define K_SYSCALL_LED_GET_INFO 405
#define K_SYSCALL_LED_OFF 406
#define K_SYSCALL_LED_ON 407
#define K_SYSCALL_LED_SET_BRIGHTNESS 408
#define K_SYSCALL_LED_SET_CHANNEL 409
#define K_SYSCALL_LED_SET_COLOR 410
#define K_SYSCALL_LED_WRITE_CHANNELS 411
#define K_SYSCALL_LLEXT_GET_FN_TABLE 412
#define K_SYSCALL_MAXIM_DS3231_GET_SYNCPOINT 413
#define K_SYSCALL_MAXIM_DS3231_REQ_SYNCPOINT 414
#define K_SYSCALL_MBOX_MAX_CHANNELS_GET 415
#define K_SYSCALL_MBOX_MTU_GET 416
#define K_SYSCALL_MBOX_SEND 417
#define K_SYSCALL_MBOX_SET_ENABLED 418
#define K_SYSCALL_MDIO_BUS_DISABLE 419
#define K_SYSCALL_MDIO_BUS_ENABLE 420
#define K_SYSCALL_MDIO_READ 421
#define K_SYSCALL_MDIO_READ_C45 422
#define K_SYSCALL_MDIO_WRITE 423
#define K_SYSCALL_MDIO_WRITE_C45 424
#define K_SYSCALL_MSPI_CONFIG 425
#define K_SYSCALL_MSPI_DEV_CONFIG 426
#define K_SYSCALL_MSPI_GET_CHANNEL_STATUS 427
#define K_SYSCALL_MSPI_SCRAMBLE_CONFIG 428
#define K_SYSCALL_MSPI_TIMING_CONFIG 429
#define K_SYSCALL_MSPI_TRANSCEIVE 430
#define K_SYSCALL_MSPI_XIP_CONFIG 431
#define K_SYSCALL_NET_SOCKET_SERVICE_REGISTER 432
#define K_SYSCALL_NRF_QSPI_NOR_XIP_ENABLE 433
#define K_SYSCALL_PECI_CONFIG 434
#define K_SYSCALL_PECI_DISABLE 435
#define K_SYSCALL_PECI_ENABLE 436
#define K_SYSCALL_PECI_TRANSFER 437
#define K_SYSCALL_PS2_CONFIG 438
#define K_SYSCALL_PS2_DISABLE_CALLBACK 439
#define K_SYSCALL_PS2_ENABLE_CALLBACK 440
#define K_SYSCALL_PS2_READ 441
#define K_SYSCALL_PS2_WRITE 442
#define K_SYSCALL_PTP_CLOCK_GET 443
#define K_SYSCALL_PWM_CAPTURE_CYCLES 444
#define K_SYSCALL_PWM_DISABLE_CAPTURE 445
#define K_SYSCALL_PWM_ENABLE_CAPTURE 446
#define K_SYSCALL_PWM_GET_CYCLES_PER_SEC 447
#define K_SYSCALL_PWM_SET_CYCLES 448
#define K_SYSCALL_RESET_LINE_ASSERT 449
#define K_SYSCALL_RESET_LINE_DEASSERT 450
#define K_SYSCALL_RESET_LINE_TOGGLE 451
#define K_SYSCALL_RESET_STATUS 452
#define K_SYSCALL_RETAINED_MEM_CLEAR 453
#define K_SYSCALL_RETAINED_MEM_READ 454
#define K_SYSCALL_RETAINED_MEM_SIZE 455
#define K_SYSCALL_RETAINED_MEM_WRITE 456
#define K_SYSCALL_RTC_ALARM_GET_SUPPORTED_FIELDS 457
#define K_SYSCALL_RTC_ALARM_GET_TIME 458
#define K_SYSCALL_RTC_ALARM_IS_PENDING 459
#define K_SYSCALL_RTC_ALARM_SET_CALLBACK 460
#define K_SYSCALL_RTC_ALARM_SET_TIME 461
#define K_SYSCALL_RTC_GET_CALIBRATION 462
#define K_SYSCALL_RTC_GET_TIME 463
#define K_SYSCALL_RTC_SET_CALIBRATION 464
#define K_SYSCALL_RTC_SET_TIME 465
#define K_SYSCALL_RTC_UPDATE_SET_CALLBACK 466
#define K_SYSCALL_RTIO_CQE_COPY_OUT 467
#define K_SYSCALL_RTIO_CQE_GET_MEMPOOL_BUFFER 468
#define K_SYSCALL_RTIO_RELEASE_BUFFER 469
#define K_SYSCALL_RTIO_SQE_CANCEL 470
#define K_SYSCALL_RTIO_SQE_COPY_IN_GET_HANDLES 471
#define K_SYSCALL_RTIO_SUBMIT 472
#define K_SYSCALL_SDHC_CARD_BUSY 473
#define K_SYSCALL_SDHC_CARD_PRESENT 474
#define K_SYSCALL_SDHC_DISABLE_INTERRUPT 475
#define K_SYSCALL_SDHC_ENABLE_INTERRUPT 476
#define K_SYSCALL_SDHC_EXECUTE_TUNING 477
#define K_SYSCALL_SDHC_GET_HOST_PROPS 478
#define K_SYSCALL_SDHC_HW_RESET 479
#define K_SYSCALL_SDHC_REQUEST 480
#define K_SYSCALL_SDHC_SET_IO 481
#define K_SYSCALL_SENSOR_ATTR_GET 482
#define K_SYSCALL_SENSOR_ATTR_SET 483
#define K_SYSCALL_SENSOR_CHANNEL_GET 484
#define K_SYSCALL_SENSOR_GET_DECODER 485
#define K_SYSCALL_SENSOR_RECONFIGURE_READ_IODEV 486
#define K_SYSCALL_SENSOR_SAMPLE_FETCH 487
#define K_SYSCALL_SENSOR_SAMPLE_FETCH_CHAN 488
#define K_SYSCALL_SIP_SUPERVISORY_CALL 489
#define K_SYSCALL_SIP_SVC_PLAT_ASYNC_RES_REQ 490
#define K_SYSCALL_SIP_SVC_PLAT_ASYNC_RES_RES 491
#define K_SYSCALL_SIP_SVC_PLAT_FORMAT_TRANS_ID 492
#define K_SYSCALL_SIP_SVC_PLAT_FREE_ASYNC_MEMORY 493
#define K_SYSCALL_SIP_SVC_PLAT_FUNC_ID_VALID 494
#define K_SYSCALL_SIP_SVC_PLAT_GET_ERROR_CODE 495
#define K_SYSCALL_SIP_SVC_PLAT_GET_TRANS_IDX 496
#define K_SYSCALL_SIP_SVC_PLAT_UPDATE_TRANS_ID 497
#define K_SYSCALL_SMBUS_BLOCK_PCALL 498
#define K_SYSCALL_SMBUS_BLOCK_READ 499
#define K_SYSCALL_SMBUS_BLOCK_WRITE 500
#define K_SYSCALL_SMBUS_BYTE_DATA_READ 501
#define K_SYSCALL_SMBUS_BYTE_DATA_WRITE 502
#define K_SYSCALL_SMBUS_BYTE_READ 503
#define K_SYSCALL_SMBUS_BYTE_WRITE 504
#define K_SYSCALL_SMBUS_CONFIGURE 505
#define K_SYSCALL_SMBUS_GET_CONFIG 506
#define K_SYSCALL_SMBUS_HOST_NOTIFY_REMOVE_CB 507
#define K_SYSCALL_SMBUS_PCALL 508
#define K_SYSCALL_SMBUS_QUICK 509
#define K_SYSCALL_SMBUS_SMBALERT_REMOVE_CB 510
#define K_SYSCALL_SMBUS_WORD_DATA_READ 511
#define K_SYSCALL_SMBUS_WORD_DATA_WRITE 512
#define K_SYSCALL_STEPPER_ENABLE 513
#define K_SYSCALL_STEPPER_GET_ACTUAL_POSITION 514
#define K_SYSCALL_STEPPER_GET_MICRO_STEP_RES 515
#define K_SYSCALL_STEPPER_IS_MOVING 516
#define K_SYSCALL_STEPPER_MOVE_BY 517
#define K_SYSCALL_STEPPER_MOVE_TO 518
#define K_SYSCALL_STEPPER_RUN 519
#define K_SYSCALL_STEPPER_SET_EVENT_CALLBACK 520
#define K_SYSCALL_STEPPER_SET_MAX_VELOCITY 521
#define K_SYSCALL_STEPPER_SET_MICRO_STEP_RES 522
#define K_SYSCALL_STEPPER_SET_REFERENCE_POSITION 523
#define K_SYSCALL_SYSCON_GET_BASE 524
#define K_SYSCALL_SYSCON_GET_SIZE 525
#define K_SYSCALL_SYSCON_READ_REG 526
#define K_SYSCALL_SYSCON_WRITE_REG 527
#define K_SYSCALL_SYS_CACHE_DATA_FLUSH_AND_INVD_RANGE 528
#define K_SYSCALL_SYS_CACHE_DATA_FLUSH_RANGE 529
#define K_SYSCALL_SYS_CACHE_DATA_INVD_RANGE 530
#define K_SYSCALL_TEE_CANCEL 531
#define K_SYSCALL_TEE_CLOSE_SESSION 532
#define K_SYSCALL_TEE_GET_VERSION 533
#define K_SYSCALL_TEE_INVOKE_FUNC 534
#define K_SYSCALL_TEE_OPEN_SESSION 535
#define K_SYSCALL_TEE_SHM_ALLOC 536
#define K_SYSCALL_TEE_SHM_FREE 537
#define K_SYSCALL_TEE_SHM_REGISTER 538
#define K_SYSCALL_TEE_SHM_UNREGISTER 539
#define K_SYSCALL_TEE_SUPPL_RECV 540
#define K_SYSCALL_TEE_SUPPL_SEND 541
#define K_SYSCALL_TGPIO_PIN_CONFIG_EXT_TIMESTAMP 542
#define K_SYSCALL_TGPIO_PIN_DISABLE 543
#define K_SYSCALL_TGPIO_PIN_PERIODIC_OUTPUT 544
#define K_SYSCALL_TGPIO_PIN_READ_TS_EC 545
#define K_SYSCALL_TGPIO_PORT_GET_CYCLES_PER_SECOND 546
#define K_SYSCALL_TGPIO_PORT_GET_TIME 547
#define K_SYSCALL_UPDATEHUB_AUTOHANDLER 548
#define K_SYSCALL_UPDATEHUB_CONFIRM 549
#define K_SYSCALL_UPDATEHUB_PROBE 550
#define K_SYSCALL_UPDATEHUB_REBOOT 551
#define K_SYSCALL_UPDATEHUB_UPDATE 552
#define K_SYSCALL_USER_FAULT 553
#define K_SYSCALL_W1_CHANGE_BUS_LOCK 554
#define K_SYSCALL_W1_CONFIGURE 555
#define K_SYSCALL_W1_GET_SLAVE_COUNT 556
#define K_SYSCALL_W1_READ_BIT 557
#define K_SYSCALL_W1_READ_BLOCK 558
#define K_SYSCALL_W1_READ_BYTE 559
#define K_SYSCALL_W1_RESET_BUS 560
#define K_SYSCALL_W1_SEARCH_BUS 561
#define K_SYSCALL_W1_WRITE_BIT 562
#define K_SYSCALL_W1_WRITE_BLOCK 563
#define K_SYSCALL_W1_WRITE_BYTE 564
#define K_SYSCALL_WDT_DISABLE 565
#define K_SYSCALL_WDT_FEED 566
#define K_SYSCALL_WDT_SETUP 567
#define K_SYSCALL_XTENSA_USER_FAULT 568
#define K_SYSCALL_Z_ERRNO 569


#ifndef _ASMLANGUAGE

#include <stdarg.h>
#include <stdint.h>

#endif /* _ASMLANGUAGE */

#endif /* ZEPHYR_SYSCALL_LIST_H */
