/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_FDTABLE_H
#define Z_INCLUDE_SYSCALLS_FDTABLE_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_zvfs_poll(struct zvfs_pollfd * fds, int nfds, int poll_timeout);

__pinned_func
static inline int zvfs_poll(struct zvfs_pollfd * fds, int nfds, int poll_timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct zvfs_pollfd * val; } parm0 = { .val = fds };
		union { uintptr_t x; int val; } parm1 = { .val = nfds };
		union { uintptr_t x; int val; } parm2 = { .val = poll_timeout };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_ZVFS_POLL);
	}
#endif
	compiler_barrier();
	return z_impl_zvfs_poll(fds, nfds, poll_timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define zvfs_poll(fds, nfds, poll_timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ZVFS_POLL, zvfs_poll, fds, nfds, poll_timeout); 	syscall__retval = zvfs_poll(fds, nfds, poll_timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_ZVFS_POLL, zvfs_poll, fds, nfds, poll_timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_zvfs_select(int nfds, struct zvfs_fd_set *ZRESTRICT readfds, struct zvfs_fd_set *ZRESTRICT writefds, struct zvfs_fd_set *ZRESTRICT errorfds, const struct timespec *ZRESTRICT timeout, const void *ZRESTRICT sigmask);

__pinned_func
static inline int zvfs_select(int nfds, struct zvfs_fd_set *ZRESTRICT readfds, struct zvfs_fd_set *ZRESTRICT writefds, struct zvfs_fd_set *ZRESTRICT errorfds, const struct timespec *ZRESTRICT timeout, const void *ZRESTRICT sigmask)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = nfds };
		union { uintptr_t x; struct zvfs_fd_set *ZRESTRICT val; } parm1 = { .val = readfds };
		union { uintptr_t x; struct zvfs_fd_set *ZRESTRICT val; } parm2 = { .val = writefds };
		union { uintptr_t x; struct zvfs_fd_set *ZRESTRICT val; } parm3 = { .val = errorfds };
		union { uintptr_t x; const struct timespec *ZRESTRICT val; } parm4 = { .val = timeout };
		union { uintptr_t x; const void *ZRESTRICT val; } parm5 = { .val = sigmask };
		return (int) arch_syscall_invoke6(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, parm5.x, K_SYSCALL_ZVFS_SELECT);
	}
#endif
	compiler_barrier();
	return z_impl_zvfs_select(nfds, readfds, writefds, errorfds, timeout, sigmask);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define zvfs_select(nfds, readfds, writefds, errorfds, timeout, sigmask) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ZVFS_SELECT, zvfs_select, nfds, readfds, writefds, errorfds, timeout, sigmask); 	syscall__retval = zvfs_select(nfds, readfds, writefds, errorfds, timeout, sigmask); 	sys_port_trace_syscall_exit(K_SYSCALL_ZVFS_SELECT, zvfs_select, nfds, readfds, writefds, errorfds, timeout, sigmask, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
