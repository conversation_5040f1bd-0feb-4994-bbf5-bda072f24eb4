/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_TEE_H
#define Z_INCLUDE_SYSCALLS_TEE_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_tee_get_version(const struct device * dev, struct tee_version_info * info);

__pinned_func
static inline int tee_get_version(const struct device * dev, struct tee_version_info * info)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct tee_version_info * val; } parm1 = { .val = info };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_TEE_GET_VERSION);
	}
#endif
	compiler_barrier();
	return z_impl_tee_get_version(dev, info);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_get_version(dev, info) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_GET_VERSION, tee_get_version, dev, info); 	syscall__retval = tee_get_version(dev, info); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_GET_VERSION, tee_get_version, dev, info, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_open_session(const struct device * dev, struct tee_open_session_arg * arg, unsigned int num_param, struct tee_param * param, uint32_t * session_id);

__pinned_func
static inline int tee_open_session(const struct device * dev, struct tee_open_session_arg * arg, unsigned int num_param, struct tee_param * param, uint32_t * session_id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct tee_open_session_arg * val; } parm1 = { .val = arg };
		union { uintptr_t x; unsigned int val; } parm2 = { .val = num_param };
		union { uintptr_t x; struct tee_param * val; } parm3 = { .val = param };
		union { uintptr_t x; uint32_t * val; } parm4 = { .val = session_id };
		return (int) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, K_SYSCALL_TEE_OPEN_SESSION);
	}
#endif
	compiler_barrier();
	return z_impl_tee_open_session(dev, arg, num_param, param, session_id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_open_session(dev, arg, num_param, param, session_id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_OPEN_SESSION, tee_open_session, dev, arg, num_param, param, session_id); 	syscall__retval = tee_open_session(dev, arg, num_param, param, session_id); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_OPEN_SESSION, tee_open_session, dev, arg, num_param, param, session_id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_close_session(const struct device * dev, uint32_t session_id);

__pinned_func
static inline int tee_close_session(const struct device * dev, uint32_t session_id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = session_id };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_TEE_CLOSE_SESSION);
	}
#endif
	compiler_barrier();
	return z_impl_tee_close_session(dev, session_id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_close_session(dev, session_id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_CLOSE_SESSION, tee_close_session, dev, session_id); 	syscall__retval = tee_close_session(dev, session_id); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_CLOSE_SESSION, tee_close_session, dev, session_id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_cancel(const struct device * dev, uint32_t session_id, uint32_t cancel_id);

__pinned_func
static inline int tee_cancel(const struct device * dev, uint32_t session_id, uint32_t cancel_id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = session_id };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = cancel_id };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_TEE_CANCEL);
	}
#endif
	compiler_barrier();
	return z_impl_tee_cancel(dev, session_id, cancel_id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_cancel(dev, session_id, cancel_id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_CANCEL, tee_cancel, dev, session_id, cancel_id); 	syscall__retval = tee_cancel(dev, session_id, cancel_id); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_CANCEL, tee_cancel, dev, session_id, cancel_id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_invoke_func(const struct device * dev, struct tee_invoke_func_arg * arg, unsigned int num_param, struct tee_param * param);

__pinned_func
static inline int tee_invoke_func(const struct device * dev, struct tee_invoke_func_arg * arg, unsigned int num_param, struct tee_param * param)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct tee_invoke_func_arg * val; } parm1 = { .val = arg };
		union { uintptr_t x; unsigned int val; } parm2 = { .val = num_param };
		union { uintptr_t x; struct tee_param * val; } parm3 = { .val = param };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_TEE_INVOKE_FUNC);
	}
#endif
	compiler_barrier();
	return z_impl_tee_invoke_func(dev, arg, num_param, param);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_invoke_func(dev, arg, num_param, param) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_INVOKE_FUNC, tee_invoke_func, dev, arg, num_param, param); 	syscall__retval = tee_invoke_func(dev, arg, num_param, param); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_INVOKE_FUNC, tee_invoke_func, dev, arg, num_param, param, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_shm_register(const struct device * dev, void * addr, size_t size, uint32_t flags, struct tee_shm ** shm);

__pinned_func
static inline int tee_shm_register(const struct device * dev, void * addr, size_t size, uint32_t flags, struct tee_shm ** shm)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; void * val; } parm1 = { .val = addr };
		union { uintptr_t x; size_t val; } parm2 = { .val = size };
		union { uintptr_t x; uint32_t val; } parm3 = { .val = flags };
		union { uintptr_t x; struct tee_shm ** val; } parm4 = { .val = shm };
		return (int) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, K_SYSCALL_TEE_SHM_REGISTER);
	}
#endif
	compiler_barrier();
	return z_impl_tee_shm_register(dev, addr, size, flags, shm);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_shm_register(dev, addr, size, flags, shm) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SHM_REGISTER, tee_shm_register, dev, addr, size, flags, shm); 	syscall__retval = tee_shm_register(dev, addr, size, flags, shm); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SHM_REGISTER, tee_shm_register, dev, addr, size, flags, shm, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_shm_unregister(const struct device * dev, struct tee_shm * shm);

__pinned_func
static inline int tee_shm_unregister(const struct device * dev, struct tee_shm * shm)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct tee_shm * val; } parm1 = { .val = shm };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_TEE_SHM_UNREGISTER);
	}
#endif
	compiler_barrier();
	return z_impl_tee_shm_unregister(dev, shm);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_shm_unregister(dev, shm) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SHM_UNREGISTER, tee_shm_unregister, dev, shm); 	syscall__retval = tee_shm_unregister(dev, shm); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SHM_UNREGISTER, tee_shm_unregister, dev, shm, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_shm_alloc(const struct device * dev, size_t size, uint32_t flags, struct tee_shm ** shm);

__pinned_func
static inline int tee_shm_alloc(const struct device * dev, size_t size, uint32_t flags, struct tee_shm ** shm)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; size_t val; } parm1 = { .val = size };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = flags };
		union { uintptr_t x; struct tee_shm ** val; } parm3 = { .val = shm };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_TEE_SHM_ALLOC);
	}
#endif
	compiler_barrier();
	return z_impl_tee_shm_alloc(dev, size, flags, shm);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_shm_alloc(dev, size, flags, shm) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SHM_ALLOC, tee_shm_alloc, dev, size, flags, shm); 	syscall__retval = tee_shm_alloc(dev, size, flags, shm); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SHM_ALLOC, tee_shm_alloc, dev, size, flags, shm, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_shm_free(const struct device * dev, struct tee_shm * shm);

__pinned_func
static inline int tee_shm_free(const struct device * dev, struct tee_shm * shm)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct tee_shm * val; } parm1 = { .val = shm };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_TEE_SHM_FREE);
	}
#endif
	compiler_barrier();
	return z_impl_tee_shm_free(dev, shm);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_shm_free(dev, shm) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SHM_FREE, tee_shm_free, dev, shm); 	syscall__retval = tee_shm_free(dev, shm); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SHM_FREE, tee_shm_free, dev, shm, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_suppl_recv(const struct device * dev, uint32_t * func, unsigned int * num_params, struct tee_param * param);

__pinned_func
static inline int tee_suppl_recv(const struct device * dev, uint32_t * func, unsigned int * num_params, struct tee_param * param)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t * val; } parm1 = { .val = func };
		union { uintptr_t x; unsigned int * val; } parm2 = { .val = num_params };
		union { uintptr_t x; struct tee_param * val; } parm3 = { .val = param };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_TEE_SUPPL_RECV);
	}
#endif
	compiler_barrier();
	return z_impl_tee_suppl_recv(dev, func, num_params, param);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_suppl_recv(dev, func, num_params, param) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SUPPL_RECV, tee_suppl_recv, dev, func, num_params, param); 	syscall__retval = tee_suppl_recv(dev, func, num_params, param); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SUPPL_RECV, tee_suppl_recv, dev, func, num_params, param, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_tee_suppl_send(const struct device * dev, unsigned int ret, unsigned int num_params, struct tee_param * param);

__pinned_func
static inline int tee_suppl_send(const struct device * dev, unsigned int ret, unsigned int num_params, struct tee_param * param)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; unsigned int val; } parm1 = { .val = ret };
		union { uintptr_t x; unsigned int val; } parm2 = { .val = num_params };
		union { uintptr_t x; struct tee_param * val; } parm3 = { .val = param };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_TEE_SUPPL_SEND);
	}
#endif
	compiler_barrier();
	return z_impl_tee_suppl_send(dev, ret, num_params, param);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define tee_suppl_send(dev, ret, num_params, param) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_TEE_SUPPL_SEND, tee_suppl_send, dev, ret, num_params, param); 	syscall__retval = tee_suppl_send(dev, ret, num_params, param); 	sys_port_trace_syscall_exit(K_SYSCALL_TEE_SUPPL_SEND, tee_suppl_send, dev, ret, num_params, param, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
