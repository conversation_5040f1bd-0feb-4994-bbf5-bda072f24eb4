/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_STEPPER_H
#define Z_INCLUDE_SYSCALLS_STEPPER_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_stepper_enable(const struct device * dev, const bool enable);

__pinned_func
static inline int stepper_enable(const struct device * dev, const bool enable)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const bool val; } parm1 = { .val = enable };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_ENABLE);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_enable(dev, enable);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_enable(dev, enable) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_ENABLE, stepper_enable, dev, enable); 	syscall__retval = stepper_enable(dev, enable); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_ENABLE, stepper_enable, dev, enable, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_move_by(const struct device * dev, int32_t micro_steps);

__pinned_func
static inline int stepper_move_by(const struct device * dev, int32_t micro_steps)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t val; } parm1 = { .val = micro_steps };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_MOVE_BY);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_move_by(dev, micro_steps);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_move_by(dev, micro_steps) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_MOVE_BY, stepper_move_by, dev, micro_steps); 	syscall__retval = stepper_move_by(dev, micro_steps); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_MOVE_BY, stepper_move_by, dev, micro_steps, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_set_max_velocity(const struct device * dev, uint32_t micro_steps_per_second);

__pinned_func
static inline int stepper_set_max_velocity(const struct device * dev, uint32_t micro_steps_per_second)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = micro_steps_per_second };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_SET_MAX_VELOCITY);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_set_max_velocity(dev, micro_steps_per_second);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_set_max_velocity(dev, micro_steps_per_second) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_SET_MAX_VELOCITY, stepper_set_max_velocity, dev, micro_steps_per_second); 	syscall__retval = stepper_set_max_velocity(dev, micro_steps_per_second); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_SET_MAX_VELOCITY, stepper_set_max_velocity, dev, micro_steps_per_second, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_set_micro_step_res(const struct device * dev, enum stepper_micro_step_resolution resolution);

__pinned_func
static inline int stepper_set_micro_step_res(const struct device * dev, enum stepper_micro_step_resolution resolution)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum stepper_micro_step_resolution val; } parm1 = { .val = resolution };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_SET_MICRO_STEP_RES);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_set_micro_step_res(dev, resolution);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_set_micro_step_res(dev, resolution) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_SET_MICRO_STEP_RES, stepper_set_micro_step_res, dev, resolution); 	syscall__retval = stepper_set_micro_step_res(dev, resolution); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_SET_MICRO_STEP_RES, stepper_set_micro_step_res, dev, resolution, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_get_micro_step_res(const struct device * dev, enum stepper_micro_step_resolution * resolution);

__pinned_func
static inline int stepper_get_micro_step_res(const struct device * dev, enum stepper_micro_step_resolution * resolution)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum stepper_micro_step_resolution * val; } parm1 = { .val = resolution };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_GET_MICRO_STEP_RES);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_get_micro_step_res(dev, resolution);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_get_micro_step_res(dev, resolution) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_GET_MICRO_STEP_RES, stepper_get_micro_step_res, dev, resolution); 	syscall__retval = stepper_get_micro_step_res(dev, resolution); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_GET_MICRO_STEP_RES, stepper_get_micro_step_res, dev, resolution, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_set_reference_position(const struct device * dev, int32_t value);

__pinned_func
static inline int stepper_set_reference_position(const struct device * dev, int32_t value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t val; } parm1 = { .val = value };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_SET_REFERENCE_POSITION);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_set_reference_position(dev, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_set_reference_position(dev, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_SET_REFERENCE_POSITION, stepper_set_reference_position, dev, value); 	syscall__retval = stepper_set_reference_position(dev, value); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_SET_REFERENCE_POSITION, stepper_set_reference_position, dev, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_get_actual_position(const struct device * dev, int32_t * value);

__pinned_func
static inline int stepper_get_actual_position(const struct device * dev, int32_t * value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t * val; } parm1 = { .val = value };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_GET_ACTUAL_POSITION);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_get_actual_position(dev, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_get_actual_position(dev, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_GET_ACTUAL_POSITION, stepper_get_actual_position, dev, value); 	syscall__retval = stepper_get_actual_position(dev, value); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_GET_ACTUAL_POSITION, stepper_get_actual_position, dev, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_move_to(const struct device * dev, int32_t micro_steps);

__pinned_func
static inline int stepper_move_to(const struct device * dev, int32_t micro_steps)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t val; } parm1 = { .val = micro_steps };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_MOVE_TO);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_move_to(dev, micro_steps);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_move_to(dev, micro_steps) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_MOVE_TO, stepper_move_to, dev, micro_steps); 	syscall__retval = stepper_move_to(dev, micro_steps); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_MOVE_TO, stepper_move_to, dev, micro_steps, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_is_moving(const struct device * dev, bool * is_moving);

__pinned_func
static inline int stepper_is_moving(const struct device * dev, bool * is_moving)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool * val; } parm1 = { .val = is_moving };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_STEPPER_IS_MOVING);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_is_moving(dev, is_moving);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_is_moving(dev, is_moving) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_IS_MOVING, stepper_is_moving, dev, is_moving); 	syscall__retval = stepper_is_moving(dev, is_moving); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_IS_MOVING, stepper_is_moving, dev, is_moving, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_run(const struct device * dev, enum stepper_direction direction, uint32_t velocity);

__pinned_func
static inline int stepper_run(const struct device * dev, enum stepper_direction direction, uint32_t velocity)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum stepper_direction val; } parm1 = { .val = direction };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = velocity };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_STEPPER_RUN);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_run(dev, direction, velocity);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_run(dev, direction, velocity) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_RUN, stepper_run, dev, direction, velocity); 	syscall__retval = stepper_run(dev, direction, velocity); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_RUN, stepper_run, dev, direction, velocity, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_stepper_set_event_callback(const struct device * dev, stepper_event_callback_t callback, void * user_data);

__pinned_func
static inline int stepper_set_event_callback(const struct device * dev, stepper_event_callback_t callback, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; stepper_event_callback_t val; } parm1 = { .val = callback };
		union { uintptr_t x; void * val; } parm2 = { .val = user_data };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_STEPPER_SET_EVENT_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_stepper_set_event_callback(dev, callback, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define stepper_set_event_callback(dev, callback, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_STEPPER_SET_EVENT_CALLBACK, stepper_set_event_callback, dev, callback, user_data); 	syscall__retval = stepper_set_event_callback(dev, callback, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_STEPPER_SET_EVENT_CALLBACK, stepper_set_event_callback, dev, callback, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
