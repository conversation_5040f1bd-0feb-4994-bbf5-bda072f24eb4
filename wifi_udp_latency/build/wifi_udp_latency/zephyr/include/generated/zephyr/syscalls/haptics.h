/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_HAPTICS_H
#define Z_INCLUDE_SYSCALLS_HAPTICS_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_haptics_start_output(const struct device * dev);

__pinned_func
static inline int haptics_start_output(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_HAPTICS_START_OUTPUT);
	}
#endif
	compiler_barrier();
	return z_impl_haptics_start_output(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define haptics_start_output(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_HAPTICS_START_OUTPUT, haptics_start_output, dev); 	syscall__retval = haptics_start_output(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_HAPTICS_START_OUTPUT, haptics_start_output, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_haptics_stop_output(const struct device * dev);

__pinned_func
static inline int haptics_stop_output(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_HAPTICS_STOP_OUTPUT);
	}
#endif
	compiler_barrier();
	return z_impl_haptics_stop_output(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define haptics_stop_output(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_HAPTICS_STOP_OUTPUT, haptics_stop_output, dev); 	syscall__retval = haptics_stop_output(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_HAPTICS_STOP_OUTPUT, haptics_stop_output, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
