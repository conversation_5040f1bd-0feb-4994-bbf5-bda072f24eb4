/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_NRF_QSPI_NOR_H
#define Z_INCLUDE_SYSCALLS_NRF_QSPI_NOR_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern void z_impl_nrf_qspi_nor_xip_enable(const struct device * dev, bool enable);

__pinned_func
static inline void nrf_qspi_nor_xip_enable(const struct device * dev, bool enable)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool val; } parm1 = { .val = enable };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_NRF_QSPI_NOR_XIP_ENABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_nrf_qspi_nor_xip_enable(dev, enable);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define nrf_qspi_nor_xip_enable(dev, enable) do { 	sys_port_trace_syscall_enter(K_SYSCALL_NRF_QSPI_NOR_XIP_ENABLE, nrf_qspi_nor_xip_enable, dev, enable); 	nrf_qspi_nor_xip_enable(dev, enable); 	sys_port_trace_syscall_exit(K_SYSCALL_NRF_QSPI_NOR_XIP_ENABLE, nrf_qspi_nor_xip_enable, dev, enable); } while(false)
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
