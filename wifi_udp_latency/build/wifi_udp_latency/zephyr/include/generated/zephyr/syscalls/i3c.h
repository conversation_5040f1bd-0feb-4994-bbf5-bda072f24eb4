/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_I3C_H
#define Z_INCLUDE_SYSCALLS_I3C_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_i3c_do_ccc(const struct device * dev, struct i3c_ccc_payload * payload);

__pinned_func
static inline int i3c_do_ccc(const struct device * dev, struct i3c_ccc_payload * payload)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct i3c_ccc_payload * val; } parm1 = { .val = payload };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_I3C_DO_CCC);
	}
#endif
	compiler_barrier();
	return z_impl_i3c_do_ccc(dev, payload);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define i3c_do_ccc(dev, payload) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_I3C_DO_CCC, i3c_do_ccc, dev, payload); 	syscall__retval = i3c_do_ccc(dev, payload); 	sys_port_trace_syscall_exit(K_SYSCALL_I3C_DO_CCC, i3c_do_ccc, dev, payload, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_i3c_transfer(struct i3c_device_desc * target, struct i3c_msg * msgs, uint8_t num_msgs);

__pinned_func
static inline int i3c_transfer(struct i3c_device_desc * target, struct i3c_msg * msgs, uint8_t num_msgs)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct i3c_device_desc * val; } parm0 = { .val = target };
		union { uintptr_t x; struct i3c_msg * val; } parm1 = { .val = msgs };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = num_msgs };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_I3C_TRANSFER);
	}
#endif
	compiler_barrier();
	return z_impl_i3c_transfer(target, msgs, num_msgs);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define i3c_transfer(target, msgs, num_msgs) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_I3C_TRANSFER, i3c_transfer, target, msgs, num_msgs); 	syscall__retval = i3c_transfer(target, msgs, num_msgs); 	sys_port_trace_syscall_exit(K_SYSCALL_I3C_TRANSFER, i3c_transfer, target, msgs, num_msgs, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
