/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_UART_H
#define Z_INCLUDE_SYSCALLS_UART_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_uart_err_check(const struct device * dev);

__pinned_func
static inline int uart_err_check(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_ERR_CHECK);
	}
#endif
	compiler_barrier();
	return z_impl_uart_err_check(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_err_check(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_ERR_CHECK, uart_err_check, dev); 	syscall__retval = uart_err_check(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_ERR_CHECK, uart_err_check, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_poll_in(const struct device * dev, unsigned char * p_char);

__pinned_func
static inline int uart_poll_in(const struct device * dev, unsigned char * p_char)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; unsigned char * val; } parm1 = { .val = p_char };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_POLL_IN);
	}
#endif
	compiler_barrier();
	return z_impl_uart_poll_in(dev, p_char);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_poll_in(dev, p_char) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_POLL_IN, uart_poll_in, dev, p_char); 	syscall__retval = uart_poll_in(dev, p_char); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_POLL_IN, uart_poll_in, dev, p_char, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_poll_in_u16(const struct device * dev, uint16_t * p_u16);

__pinned_func
static inline int uart_poll_in_u16(const struct device * dev, uint16_t * p_u16)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t * val; } parm1 = { .val = p_u16 };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_POLL_IN_U16);
	}
#endif
	compiler_barrier();
	return z_impl_uart_poll_in_u16(dev, p_u16);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_poll_in_u16(dev, p_u16) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_POLL_IN_U16, uart_poll_in_u16, dev, p_u16); 	syscall__retval = uart_poll_in_u16(dev, p_u16); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_POLL_IN_U16, uart_poll_in_u16, dev, p_u16, syscall__retval); 	syscall__retval; })
#endif
#endif


extern void z_impl_uart_poll_out(const struct device * dev, unsigned char out_char);

__pinned_func
static inline void uart_poll_out(const struct device * dev, unsigned char out_char)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; unsigned char val; } parm1 = { .val = out_char };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_POLL_OUT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_poll_out(dev, out_char);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_poll_out(dev, out_char) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_POLL_OUT, uart_poll_out, dev, out_char); 	uart_poll_out(dev, out_char); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_POLL_OUT, uart_poll_out, dev, out_char); } while(false)
#endif
#endif


extern void z_impl_uart_poll_out_u16(const struct device * dev, uint16_t out_u16);

__pinned_func
static inline void uart_poll_out_u16(const struct device * dev, uint16_t out_u16)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = out_u16 };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_POLL_OUT_U16);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_poll_out_u16(dev, out_u16);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_poll_out_u16(dev, out_u16) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_POLL_OUT_U16, uart_poll_out_u16, dev, out_u16); 	uart_poll_out_u16(dev, out_u16); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_POLL_OUT_U16, uart_poll_out_u16, dev, out_u16); } while(false)
#endif
#endif


extern int z_impl_uart_configure(const struct device * dev, const struct uart_config * cfg);

__pinned_func
static inline int uart_configure(const struct device * dev, const struct uart_config * cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct uart_config * val; } parm1 = { .val = cfg };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_CONFIGURE);
	}
#endif
	compiler_barrier();
	return z_impl_uart_configure(dev, cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_configure(dev, cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_CONFIGURE, uart_configure, dev, cfg); 	syscall__retval = uart_configure(dev, cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_CONFIGURE, uart_configure, dev, cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_config_get(const struct device * dev, struct uart_config * cfg);

__pinned_func
static inline int uart_config_get(const struct device * dev, struct uart_config * cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct uart_config * val; } parm1 = { .val = cfg };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_UART_CONFIG_GET);
	}
#endif
	compiler_barrier();
	return z_impl_uart_config_get(dev, cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_config_get(dev, cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_CONFIG_GET, uart_config_get, dev, cfg); 	syscall__retval = uart_config_get(dev, cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_CONFIG_GET, uart_config_get, dev, cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern void z_impl_uart_irq_tx_enable(const struct device * dev);

__pinned_func
static inline void uart_irq_tx_enable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_TX_ENABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_tx_enable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_tx_enable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_TX_ENABLE, uart_irq_tx_enable, dev); 	uart_irq_tx_enable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_TX_ENABLE, uart_irq_tx_enable, dev); } while(false)
#endif
#endif


extern void z_impl_uart_irq_tx_disable(const struct device * dev);

__pinned_func
static inline void uart_irq_tx_disable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_TX_DISABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_tx_disable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_tx_disable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_TX_DISABLE, uart_irq_tx_disable, dev); 	uart_irq_tx_disable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_TX_DISABLE, uart_irq_tx_disable, dev); } while(false)
#endif
#endif


extern void z_impl_uart_irq_rx_enable(const struct device * dev);

__pinned_func
static inline void uart_irq_rx_enable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_RX_ENABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_rx_enable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_rx_enable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_RX_ENABLE, uart_irq_rx_enable, dev); 	uart_irq_rx_enable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_RX_ENABLE, uart_irq_rx_enable, dev); } while(false)
#endif
#endif


extern void z_impl_uart_irq_rx_disable(const struct device * dev);

__pinned_func
static inline void uart_irq_rx_disable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_RX_DISABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_rx_disable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_rx_disable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_RX_DISABLE, uart_irq_rx_disable, dev); 	uart_irq_rx_disable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_RX_DISABLE, uart_irq_rx_disable, dev); } while(false)
#endif
#endif


extern void z_impl_uart_irq_err_enable(const struct device * dev);

__pinned_func
static inline void uart_irq_err_enable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_ERR_ENABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_err_enable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_err_enable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_ERR_ENABLE, uart_irq_err_enable, dev); 	uart_irq_err_enable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_ERR_ENABLE, uart_irq_err_enable, dev); } while(false)
#endif
#endif


extern void z_impl_uart_irq_err_disable(const struct device * dev);

__pinned_func
static inline void uart_irq_err_disable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_ERR_DISABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_uart_irq_err_disable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_err_disable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_ERR_DISABLE, uart_irq_err_disable, dev); 	uart_irq_err_disable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_ERR_DISABLE, uart_irq_err_disable, dev); } while(false)
#endif
#endif


extern int z_impl_uart_irq_is_pending(const struct device * dev);

__pinned_func
static inline int uart_irq_is_pending(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_IS_PENDING);
	}
#endif
	compiler_barrier();
	return z_impl_uart_irq_is_pending(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_is_pending(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_IS_PENDING, uart_irq_is_pending, dev); 	syscall__retval = uart_irq_is_pending(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_IS_PENDING, uart_irq_is_pending, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_irq_update(const struct device * dev);

__pinned_func
static inline int uart_irq_update(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_IRQ_UPDATE);
	}
#endif
	compiler_barrier();
	return z_impl_uart_irq_update(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_irq_update(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_IRQ_UPDATE, uart_irq_update, dev); 	syscall__retval = uart_irq_update(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_IRQ_UPDATE, uart_irq_update, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_tx(const struct device * dev, const uint8_t * buf, size_t len, int32_t timeout);

__pinned_func
static inline int uart_tx(const struct device * dev, const uint8_t * buf, size_t len, int32_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const uint8_t * val; } parm1 = { .val = buf };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		union { uintptr_t x; int32_t val; } parm3 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_UART_TX);
	}
#endif
	compiler_barrier();
	return z_impl_uart_tx(dev, buf, len, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_tx(dev, buf, len, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_TX, uart_tx, dev, buf, len, timeout); 	syscall__retval = uart_tx(dev, buf, len, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_TX, uart_tx, dev, buf, len, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_tx_u16(const struct device * dev, const uint16_t * buf, size_t len, int32_t timeout);

__pinned_func
static inline int uart_tx_u16(const struct device * dev, const uint16_t * buf, size_t len, int32_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const uint16_t * val; } parm1 = { .val = buf };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		union { uintptr_t x; int32_t val; } parm3 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_UART_TX_U16);
	}
#endif
	compiler_barrier();
	return z_impl_uart_tx_u16(dev, buf, len, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_tx_u16(dev, buf, len, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_TX_U16, uart_tx_u16, dev, buf, len, timeout); 	syscall__retval = uart_tx_u16(dev, buf, len, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_TX_U16, uart_tx_u16, dev, buf, len, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_tx_abort(const struct device * dev);

__pinned_func
static inline int uart_tx_abort(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_TX_ABORT);
	}
#endif
	compiler_barrier();
	return z_impl_uart_tx_abort(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_tx_abort(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_TX_ABORT, uart_tx_abort, dev); 	syscall__retval = uart_tx_abort(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_TX_ABORT, uart_tx_abort, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_rx_enable(const struct device * dev, uint8_t * buf, size_t len, int32_t timeout);

__pinned_func
static inline int uart_rx_enable(const struct device * dev, uint8_t * buf, size_t len, int32_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = buf };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		union { uintptr_t x; int32_t val; } parm3 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_UART_RX_ENABLE);
	}
#endif
	compiler_barrier();
	return z_impl_uart_rx_enable(dev, buf, len, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_rx_enable(dev, buf, len, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_RX_ENABLE, uart_rx_enable, dev, buf, len, timeout); 	syscall__retval = uart_rx_enable(dev, buf, len, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_RX_ENABLE, uart_rx_enable, dev, buf, len, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_rx_enable_u16(const struct device * dev, uint16_t * buf, size_t len, int32_t timeout);

__pinned_func
static inline int uart_rx_enable_u16(const struct device * dev, uint16_t * buf, size_t len, int32_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t * val; } parm1 = { .val = buf };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		union { uintptr_t x; int32_t val; } parm3 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_UART_RX_ENABLE_U16);
	}
#endif
	compiler_barrier();
	return z_impl_uart_rx_enable_u16(dev, buf, len, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_rx_enable_u16(dev, buf, len, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_RX_ENABLE_U16, uart_rx_enable_u16, dev, buf, len, timeout); 	syscall__retval = uart_rx_enable_u16(dev, buf, len, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_RX_ENABLE_U16, uart_rx_enable_u16, dev, buf, len, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_rx_disable(const struct device * dev);

__pinned_func
static inline int uart_rx_disable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_UART_RX_DISABLE);
	}
#endif
	compiler_barrier();
	return z_impl_uart_rx_disable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_rx_disable(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_RX_DISABLE, uart_rx_disable, dev); 	syscall__retval = uart_rx_disable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_RX_DISABLE, uart_rx_disable, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_line_ctrl_set(const struct device * dev, uint32_t ctrl, uint32_t val);

__pinned_func
static inline int uart_line_ctrl_set(const struct device * dev, uint32_t ctrl, uint32_t val)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = ctrl };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = val };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_UART_LINE_CTRL_SET);
	}
#endif
	compiler_barrier();
	return z_impl_uart_line_ctrl_set(dev, ctrl, val);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_line_ctrl_set(dev, ctrl, val) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_LINE_CTRL_SET, uart_line_ctrl_set, dev, ctrl, val); 	syscall__retval = uart_line_ctrl_set(dev, ctrl, val); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_LINE_CTRL_SET, uart_line_ctrl_set, dev, ctrl, val, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_line_ctrl_get(const struct device * dev, uint32_t ctrl, uint32_t * val);

__pinned_func
static inline int uart_line_ctrl_get(const struct device * dev, uint32_t ctrl, uint32_t * val)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = ctrl };
		union { uintptr_t x; uint32_t * val; } parm2 = { .val = val };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_UART_LINE_CTRL_GET);
	}
#endif
	compiler_barrier();
	return z_impl_uart_line_ctrl_get(dev, ctrl, val);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_line_ctrl_get(dev, ctrl, val) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_LINE_CTRL_GET, uart_line_ctrl_get, dev, ctrl, val); 	syscall__retval = uart_line_ctrl_get(dev, ctrl, val); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_LINE_CTRL_GET, uart_line_ctrl_get, dev, ctrl, val, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_uart_drv_cmd(const struct device * dev, uint32_t cmd, uint32_t p);

__pinned_func
static inline int uart_drv_cmd(const struct device * dev, uint32_t cmd, uint32_t p)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = cmd };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = p };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_UART_DRV_CMD);
	}
#endif
	compiler_barrier();
	return z_impl_uart_drv_cmd(dev, cmd, p);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define uart_drv_cmd(dev, cmd, p) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_UART_DRV_CMD, uart_drv_cmd, dev, cmd, p); 	syscall__retval = uart_drv_cmd(dev, cmd, p); 	sys_port_trace_syscall_exit(K_SYSCALL_UART_DRV_CMD, uart_drv_cmd, dev, cmd, p, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
