/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_ESPI_SAF_H
#define Z_INCLUDE_SYSCALLS_ESPI_SAF_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_espi_saf_config(const struct device * dev, const struct espi_saf_cfg * cfg);

__pinned_func
static inline int espi_saf_config(const struct device * dev, const struct espi_saf_cfg * cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct espi_saf_cfg * val; } parm1 = { .val = cfg };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_CONFIG);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_config(dev, cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_config(dev, cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_CONFIG, espi_saf_config, dev, cfg); 	syscall__retval = espi_saf_config(dev, cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_CONFIG, espi_saf_config, dev, cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_set_protection_regions(const struct device * dev, const struct espi_saf_protection * pr);

__pinned_func
static inline int espi_saf_set_protection_regions(const struct device * dev, const struct espi_saf_protection * pr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct espi_saf_protection * val; } parm1 = { .val = pr };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_SET_PROTECTION_REGIONS);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_set_protection_regions(dev, pr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_set_protection_regions(dev, pr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_SET_PROTECTION_REGIONS, espi_saf_set_protection_regions, dev, pr); 	syscall__retval = espi_saf_set_protection_regions(dev, pr); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_SET_PROTECTION_REGIONS, espi_saf_set_protection_regions, dev, pr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_activate(const struct device * dev);

__pinned_func
static inline int espi_saf_activate(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_ESPI_SAF_ACTIVATE);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_activate(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_activate(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_ACTIVATE, espi_saf_activate, dev); 	syscall__retval = espi_saf_activate(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_ACTIVATE, espi_saf_activate, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_espi_saf_get_channel_status(const struct device * dev);

__pinned_func
static inline bool espi_saf_get_channel_status(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (bool) arch_syscall_invoke1(parm0.x, K_SYSCALL_ESPI_SAF_GET_CHANNEL_STATUS);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_get_channel_status(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_get_channel_status(dev) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_GET_CHANNEL_STATUS, espi_saf_get_channel_status, dev); 	syscall__retval = espi_saf_get_channel_status(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_GET_CHANNEL_STATUS, espi_saf_get_channel_status, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_flash_read(const struct device * dev, struct espi_saf_packet * pckt);

__pinned_func
static inline int espi_saf_flash_read(const struct device * dev, struct espi_saf_packet * pckt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct espi_saf_packet * val; } parm1 = { .val = pckt };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_FLASH_READ);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_flash_read(dev, pckt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_flash_read(dev, pckt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_FLASH_READ, espi_saf_flash_read, dev, pckt); 	syscall__retval = espi_saf_flash_read(dev, pckt); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_FLASH_READ, espi_saf_flash_read, dev, pckt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_flash_write(const struct device * dev, struct espi_saf_packet * pckt);

__pinned_func
static inline int espi_saf_flash_write(const struct device * dev, struct espi_saf_packet * pckt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct espi_saf_packet * val; } parm1 = { .val = pckt };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_FLASH_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_flash_write(dev, pckt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_flash_write(dev, pckt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_FLASH_WRITE, espi_saf_flash_write, dev, pckt); 	syscall__retval = espi_saf_flash_write(dev, pckt); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_FLASH_WRITE, espi_saf_flash_write, dev, pckt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_flash_erase(const struct device * dev, struct espi_saf_packet * pckt);

__pinned_func
static inline int espi_saf_flash_erase(const struct device * dev, struct espi_saf_packet * pckt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct espi_saf_packet * val; } parm1 = { .val = pckt };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_FLASH_ERASE);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_flash_erase(dev, pckt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_flash_erase(dev, pckt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_FLASH_ERASE, espi_saf_flash_erase, dev, pckt); 	syscall__retval = espi_saf_flash_erase(dev, pckt); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_FLASH_ERASE, espi_saf_flash_erase, dev, pckt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_espi_saf_flash_unsuccess(const struct device * dev, struct espi_saf_packet * pckt);

__pinned_func
static inline int espi_saf_flash_unsuccess(const struct device * dev, struct espi_saf_packet * pckt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct espi_saf_packet * val; } parm1 = { .val = pckt };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ESPI_SAF_FLASH_UNSUCCESS);
	}
#endif
	compiler_barrier();
	return z_impl_espi_saf_flash_unsuccess(dev, pckt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define espi_saf_flash_unsuccess(dev, pckt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ESPI_SAF_FLASH_UNSUCCESS, espi_saf_flash_unsuccess, dev, pckt); 	syscall__retval = espi_saf_flash_unsuccess(dev, pckt); 	sys_port_trace_syscall_exit(K_SYSCALL_ESPI_SAF_FLASH_UNSUCCESS, espi_saf_flash_unsuccess, dev, pckt, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
