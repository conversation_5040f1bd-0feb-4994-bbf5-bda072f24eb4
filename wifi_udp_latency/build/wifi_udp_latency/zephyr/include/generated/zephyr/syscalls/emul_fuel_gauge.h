/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_EMUL_FUEL_GAUGE_H
#define Z_INCLUDE_SYSCALLS_EMUL_FUEL_GAUGE_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_emul_fuel_gauge_set_battery_charging(const struct emul * target, uint32_t uV, int uA);

__pinned_func
static inline int emul_fuel_gauge_set_battery_charging(const struct emul * target, uint32_t uV, int uA)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct emul * val; } parm0 = { .val = target };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = uV };
		union { uintptr_t x; int val; } parm2 = { .val = uA };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_EMUL_FUEL_GAUGE_SET_BATTERY_CHARGING);
	}
#endif
	compiler_barrier();
	return z_impl_emul_fuel_gauge_set_battery_charging(target, uV, uA);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define emul_fuel_gauge_set_battery_charging(target, uV, uA) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_EMUL_FUEL_GAUGE_SET_BATTERY_CHARGING, emul_fuel_gauge_set_battery_charging, target, uV, uA); 	syscall__retval = emul_fuel_gauge_set_battery_charging(target, uV, uA); 	sys_port_trace_syscall_exit(K_SYSCALL_EMUL_FUEL_GAUGE_SET_BATTERY_CHARGING, emul_fuel_gauge_set_battery_charging, target, uV, uA, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_emul_fuel_gauge_is_battery_cutoff(const struct emul * target, bool * cutoff);

__pinned_func
static inline int emul_fuel_gauge_is_battery_cutoff(const struct emul * target, bool * cutoff)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct emul * val; } parm0 = { .val = target };
		union { uintptr_t x; bool * val; } parm1 = { .val = cutoff };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_EMUL_FUEL_GAUGE_IS_BATTERY_CUTOFF);
	}
#endif
	compiler_barrier();
	return z_impl_emul_fuel_gauge_is_battery_cutoff(target, cutoff);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define emul_fuel_gauge_is_battery_cutoff(target, cutoff) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_EMUL_FUEL_GAUGE_IS_BATTERY_CUTOFF, emul_fuel_gauge_is_battery_cutoff, target, cutoff); 	syscall__retval = emul_fuel_gauge_is_battery_cutoff(target, cutoff); 	sys_port_trace_syscall_exit(K_SYSCALL_EMUL_FUEL_GAUGE_IS_BATTERY_CUTOFF, emul_fuel_gauge_is_battery_cutoff, target, cutoff, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
