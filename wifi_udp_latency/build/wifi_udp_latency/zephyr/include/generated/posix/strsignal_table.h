
/*
 * This file is generated by /opt/nordic/ncs/v3.0.2/zephyr/scripts/build/gen_strsignal_table.py
 */

#include <zephyr/posix/signal.h>

static const char *const strsignal_list[32] = {
	[SIGHUP] = "Hangup",
	[SIGINT] = "Interrupt",
	[SIGQUIT] = "Quit",
	[SIGILL] = "Illegal instruction",
	[SIGTRAP] = "Trace/breakpoint trap",
	[SIGABRT] = "Aborted",
	[SIGBUS] = "Bus error",
	[SIGFPE] = "Arithmetic exception",
	[SIGKILL] = "Killed",
	[SIGSEGV] = "Invalid memory reference",
	[SIGPIPE] = "Broken pipe",
	[SIGALRM] = "Alarm clock",
	[SIGTERM] = "Terminated",
	[SIGCHLD] = "Child status changed",
	[SIGCONT] = "Continued",
	[SIGSTOP] = "Stop executing",
	[SIGTSTP] = "Stopped",
	[SIGTTIN] = "Stopped (read)",
	[SIGTTOU] = "Stopped (write)",
	[SIGURG] = "Urgent I/O condition",
	[SIGXCPU] = "CPU time limit exceeded",
	[SIGXFSZ] = "File size limit exceeded",
	[SIGVTALRM] = "Virtual timer expired",
	[SIGPROF] = "Profiling timer expired",
	[SIGPOLL] = "Pollable event occurred",
	[SIGSYS] = "Bad system call",
};
