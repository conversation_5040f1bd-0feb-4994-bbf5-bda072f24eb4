/* File generated by /opt/nordic/ncs/v3.0.2/nrf/scripts/partition_manager_output.py, do not modify */
#ifndef PM_CONFIG_H__
#define PM_CONFIG_H__
#define PM_APP_OFFSET 0x0
#define PM_APP_ADDRESS 0x0
#define PM_APP_END_ADDRESS 0xf0000
#define PM_APP_SIZE 0xf0000
#define PM_APP_NAME app
#define PM_APP_ID 0
#define PM_app_ID PM_APP_ID
#define PM_app_IS_ENABLED 1
#define PM_0_LABEL APP
#define PM_APP_DEV flash_controller
#define PM_APP_DEFAULT_DRIVER_KCONFIG CONFIG_SOC_FLASH_NRF
#define PM_LITTLEFS_STORAGE_OFFSET 0xf0000
#define PM_LITTLEFS_STORAGE_ADDRESS 0xf0000
#define PM_LITTLEFS_STORAGE_END_ADDRESS 0xf6000
#define PM_LITTLEFS_STORAGE_SIZE 0x6000
#define PM_LITTLEFS_STORAGE_NAME littlefs_storage
#define PM_LITTLEFS_STORAGE_ID 1
#define PM_littlefs_storage_ID PM_LITTLEFS_STORAGE_ID
#define PM_littlefs_storage_IS_ENABLED 1
#define PM_1_LABEL LITTLEFS_STORAGE
#define PM_LITTLEFS_STORAGE_DEV flash_controller
#define PM_LITTLEFS_STORAGE_DEFAULT_DRIVER_KCONFIG CONFIG_SOC_FLASH_NRF
#define PM_NVS_STORAGE_OFFSET 0xf6000
#define PM_NVS_STORAGE_ADDRESS 0xf6000
#define PM_NVS_STORAGE_END_ADDRESS 0xfc000
#define PM_NVS_STORAGE_SIZE 0x6000
#define PM_NVS_STORAGE_NAME nvs_storage
#define PM_NVS_STORAGE_ID 2
#define PM_nvs_storage_ID PM_NVS_STORAGE_ID
#define PM_nvs_storage_IS_ENABLED 1
#define PM_2_LABEL NVS_STORAGE
#define PM_NVS_STORAGE_DEV flash_controller
#define PM_NVS_STORAGE_DEFAULT_DRIVER_KCONFIG CONFIG_SOC_FLASH_NRF
#define PM_SETTINGS_STORAGE_OFFSET 0xfc000
#define PM_SETTINGS_STORAGE_ADDRESS 0xfc000
#define PM_SETTINGS_STORAGE_END_ADDRESS 0xfe000
#define PM_SETTINGS_STORAGE_SIZE 0x2000
#define PM_SETTINGS_STORAGE_NAME settings_storage
#define PM_SETTINGS_STORAGE_ID 3
#define PM_settings_storage_ID PM_SETTINGS_STORAGE_ID
#define PM_settings_storage_IS_ENABLED 1
#define PM_3_LABEL SETTINGS_STORAGE
#define PM_SETTINGS_STORAGE_DEV flash_controller
#define PM_SETTINGS_STORAGE_DEFAULT_DRIVER_KCONFIG CONFIG_SOC_FLASH_NRF
#define PM_EMPTY_0_OFFSET 0xfe000
#define PM_EMPTY_0_ADDRESS 0xfe000
#define PM_EMPTY_0_END_ADDRESS 0x100000
#define PM_EMPTY_0_SIZE 0x2000
#define PM_EMPTY_0_NAME EMPTY_0
#define PM_EMPTY_0_ID 4
#define PM_empty_0_ID PM_EMPTY_0_ID
#define PM_empty_0_IS_ENABLED 1
#define PM_4_LABEL EMPTY_0
#define PM_EMPTY_0_DEV flash_controller
#define PM_EMPTY_0_DEFAULT_DRIVER_KCONFIG CONFIG_SOC_FLASH_NRF
#define PM_OTP_OFFSET 0x0
#define PM_OTP_ADDRESS 0xff8100
#define PM_OTP_END_ADDRESS 0xff83fc
#define PM_OTP_SIZE 0x2fc
#define PM_OTP_NAME otp
#define PM_SRAM_PRIMARY_OFFSET 0x0
#define PM_SRAM_PRIMARY_ADDRESS 0x20000000
#define PM_SRAM_PRIMARY_END_ADDRESS 0x20070000
#define PM_SRAM_PRIMARY_SIZE 0x70000
#define PM_SRAM_PRIMARY_NAME sram_primary
#define PM_RPMSG_NRF53_SRAM_OFFSET 0x70000
#define PM_RPMSG_NRF53_SRAM_ADDRESS 0x20070000
#define PM_RPMSG_NRF53_SRAM_END_ADDRESS 0x20080000
#define PM_RPMSG_NRF53_SRAM_SIZE 0x10000
#define PM_RPMSG_NRF53_SRAM_NAME rpmsg_nrf53_sram
#define PM_NUM 5
#define PM_ALL_BY_SIZE "otp EMPTY_0 settings_storage littlefs_storage nvs_storage rpmsg_nrf53_sram sram_primary app"
#define PM_ADDRESS 0x0
#define PM_SIZE 0xf0000
#define PM_SRAM_ADDRESS 0x20000000
#define PM_SRAM_SIZE 0x70000
#endif /* PM_CONFIG_H__ */