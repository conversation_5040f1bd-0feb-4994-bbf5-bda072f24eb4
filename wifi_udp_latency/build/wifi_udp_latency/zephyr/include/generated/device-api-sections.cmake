list(APPEND sections "{NAME\;entropy_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;entropy_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._entropy_driver_api.static.*\;SYMBOLS\;_entropy_driver_api_list_start\;_entropy_driver_api_list_end}")
list(APPEND sections "{NAME\;flash_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;flash_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._flash_driver_api.static.*\;SYMBOLS\;_flash_driver_api_list_start\;_flash_driver_api_list_end}")
list(APPEND sections "{NAME\;gpio_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;gpio_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._gpio_driver_api.static.*\;SYMBOLS\;_gpio_driver_api_list_start\;_gpio_driver_api_list_end}")
list(APPEND sections "{NAME\;spi_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;spi_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._spi_driver_api.static.*\;SYMBOLS\;_spi_driver_api_list_start\;_spi_driver_api_list_end}")
list(APPEND sections "{NAME\;shared_irq_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;shared_irq_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._shared_irq_driver_api.static.*\;SYMBOLS\;_shared_irq_driver_api_list_start\;_shared_irq_driver_api_list_end}")
list(APPEND sections "{NAME\;crypto_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;crypto_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._crypto_driver_api.static.*\;SYMBOLS\;_crypto_driver_api_list_start\;_crypto_driver_api_list_end}")
list(APPEND sections "{NAME\;adc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;adc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._adc_driver_api.static.*\;SYMBOLS\;_adc_driver_api_list_start\;_adc_driver_api_list_end}")
list(APPEND sections "{NAME\;auxdisplay_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;auxdisplay_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._auxdisplay_driver_api.static.*\;SYMBOLS\;_auxdisplay_driver_api_list_start\;_auxdisplay_driver_api_list_end}")
list(APPEND sections "{NAME\;bbram_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;bbram_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._bbram_driver_api.static.*\;SYMBOLS\;_bbram_driver_api_list_start\;_bbram_driver_api_list_end}")
list(APPEND sections "{NAME\;bt_hci_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;bt_hci_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._bt_hci_driver_api.static.*\;SYMBOLS\;_bt_hci_driver_api_list_start\;_bt_hci_driver_api_list_end}")
list(APPEND sections "{NAME\;can_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;can_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._can_driver_api.static.*\;SYMBOLS\;_can_driver_api_list_start\;_can_driver_api_list_end}")
list(APPEND sections "{NAME\;cellular_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;cellular_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._cellular_driver_api.static.*\;SYMBOLS\;_cellular_driver_api_list_start\;_cellular_driver_api_list_end}")
list(APPEND sections "{NAME\;charger_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;charger_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._charger_driver_api.static.*\;SYMBOLS\;_charger_driver_api_list_start\;_charger_driver_api_list_end}")
list(APPEND sections "{NAME\;clock_control_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;clock_control_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._clock_control_driver_api.static.*\;SYMBOLS\;_clock_control_driver_api_list_start\;_clock_control_driver_api_list_end}")
list(APPEND sections "{NAME\;comparator_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;comparator_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._comparator_driver_api.static.*\;SYMBOLS\;_comparator_driver_api_list_start\;_comparator_driver_api_list_end}")
list(APPEND sections "{NAME\;coredump_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;coredump_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._coredump_driver_api.static.*\;SYMBOLS\;_coredump_driver_api_list_start\;_coredump_driver_api_list_end}")
list(APPEND sections "{NAME\;counter_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;counter_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._counter_driver_api.static.*\;SYMBOLS\;_counter_driver_api_list_start\;_counter_driver_api_list_end}")
list(APPEND sections "{NAME\;dac_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;dac_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._dac_driver_api.static.*\;SYMBOLS\;_dac_driver_api_list_start\;_dac_driver_api_list_end}")
list(APPEND sections "{NAME\;dai_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;dai_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._dai_driver_api.static.*\;SYMBOLS\;_dai_driver_api_list_start\;_dai_driver_api_list_end}")
list(APPEND sections "{NAME\;display_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;display_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._display_driver_api.static.*\;SYMBOLS\;_display_driver_api_list_start\;_display_driver_api_list_end}")
list(APPEND sections "{NAME\;dma_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;dma_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._dma_driver_api.static.*\;SYMBOLS\;_dma_driver_api_list_start\;_dma_driver_api_list_end}")
list(APPEND sections "{NAME\;edac_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;edac_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._edac_driver_api.static.*\;SYMBOLS\;_edac_driver_api_list_start\;_edac_driver_api_list_end}")
list(APPEND sections "{NAME\;eeprom_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;eeprom_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._eeprom_driver_api.static.*\;SYMBOLS\;_eeprom_driver_api_list_start\;_eeprom_driver_api_list_end}")
list(APPEND sections "{NAME\;emul_bbram_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;emul_bbram_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._emul_bbram_driver_api.static.*\;SYMBOLS\;_emul_bbram_driver_api_list_start\;_emul_bbram_driver_api_list_end}")
list(APPEND sections "{NAME\;fuel_gauge_emul_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;fuel_gauge_emul_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._fuel_gauge_emul_driver_api.static.*\;SYMBOLS\;_fuel_gauge_emul_driver_api_list_start\;_fuel_gauge_emul_driver_api_list_end}")
list(APPEND sections "{NAME\;emul_sensor_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;emul_sensor_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._emul_sensor_driver_api.static.*\;SYMBOLS\;_emul_sensor_driver_api_list_start\;_emul_sensor_driver_api_list_end}")
list(APPEND sections "{NAME\;espi_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;espi_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._espi_driver_api.static.*\;SYMBOLS\;_espi_driver_api_list_start\;_espi_driver_api_list_end}")
list(APPEND sections "{NAME\;espi_saf_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;espi_saf_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._espi_saf_driver_api.static.*\;SYMBOLS\;_espi_saf_driver_api_list_start\;_espi_saf_driver_api_list_end}")
list(APPEND sections "{NAME\;fpga_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;fpga_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._fpga_driver_api.static.*\;SYMBOLS\;_fpga_driver_api_list_start\;_fpga_driver_api_list_end}")
list(APPEND sections "{NAME\;fuel_gauge_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;fuel_gauge_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._fuel_gauge_driver_api.static.*\;SYMBOLS\;_fuel_gauge_driver_api_list_start\;_fuel_gauge_driver_api_list_end}")
list(APPEND sections "{NAME\;gnss_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;gnss_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._gnss_driver_api.static.*\;SYMBOLS\;_gnss_driver_api_list_start\;_gnss_driver_api_list_end}")
list(APPEND sections "{NAME\;haptics_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;haptics_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._haptics_driver_api.static.*\;SYMBOLS\;_haptics_driver_api_list_start\;_haptics_driver_api_list_end}")
list(APPEND sections "{NAME\;hwspinlock_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;hwspinlock_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._hwspinlock_driver_api.static.*\;SYMBOLS\;_hwspinlock_driver_api_list_start\;_hwspinlock_driver_api_list_end}")
list(APPEND sections "{NAME\;i2c_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;i2c_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._i2c_driver_api.static.*\;SYMBOLS\;_i2c_driver_api_list_start\;_i2c_driver_api_list_end}")
list(APPEND sections "{NAME\;i2c_target_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;i2c_target_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._i2c_target_driver_api.static.*\;SYMBOLS\;_i2c_target_driver_api_list_start\;_i2c_target_driver_api_list_end}")
list(APPEND sections "{NAME\;i2s_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;i2s_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._i2s_driver_api.static.*\;SYMBOLS\;_i2s_driver_api_list_start\;_i2s_driver_api_list_end}")
list(APPEND sections "{NAME\;i3c_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;i3c_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._i3c_driver_api.static.*\;SYMBOLS\;_i3c_driver_api_list_start\;_i3c_driver_api_list_end}")
list(APPEND sections "{NAME\;ipm_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;ipm_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._ipm_driver_api.static.*\;SYMBOLS\;_ipm_driver_api_list_start\;_ipm_driver_api_list_end}")
list(APPEND sections "{NAME\;kscan_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;kscan_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._kscan_driver_api.static.*\;SYMBOLS\;_kscan_driver_api_list_start\;_kscan_driver_api_list_end}")
list(APPEND sections "{NAME\;led_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;led_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._led_driver_api.static.*\;SYMBOLS\;_led_driver_api_list_start\;_led_driver_api_list_end}")
list(APPEND sections "{NAME\;led_strip_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;led_strip_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._led_strip_driver_api.static.*\;SYMBOLS\;_led_strip_driver_api_list_start\;_led_strip_driver_api_list_end}")
list(APPEND sections "{NAME\;lora_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;lora_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._lora_driver_api.static.*\;SYMBOLS\;_lora_driver_api_list_start\;_lora_driver_api_list_end}")
list(APPEND sections "{NAME\;mbox_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;mbox_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._mbox_driver_api.static.*\;SYMBOLS\;_mbox_driver_api_list_start\;_mbox_driver_api_list_end}")
list(APPEND sections "{NAME\;mdio_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;mdio_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._mdio_driver_api.static.*\;SYMBOLS\;_mdio_driver_api_list_start\;_mdio_driver_api_list_end}")
list(APPEND sections "{NAME\;mipi_dbi_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;mipi_dbi_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._mipi_dbi_driver_api.static.*\;SYMBOLS\;_mipi_dbi_driver_api_list_start\;_mipi_dbi_driver_api_list_end}")
list(APPEND sections "{NAME\;mipi_dsi_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;mipi_dsi_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._mipi_dsi_driver_api.static.*\;SYMBOLS\;_mipi_dsi_driver_api_list_start\;_mipi_dsi_driver_api_list_end}")
list(APPEND sections "{NAME\;mspi_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;mspi_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._mspi_driver_api.static.*\;SYMBOLS\;_mspi_driver_api_list_start\;_mspi_driver_api_list_end}")
list(APPEND sections "{NAME\;peci_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;peci_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._peci_driver_api.static.*\;SYMBOLS\;_peci_driver_api_list_start\;_peci_driver_api_list_end}")
list(APPEND sections "{NAME\;ps2_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;ps2_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._ps2_driver_api.static.*\;SYMBOLS\;_ps2_driver_api_list_start\;_ps2_driver_api_list_end}")
list(APPEND sections "{NAME\;ptp_clock_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;ptp_clock_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._ptp_clock_driver_api.static.*\;SYMBOLS\;_ptp_clock_driver_api_list_start\;_ptp_clock_driver_api_list_end}")
list(APPEND sections "{NAME\;pwm_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;pwm_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._pwm_driver_api.static.*\;SYMBOLS\;_pwm_driver_api_list_start\;_pwm_driver_api_list_end}")
list(APPEND sections "{NAME\;regulator_parent_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;regulator_parent_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._regulator_parent_driver_api.static.*\;SYMBOLS\;_regulator_parent_driver_api_list_start\;_regulator_parent_driver_api_list_end}")
list(APPEND sections "{NAME\;regulator_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;regulator_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._regulator_driver_api.static.*\;SYMBOLS\;_regulator_driver_api_list_start\;_regulator_driver_api_list_end}")
list(APPEND sections "{NAME\;reset_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;reset_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._reset_driver_api.static.*\;SYMBOLS\;_reset_driver_api_list_start\;_reset_driver_api_list_end}")
list(APPEND sections "{NAME\;retained_mem_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;retained_mem_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._retained_mem_driver_api.static.*\;SYMBOLS\;_retained_mem_driver_api_list_start\;_retained_mem_driver_api_list_end}")
list(APPEND sections "{NAME\;rtc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;rtc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._rtc_driver_api.static.*\;SYMBOLS\;_rtc_driver_api_list_start\;_rtc_driver_api_list_end}")
list(APPEND sections "{NAME\;sdhc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;sdhc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._sdhc_driver_api.static.*\;SYMBOLS\;_sdhc_driver_api_list_start\;_sdhc_driver_api_list_end}")
list(APPEND sections "{NAME\;sensor_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;sensor_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._sensor_driver_api.static.*\;SYMBOLS\;_sensor_driver_api_list_start\;_sensor_driver_api_list_end}")
list(APPEND sections "{NAME\;smbus_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;smbus_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._smbus_driver_api.static.*\;SYMBOLS\;_smbus_driver_api_list_start\;_smbus_driver_api_list_end}")
list(APPEND sections "{NAME\;stepper_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;stepper_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._stepper_driver_api.static.*\;SYMBOLS\;_stepper_driver_api_list_start\;_stepper_driver_api_list_end}")
list(APPEND sections "{NAME\;syscon_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;syscon_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._syscon_driver_api.static.*\;SYMBOLS\;_syscon_driver_api_list_start\;_syscon_driver_api_list_end}")
list(APPEND sections "{NAME\;tee_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;tee_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._tee_driver_api.static.*\;SYMBOLS\;_tee_driver_api_list_start\;_tee_driver_api_list_end}")
list(APPEND sections "{NAME\;video_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;video_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._video_driver_api.static.*\;SYMBOLS\;_video_driver_api_list_start\;_video_driver_api_list_end}")
list(APPEND sections "{NAME\;w1_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;w1_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._w1_driver_api.static.*\;SYMBOLS\;_w1_driver_api_list_start\;_w1_driver_api_list_end}")
list(APPEND sections "{NAME\;wdt_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;wdt_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._wdt_driver_api.static.*\;SYMBOLS\;_wdt_driver_api_list_start\;_wdt_driver_api_list_end}")
list(APPEND sections "{NAME\;can_transceiver_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;can_transceiver_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._can_transceiver_driver_api.static.*\;SYMBOLS\;_can_transceiver_driver_api_list_start\;_can_transceiver_driver_api_list_end}")
list(APPEND sections "{NAME\;nrf_clock_control_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;nrf_clock_control_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._nrf_clock_control_driver_api.static.*\;SYMBOLS\;_nrf_clock_control_driver_api_list_start\;_nrf_clock_control_driver_api_list_end}")
list(APPEND sections "{NAME\;i3c_target_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;i3c_target_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._i3c_target_driver_api.static.*\;SYMBOLS\;_i3c_target_driver_api_list_start\;_i3c_target_driver_api_list_end}")
list(APPEND sections "{NAME\;its_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;its_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._its_driver_api.static.*\;SYMBOLS\;_its_driver_api_list_start\;_its_driver_api_list_end}")
list(APPEND sections "{NAME\;vtd_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;vtd_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._vtd_driver_api.static.*\;SYMBOLS\;_vtd_driver_api_list_start\;_vtd_driver_api_list_end}")
list(APPEND sections "{NAME\;tgpio_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;tgpio_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._tgpio_driver_api.static.*\;SYMBOLS\;_tgpio_driver_api_list_start\;_tgpio_driver_api_list_end}")
list(APPEND sections "{NAME\;pcie_ctrl_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;pcie_ctrl_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._pcie_ctrl_driver_api.static.*\;SYMBOLS\;_pcie_ctrl_driver_api_list_start\;_pcie_ctrl_driver_api_list_end}")
list(APPEND sections "{NAME\;pcie_ep_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;pcie_ep_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._pcie_ep_driver_api.static.*\;SYMBOLS\;_pcie_ep_driver_api_list_start\;_pcie_ep_driver_api_list_end}")
list(APPEND sections "{NAME\;svc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;svc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._svc_driver_api.static.*\;SYMBOLS\;_svc_driver_api_list_start\;_svc_driver_api_list_end}")
list(APPEND sections "{NAME\;uart_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;uart_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._uart_driver_api.static.*\;SYMBOLS\;_uart_driver_api_list_start\;_uart_driver_api_list_end}")
list(APPEND sections "{NAME\;bc12_emul_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;bc12_emul_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._bc12_emul_driver_api.static.*\;SYMBOLS\;_bc12_emul_driver_api_list_start\;_bc12_emul_driver_api_list_end}")
list(APPEND sections "{NAME\;bc12_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;bc12_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._bc12_driver_api.static.*\;SYMBOLS\;_bc12_driver_api_list_start\;_bc12_driver_api_list_end}")
list(APPEND sections "{NAME\;usbc_ppc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;usbc_ppc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._usbc_ppc_driver_api.static.*\;SYMBOLS\;_usbc_ppc_driver_api_list_start\;_usbc_ppc_driver_api_list_end}")
list(APPEND sections "{NAME\;tcpc_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;tcpc_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._tcpc_driver_api.static.*\;SYMBOLS\;_tcpc_driver_api_list_start\;_tcpc_driver_api_list_end}")
list(APPEND sections "{NAME\;usbc_vbus_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;usbc_vbus_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._usbc_vbus_driver_api.static.*\;SYMBOLS\;_usbc_vbus_driver_api_list_start\;_usbc_vbus_driver_api_list_end}")
list(APPEND sections "{NAME\;ivshmem_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;ivshmem_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._ivshmem_driver_api.static.*\;SYMBOLS\;_ivshmem_driver_api_list_start\;_ivshmem_driver_api_list_end}")
list(APPEND sections "{NAME\;ethphy_driver_api_area\;GROUP\;RODATA_REGION\;SUBALIGN\;4\;NOINPUT\;TRUE}")
list(APPEND section_settings "{SECTION\;ethphy_driver_api_area\;SORT\;NAME\;KEEP\;TRUE\;INPUT\;._ethphy_driver_api.static.*\;SYMBOLS\;_ethphy_driver_api_list_start\;_ethphy_driver_api_list_end}")
set(DEVICE_API_SECTIONS         "${sections}" CACHE INTERNAL "")
set(DEVICE_API_SECTION_SETTINGS "${section_settings}" CACHE INTERNAL "")
