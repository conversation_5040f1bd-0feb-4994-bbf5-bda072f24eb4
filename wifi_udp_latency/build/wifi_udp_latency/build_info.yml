cmake:
  application:
    configuration-dir: /opt/nordic/ncs/v3.0.2/wifi_udp_latency
    source-dir: /opt/nordic/ncs/v3.0.2/wifi_udp_latency
  board:
    name: nrf7002dk
    path:
     - /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
    qualifiers: nrf5340/cpuapp
    revision: 
  devicetree:
    bindings-dirs:
     - /opt/nordic/ncs/v3.0.2/nrf/dts/bindings
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/bindings
    files:
     - /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/nrf7002dk_nrf5340_cpuapp.dts
    include-dirs:
     - /opt/nordic/ncs/v3.0.2/nrf/include
     - /opt/nordic/ncs/v3.0.2/nrf/dts/common
     - /opt/nordic/ncs/v3.0.2/nrf/dts/riscv
     - /opt/nordic/ncs/v3.0.2/nrf/dts/arm
     - /opt/nordic/ncs/v3.0.2/nrf/dts
     - /opt/nordic/ncs/v3.0.2/modules/hal/nxp/dts
     - /opt/nordic/ncs/v3.0.2/modules/hal/stm32/dts
     - /opt/nordic/ncs/v3.0.2/zephyr/include
     - /opt/nordic/ncs/v3.0.2/zephyr/include/zephyr
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/common
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/x86
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/xtensa
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/sparc
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/riscv
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/posix
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/nios2
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/arm64
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/arm
     - /opt/nordic/ncs/v3.0.2/zephyr/dts/arc
     - /opt/nordic/ncs/v3.0.2/zephyr/dts
  kconfig:
    extra-user-files:
     - overlay-test2-rx.conf
    files:
     - /opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk/nrf7002dk_nrf5340_cpuapp_defconfig
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/prj.conf
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/boards/nrf7002dk_nrf5340_cpuapp.conf
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/overlay-test2-rx.conf
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/.config.sysbuild
    user-files:
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/prj.conf
     - /opt/nordic/ncs/v3.0.2/wifi_udp_latency/boards/nrf7002dk_nrf5340_cpuapp.conf
  toolchain:
    name: zephyr
    path: /opt/nordic/zp/toolchains/zephyr-sdk-0.17.0
  vendor-specific:
    nordic:
      svdfile: /opt/nordic/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk/nrf5340_application.svd
  zephyr:
    version: 4.0.99
    zephyr-base: /opt/nordic/ncs/v3.0.2/zephyr
version: 0.1.0
