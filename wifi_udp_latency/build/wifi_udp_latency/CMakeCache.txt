# This is the CMakeCache file.
# For build in directory: /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Application Binary Directory
APPLICATION_BINARY_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency

//The application configuration folder
APPLICATION_CONFIG_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency

//Application Source Directory
APPLICATION_SOURCE_DIR:PATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency

//Mbed TLS folder
ARM_MBEDTLS_PATH:STRING=/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls

//Selected board
BOARD:STRING=nrf7002dk/nrf5340/cpuapp

//Main board directory for board (nrf7002dk)
BOARD_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk

//Support board extensions
BOARD_EXTENSIONS:BOOL=ON

//Path to a program.
BOSSAC:FILEPATH=BOSSAC-NOTFOUND

//Kernel binary file
BYPRODUCT_KERNEL_BIN_NAME:FILEPATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/zephyr.bin

//Kernel elf file
BYPRODUCT_KERNEL_ELF_NAME:FILEPATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/zephyr.elf

//Kernel hex file
BYPRODUCT_KERNEL_HEX_NAME:FILEPATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/zephyr.hex

//Selected board
CACHED_BOARD:STRING=nrf7002dk/nrf5340/cpuapp

//Selected shield
CACHED_SHIELD:STRING=

//Selected snippet
CACHED_SNIPPET:STRING=

//Path to a program.
CCACHE_FOUND:FILEPATH=/opt/homebrew/bin/ccache

//Path to a program.
CCACHE_PROGRAM:FILEPATH=/opt/homebrew/bin/ccache

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar

//Path to a program.
CMAKE_AS:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-as

//ASM compiler
CMAKE_ASM_COMPILER:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_AR:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_RANLIB:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib

//Flags used by the ASM compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the ASM compiler during DEBUG builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=-g

//Flags used by the ASM compiler during MINSIZEREL builds.
CMAKE_ASM_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the ASM compiler during RELEASE builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the ASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//CXX compiler
CMAKE_CXX_COMPILER:STRING=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-g++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:STRING=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Export CMake compile commands. Used by gen_app_partitions.py
// script
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_GCOV:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/aarch64-zephyr-elf/bin/aarch64-zephyr-elf-gcov

//Path to a program.
CMAKE_GDB:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb

//Path to a program.
CMAKE_GDB_NO_PY:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/opt/homebrew/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=wifi_udp_latency

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=4.0.99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/bin/arm-zephyr-eabi-strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//No help, variable specified on the command line.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//If desired, you can build the application usingthe configuration
// settings specified in an alternate .conf file using this parameter.
// These settings will override the settings in the application’s
// .config file or its default .conf file.Multiple files may be
// listed, e.g. CONF_FILE="prj1.conf;prj2.conf" The CACHED_CONF_FILE
// is internal Zephyr variable used between CMake runs. To change
// CONF_FILE, use the CONF_FILE variable.
CONF_FILE:STRING=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/prj.conf;/opt/nordic/ncs/v3.0.2/wifi_udp_latency/boards/nrf7002dk_nrf5340_cpuapp.conf

//Path to a program.
DTC:FILEPATH=/opt/homebrew/bin/dtc

//If desired, you can build the application using the DT configuration
// settings specified in an alternate .overlay file using this
// parameter. These settings will override the settings in the
// board's .dts file. Multiple files may be listed, e.g. DTC_OVERLAY_FILE="dts1.overlay
// dts2.overlay"
DTC_OVERLAY_FILE:STRING=

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_PSA_CRYPTO_CONFIG:BOOL=False

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_PSA_CRYPTO_LIBRARY_CONFIG:BOOL=False

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_PSA_INTERFACE:BOOL=False

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_TFM_API_NS:BOOL=False

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_TFM_PSA_ROT_PARTITION_CRYPTO:BOOL=False

//Use to ensure we add links only once
EXTERNAL_CRYPTO_CORE_HANDLED_TFM_SPRT:BOOL=False

//No help, variable specified on the command line.
EXTRA_KCONFIG_TARGETS:UNINITIALIZED=

//No help, variable specified on the command line.
FORCED_CONF_FILE:FILEPATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/.config.sysbuild

//Git command line client
GIT_EXECUTABLE:FILEPATH=/usr/bin/git

//Linker BFD compatibility (compiler reported)
GNULD_LINKER_IS_BFD:BOOL=ON

//GNU ld version
GNULD_VERSION_STRING:STRING=2.38

//Path to a program.
GPERF:FILEPATH=/usr/bin/gperf

//Path to a program.
IMGTOOL:FILEPATH=/opt/nordic/ncs/v3.0.2/bootloader/mcuboot/scripts/imgtool.py

//Mbed TLS Config file
MBEDTLS_CONFIG_FILE:STRING=nrf-config.h

//PSA crypto config file (PSA_WANT)
MBEDTLS_PSA_CRYPTO_CONFIG_FILE:STRING=nrf-psa-crypto-config.h

//PSA crypto config file (PSA_NEED)
MBEDTLS_PSA_CRYPTO_USER_CONFIG_FILE:STRING=nrf-psa-crypto-user-config.h

//nrfxlib folder
NRFXLIB_DIR:STRING=/opt/nordic/ncs/v3.0.2/nrfxlib

//nrfx Directory
NRFX_DIR:PATH=/opt/nordic/ncs/v3.0.2/modules/hal/nordic/nrfx

//NCS root directory
NRF_DIR:PATH=/opt/nordic/ncs/v3.0.2/nrf

//nrf_security root folder
NRF_SECURITY_ROOT:STRING=/opt/nordic/ncs/v3.0.2/nrf/subsys/nrf_security

//oberon-psa-core folder
OBERON_PSA_CORE_PATH:STRING=/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto

//Path to a program.
OPENOCD:FILEPATH=/opt/homebrew/bin/openocd

//Path to a program.
PAHOLE:FILEPATH=PAHOLE-NOTFOUND

//Path used for generated PSA crypto configuratiosn for the interface
PSA_CRYPTO_CONFIG_INTERFACE_PATH:STRING=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/generated/interface_nrf_security_psa

//Path used for generated PSA crypto for library builds
PSA_CRYPTO_CONFIG_LIBRARY_PATH:STRING=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/generated/library_nrf_security_psa

//Enable building PSA core externally
PSA_CRYPTO_EXTERNAL_CORE:BOOL=ON

//Path to a program.
PTY_INTERFACE:FILEPATH=PTY_INTERFACE-NOTFOUND

//Path to a program.
PUNCOVER:FILEPATH=PUNCOVER-NOTFOUND

//Value Computed by CMake
Picolibc_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/picolibc

//Value Computed by CMake
Picolibc_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Picolibc_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/modules/lib/picolibc

//Path to a program.
Python3_EXECUTABLE:FILEPATH=/opt/nordic/ncs/toolchains/ef4fc6722e/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/bin/python3

//Path to the SoC directory.
SOC_FULL_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/soc/nordic

//Path to a CMSIS-SVD file
SOC_SVD_FILE:FILEPATH=/opt/nordic/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk/nrf5340_application.svd

//No help, variable specified on the command line.
SYSBUILD:BOOL=True

//No help, variable specified on the command line.
SYSBUILD_CACHE:FILEPATH=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency_sysbuild_cache.txt

//True if toolchain supports newlib
TOOLCHAIN_HAS_NEWLIB:BOOL=ON

//True if toolchain supports picolibc
TOOLCHAIN_HAS_PICOLIBC:BOOL=ON

//Zephyr toolchain root
TOOLCHAIN_ROOT:STRING=/opt/nordic/ncs/v3.0.2/zephyr

//Zephyr base
ZEPHYR_BASE:PATH=/opt/nordic/ncs/v3.0.2/zephyr

//Path to Zephyr git repository index file
ZEPHYR_GIT_INDEX:PATH=/opt/nordic/ncs/v3.0.2/zephyr/.git/index

//Zephyr SDK install directory
ZEPHYR_SDK_INSTALL_DIR:PATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0

//Zephyr toolchain variant
ZEPHYR_TOOLCHAIN_VARIANT:STRING=zephyr

//Value Computed by CMake
Zephyr-Kernel_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency

//Value Computed by CMake
Zephyr-Kernel_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Zephyr-Kernel_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency

//The directory containing a CMake configuration file for Zephyr-sdk.
Zephyr-sdk_DIR:PATH=/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/cmake

//The directory containing a CMake configuration file for ZephyrAppConfiguration.
ZephyrAppConfiguration_DIR:PATH=ZephyrAppConfiguration_DIR-NOTFOUND

//The directory containing a CMake configuration file for ZephyrBuildConfiguration.
ZephyrBuildConfiguration_DIR:PATH=/opt/nordic/ncs/v3.0.2/nrf/share/zephyrbuild-package/cmake

//The directory containing a CMake configuration file for Zephyr.
Zephyr_DIR:PATH=/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake

//Value Computed by CMake
wifi_udp_latency_BINARY_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency

//Value Computed by CMake
wifi_udp_latency_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
wifi_udp_latency_SOURCE_DIR:STATIC=/opt/nordic/ncs/v3.0.2/wifi_udp_latency


########################
# INTERNAL cache entries
########################

//List of board directories for board (nrf7002dk)
BOARD_DIRECTORIES:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/boards/nordic/nrf7002dk
//DT bindings root directories
CACHED_DTS_ROOT_BINDINGS:INTERNAL=/opt/nordic/ncs/v3.0.2/nrf/dts/bindings;/opt/nordic/ncs/v3.0.2/zephyr/dts/bindings
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER
CMAKE_ASM_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_AR
CMAKE_ASM_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_RANLIB
CMAKE_ASM_COMPILER_RANLIB-ADVANCED:INTERNAL=1
CMAKE_ASM_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS
CMAKE_ASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_DEBUG
CMAKE_ASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_MINSIZEREL
CMAKE_ASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELEASE
CMAKE_ASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=223
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//The build type
CONF_FILE_BUILD_TYPE:INTERNAL=
//Details about finding Dtc
FIND_PACKAGE_MESSAGE_DETAILS_Dtc:INTERNAL=[/opt/homebrew/bin/dtc][v1.7.2(1.4.6)]
//Details about finding GnuLd
FIND_PACKAGE_MESSAGE_DETAILS_GnuLd:INTERNAL=[/opt/nordic/zp/toolchains/zephyr-sdk-0.17.0/arm-zephyr-eabi/arm-zephyr-eabi/bin/ld.bfd][v2.38()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/opt/nordic/ncs/toolchains/ef4fc6722e/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/bin/python3][found components: Interpreter ][v3.12.4(3.10)]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Zephyr hardware model version
HWM:INTERNAL=v2
//Zephyr hardware model
HWMv2:INTERNAL=True
KERNEL_META_PATH:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/zephyr.meta
//shared var
PM_YML_DEP_FILES:INTERNAL=/opt/nordic/ncs/v3.0.2/nrf/subsys/partition_manager/pm.yml.settings;/opt/nordic/ncs/v3.0.2/nrf/subsys/partition_manager/pm.yml.file_system;/opt/nordic/ncs/v3.0.2/nrf/subsys/partition_manager/pm.yml.nvs;/opt/nordic/ncs/v3.0.2/nrf/subsys/partition_manager/pm.yml.rpmsg_nrf53
//shared var
PM_YML_FILES:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/partition_manager/pm.yml.settings;/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/partition_manager/pm.yml.file_system;/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/partition_manager/pm.yml.nvs;/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/partition_manager/pm.yml.rpmsg_nrf53
//List of SoC directories for SoC (nrf5340)
SOC_DIRECTORIES:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/soc/nordic
//SoC Linker script
SOC_LINKER_SCRIPT:INTERNAL=/opt/nordic/ncs/v3.0.2/zephyr/include/zephyr/arch/arm/cortex_m/scripts/linker.ld
//West
WEST:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/bin/python3;-m;west
//shared var
ZEPHYR_BINARY_DIR:INTERNAL=/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/opt/nordic/ncs/toolchains/ef4fc6722e/Cellar/python@3.12/3.12.4/Frameworks/Python.framework/Versions/3.12/bin/python3
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;<none>;cpython-312-darwin.so;abi3;/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/Frameworks/Python.framework/Versions/3.12/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/opt/python@3.12/Frameworks/Python.framework/Versions/3.12/lib/python3.12;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages;/opt/nordic/ncs/toolchains/ef4fc6722e/lib/python3.12/site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=a8170795f555384c1207bc2ff4cc1bcf
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=

