/*
 * Copyright (c) 2021 Nordic Semiconductor
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 *
 */

#ifndef PSA_CRYPTO_USER_CONFIG_H
#define PSA_CRYPTO_USER_CONFIG_H

/* PSA Core implementation */
#define PSA_CORE_OBERON                                    1

/* #undef PSA_MAX_RSA_KEY_BITS */

/*
 * nrf_cc3xx_platform driver configurations
 */
#define PSA_NEED_CC3XX_CTR_DRBG_DRIVER                     1
/* #undef PSA_NEED_CC3XX_HMAC_DRBG_DRIVER */

/*
 * nrf_cc3xx driver configurations
 */
/* #undef PSA_NEED_CC3XX_AEAD_DRIVER */
/* #undef PSA_NEED_CC3XX_ASYMMETRIC_ENCRYPTION_DRIVER */
/* #undef PSA_NEED_CC3XX_CIPHER_DRIVER */
/* #undef PSA_NEED_CC3XX_KEY_AGREEMENT_DRIVER */
/* #undef PSA_NEED_CC3XX_HASH_DRIVER */
/* #undef PSA_NEED_CC3XX_KEY_MANAGEMENT_DRIVER */
/* #undef PSA_NEED_CC3XX_MAC_DRIVER */
/* #undef PSA_NEED_CC3XX_ASYMMETRIC_SIGNATURE_DRIVER */

/*
 * nrf_oberon driver configurations
 */
#define PSA_NEED_OBERON_AEAD_DRIVER                                            1
/* #undef PSA_NEED_OBERON_ANY_RSA_KEY_SIZE */
/* #undef PSA_NEED_OBERON_ASYMMETRIC_ENCRYPTION_DRIVER */
#define PSA_NEED_OBERON_ASYMMETRIC_SIGNATURE_DRIVER                            1
#define PSA_NEED_OBERON_CBC_NO_PADDING_AES                                     1
#define PSA_NEED_OBERON_CBC_PKCS7_AES                                          1
#define PSA_NEED_OBERON_CCM_AES                                                1
/* #undef PSA_NEED_OBERON_CCM_STAR_NO_TAG_AES */
#define PSA_NEED_OBERON_CHACHA20_POLY1305                                      1
#define PSA_NEED_OBERON_CIPHER_DRIVER                                          1
#define PSA_NEED_OBERON_CMAC                                                   1
#define PSA_NEED_OBERON_CTR_AES                                                1
/* #undef PSA_NEED_OBERON_CTR_DRBG_DRIVER */
#define PSA_NEED_OBERON_ECB_NO_PADDING_AES                                     1
#define PSA_NEED_OBERON_ECDH                                                   1
/* #undef PSA_NEED_OBERON_ECDH_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_ECDH_MONTGOMERY_448 */
/* #undef PSA_NEED_OBERON_ECDH_SECP_R1_224 */
#define PSA_NEED_OBERON_ECDH_SECP_R1_256                                       1
/* #undef PSA_NEED_OBERON_ECDH_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_ECDH_SECP_R1_521 */
#define PSA_NEED_OBERON_ECDSA_DETERMINISTIC                                    1
#define PSA_NEED_OBERON_ECDSA_RANDOMIZED                                       1
/* #undef PSA_NEED_OBERON_ECDSA_SECP_R1_224 */
#define PSA_NEED_OBERON_ECDSA_SECP_R1_256                                      1
/* #undef PSA_NEED_OBERON_ECDSA_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_ECDSA_SECP_R1_521 */
#define PSA_NEED_OBERON_ECDSA_SIGN                                             1
#define PSA_NEED_OBERON_ECDSA_VERIFY                                           1
/* #undef PSA_NEED_OBERON_ECJPAKE_SECP_R1_256 */
/* #undef PSA_NEED_OBERON_ED25519PH */
/* #undef PSA_NEED_OBERON_ED448PH */
#define PSA_NEED_OBERON_GCM_AES                                                1
#define PSA_NEED_OBERON_HASH_DRIVER                                            1
#define PSA_NEED_OBERON_HKDF                                                   1
/* #undef PSA_NEED_OBERON_HKDF_EXPAND */
/* #undef PSA_NEED_OBERON_HKDF_EXTRACT */
#define PSA_NEED_OBERON_HMAC                                                   1
/* #undef PSA_NEED_OBERON_HMAC_DRBG_DRIVER */
/* #undef PSA_NEED_OBERON_JPAKE */
#define PSA_NEED_OBERON_KEY_AGREEMENT_DRIVER                                   1
#define PSA_NEED_OBERON_KEY_DERIVATION_DRIVER                                  1
#define PSA_NEED_OBERON_KEY_MANAGEMENT_DRIVER                                  1
/* #undef PSA_NEED_OBERON_KEY_WRAP_DRIVER */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE                           1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_MONTGOMERY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_MONTGOMERY_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP                      1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_224 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_256               1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_521 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_TWISTED_EDWARDS */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_DERIVE_TWISTED_EDWARDS_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT                           1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_MONTGOMERY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_MONTGOMERY_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP                      1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_224 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_256               1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_521 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_TWISTED_EDWARDS */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_EXPORT_TWISTED_EDWARDS_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE                         1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_MONTGOMERY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_MONTGOMERY_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP                    1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_224 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_256             1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_521 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_TWISTED_EDWARDS */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_GENERATE_TWISTED_EDWARDS_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT                           1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_MONTGOMERY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_MONTGOMERY_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP                      1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_224 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_256               1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_521 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_TWISTED_EDWARDS */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_KEY_PAIR_IMPORT_TWISTED_EDWARDS_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY                                1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_MONTGOMERY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_MONTGOMERY_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_MONTGOMERY_448 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP                           1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_224 */
#define PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_256                    1
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_384 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_521 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_TWISTED_EDWARDS */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_ECC_PUBLIC_KEY_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_RSA_KEY_PAIR_EXPORT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_RSA_KEY_PAIR_IMPORT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_RSA_PUBLIC_KEY */
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE                       1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP                  1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP_R1_256           1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT                       1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP                  1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP_R1_256           1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT                       1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP                  1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP_R1_256           1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY                            1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP                       1
#define PSA_NEED_OBERON_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP_R1_256                1
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_KEY_PAIR_EXPORT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_KEY_PAIR_EXPORT_3072 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_KEY_PAIR_IMPORT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_KEY_PAIR_IMPORT_3072 */
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_PUBLIC_KEY */
/* #undef PSA_NEED_OBERON_KEY_TYPE_SRP_6_PUBLIC_KEY_3072 */
#define PSA_NEED_OBERON_MAC_DRIVER                                             1
/* #undef PSA_NEED_OBERON_PAKE_DRIVER */
#define PSA_NEED_OBERON_PBKDF2_AES_CMAC_PRF_128                                1
/* #undef PSA_NEED_OBERON_PBKDF2_HMAC */
/* #undef PSA_NEED_OBERON_PURE_EDDSA_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_OBERON_PURE_EDDSA_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_OBERON_RSA_ANY_CRYPT */
/* #undef PSA_NEED_OBERON_RSA_ANY_SIGN */
/* #undef PSA_NEED_OBERON_RSA_ANY_VERIFY */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_1024 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_1536 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_2048 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_3072 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_4096 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_6144 */
/* #undef PSA_NEED_OBERON_RSA_KEY_SIZE_8192 */
/* #undef PSA_NEED_OBERON_RSA_OAEP */
/* #undef PSA_NEED_OBERON_RSA_PKCS1V15_CRYPT */
/* #undef PSA_NEED_OBERON_RSA_PKCS1V15_SIGN */
/* #undef PSA_NEED_OBERON_RSA_PSS */
/* #undef PSA_NEED_OBERON_SHA3 */
/* #undef PSA_NEED_OBERON_SHA3_224 */
/* #undef PSA_NEED_OBERON_SHA3_256 */
/* #undef PSA_NEED_OBERON_SHA3_384 */
/* #undef PSA_NEED_OBERON_SHA3_512 */
/* #undef PSA_NEED_OBERON_SHAKE */
/* #undef PSA_NEED_OBERON_SHAKE256_512 */
#define PSA_NEED_OBERON_SHA_1                                                  1
#define PSA_NEED_OBERON_SHA_224                                                1
#define PSA_NEED_OBERON_SHA_256                                                1
#define PSA_NEED_OBERON_SHA_384                                                1
#define PSA_NEED_OBERON_SHA_512                                                1
/* #undef PSA_NEED_OBERON_SPAKE2P */
/* #undef PSA_NEED_OBERON_SPAKE2P_CMAC_SECP_R1_256 */
/* #undef PSA_NEED_OBERON_SPAKE2P_HMAC_SECP_R1_256 */
/* #undef PSA_NEED_OBERON_SPAKE2P_MATTER */
/* #undef PSA_NEED_OBERON_SRP_6 */
/* #undef PSA_NEED_OBERON_SRP_6_3072 */
/* #undef PSA_NEED_OBERON_SRP_PASSWORD_HASH */
#define PSA_NEED_OBERON_STREAM_CIPHER_CHACHA20                                 1
#define PSA_NEED_OBERON_TLS12_ECJPAKE_TO_PMS                                   1
#define PSA_NEED_OBERON_TLS12_PRF                                              1
#define PSA_NEED_OBERON_TLS12_PSK_TO_MS                                        1
/* #undef PSA_NEED_OBERON_AES_KW */
/* #undef PSA_NEED_OBERON_AES_KWP */
/* #undef PSA_NEED_OBERON_WPA3_SAE_PT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_WPA3_SAE_PT */
/* #undef PSA_NEED_OBERON_KEY_TYPE_WPA3_SAE_PT_SECP */
/* #undef PSA_NEED_OBERON_KEY_TYPE_WPA3_SAE_PT_SECP_R1_256 */
/* #undef PSA_NEED_OBERON_WPA3_SAE */
/* #undef PSA_NEED_OBERON_WPA3_SAE_SECP_R1_256 */
/* #undef PSA_NEED_OBERON_SP800_108_COUNTER_CMAC */
/* #undef PSA_NEED_OBERON_SP800_108_COUNTER_HMAC */

/* #undef PSA_NEED_NRF_RNG_ENTROPY_DRIVER */

/*
 * CRACEN driver configuration
 * NCSDK-20700: Make PSA CRACEN driver configurable.
 */
/* #undef PSA_CRYPTO_DRIVER_CRACEN */
/* #undef PSA_NEED_CRACEN_AEAD_DRIVER */
/* #undef PSA_NEED_CRACEN_CIPHER_DRIVER */
/* #undef PSA_NEED_CRACEN_KEY_AGREEMENT_DRIVER */
/* #undef PSA_NEED_CRACEN_ASYMMETRIC_SIGNATURE_DRIVER */
/* #undef PSA_NEED_CRACEN_ASYMMETRIC_ENCRYPTION_DRIVER */
/* #undef PSA_NEED_CRACEN_HASH_DRIVER */
/* #undef PSA_NEED_CRACEN_KEY_MANAGEMENT_DRIVER */
/* #undef PSA_NEED_CRACEN_KMU_DRIVER */
/* #undef PSA_NEED_CRACEN_PLATFORM_KEYS */
/* #undef PSA_NEED_CRACEN_MAC_DRIVER */
/* #undef PSA_NEED_CRACEN_PAKE_DRIVER */
/* #undef PSA_NEED_CRACEN_KEY_DERIVATION_DRIVER */
/* #undef PSA_NEED_CRACEN_CTR_DRBG_DRIVER */

/* #undef PSA_NEED_CRACEN_KMU_ENCRYPTED_KEYS */
/* #undef PSA_NEED_CRACEN_CCM_AES */
/* #undef PSA_NEED_CRACEN_GCM_AES */
/* #undef PSA_NEED_CRACEN_CHACHA20_POLY1305 */
/* #undef PSA_NEED_CRACEN_CTR_AES */
/* #undef PSA_NEED_CRACEN_CBC_PKCS7_AES */
/* #undef PSA_NEED_CRACEN_CBC_NO_PADDING_AES */
/* #undef PSA_NEED_CRACEN_ECB_NO_PADDING_AES */
/* #undef PSA_NEED_CRACEN_STREAM_CIPHER_CHACHA20 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_ECDH_BRAINPOOL_P_R1 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_R1 */
/* #undef PSA_NEED_CRACEN_ECDH_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_ECDH_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_ECDH_MONTGOMERY */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_ECDH_SECP_K1 */
/* #undef PSA_NEED_CRACEN_ASYMMETRIC_SIGNATURE_ANY_RSA */
/* #undef PSA_NEED_CRACEN_ASYMMETRIC_SIGNATURE_ANY_ECC */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_ECDSA_BRAINPOOL_P_R1 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_R1 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_ECDSA_SECP_K1 */
/* #undef PSA_NEED_CRACEN_PURE_EDDSA_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_PURE_EDDSA_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_PURE_EDDSA_TWISTED_EDWARDS */
/* #undef PSA_NEED_CRACEN_ED25519PH */
/* #undef PSA_NEED_CRACEN_RSA_PKCS1V15_SIGN */
/* #undef PSA_NEED_CRACEN_RSA_PSS */
/* #undef PSA_NEED_CRACEN_RSA_OAEP */
/* #undef PSA_NEED_CRACEN_RSA_PKCS1V15_CRYPT */
/* #undef PSA_NEED_CRACEN_SHA_1 */
/* #undef PSA_NEED_CRACEN_SHA_224 */
/* #undef PSA_NEED_CRACEN_SHA_256 */
/* #undef PSA_NEED_CRACEN_SHA_384 */
/* #undef PSA_NEED_CRACEN_SHA_512 */
/* #undef PSA_NEED_CRACEN_SHA3_224 */
/* #undef PSA_NEED_CRACEN_SHA3_256 */
/* #undef PSA_NEED_CRACEN_SHA3_384 */
/* #undef PSA_NEED_CRACEN_SHA3_512 */

/* Key management driver configurations */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1_521 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_R1 */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_K1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_K1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_SECP_K1 */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_MONTGOMERY_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_MONTGOMERY_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_MONTGOMERY */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_TWISTED_EDWARDS_255 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_TWISTED_EDWARDS_448 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_TWISTED_EDWARDS */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_192 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_224 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_320 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_384 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1_512 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_BRAINPOOL_P_R1 */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_PUBLIC_KEY */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_IMPORT */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_EXPORT */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_GENERATE */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_ECC_KEY_PAIR_DERIVE */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_SPAKE2P_PUBLIC_KEY_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SPAKE2P_KEY_PAIR_IMPORT_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SPAKE2P_KEY_PAIR_EXPORT_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SPAKE2P_KEY_PAIR_DERIVE_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SPAKE2P_SECP_R1_256 */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_SRP_6_PUBLIC_KEY_3072 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SRP_6_KEY_PAIR_IMPORT_3072 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SRP_6_KEY_PAIR_EXPORT_3072 */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_SRP_6_3072 */

/* #undef PSA_NEED_CRACEN_KEY_TYPE_RSA_PUBLIC_KEY */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_RSA_KEY_PAIR_IMPORT */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_RSA_KEY_PAIR_EXPORT */
/* #undef PSA_NEED_CRACEN_KEY_TYPE_RSA_KEY_PAIR_GENERATE */

/* #undef PSA_NEED_CRACEN_HMAC */
/* #undef PSA_NEED_CRACEN_CMAC */
/* #undef PSA_NEED_CRACEN_SRP_6 */
/* #undef PSA_NEED_CRACEN_SRP_PASSWORD_HASH */
/* #undef PSA_NEED_CRACEN_ECJPAKE_SECP_R1_256 */
/* #undef PSA_NEED_CRACEN_ECJPAKE */
/* #undef PSA_NEED_CRACEN_SPAKE2P */
/* #undef PSA_NEED_CRACEN_HKDF */
/* #undef PSA_NEED_CRACEN_SP800_108_COUNTER_CMAC */
/* #undef PSA_NEED_CRACEN_TLS12_ECJPAKE_TO_PMS */
/* #undef PSA_NEED_CRACEN_TLS12_PRF */
/* #undef PSA_NEED_CRACEN_TLS12_PSK_TO_MS */
/* #undef PSA_NEED_CRACEN_PBKDF2_HMAC */

/* Nordic specific */
/* #undef PSA_CRYPTO_DRIVER_ALG_PRNG_TEST */

/* PSA and drivers */
#define MBEDTLS_PSA_CRYPTO_C
/* #undef MBEDTLS_PSA_CRYPTO_STORAGE_C */
/* MBEDTLS_PSA_CRYPTO_DRIVERS is defined to 1 by TF-M's build system. */
#define MBEDTLS_PSA_CRYPTO_DRIVERS                         1
#define MBEDTLS_PSA_CRYPTO_CLIENT
#define MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG
#define MBEDTLS_PSA_KEY_SLOT_COUNT                         32
/* #undef MBEDTLS_PSA_STATIC_KEY_SLOTS */
/* #undef MBEDTLS_PSA_STATIC_KEY_SLOT_BUFFER_SIZE */

#include <oberon_check_unsupported.h>

#include <check_crypto_config.h>

#endif /* PSA_CRYPTO_USER_CONFIG_H */
