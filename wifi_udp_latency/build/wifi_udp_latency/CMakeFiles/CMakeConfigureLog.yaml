
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:200 (message)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:128 (project)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      The target system is: Generic - 4.0.99 - arm
      The host system is: Darwin - 24.5.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:128 (project)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/bin/ld: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/libc.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x34): undefined reference to `_exit'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:128 (project)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/CMakeFiles/4.0.3/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:128 (project)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-g++ 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/bin/ld: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/libc.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x34): undefined reference to `_exit'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:128 (project)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-g++ 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        /opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/CMakeFiles/4.0.3/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:1271 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineASMCompiler.cmake:139 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/kernel.cmake:134 (enable_language)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/cmake/modules/zephyr_default.cmake:142 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:66 (include)"
      - "/opt/nordic/ncs/v3.0.2/zephyr/share/zephyr-package/cmake/ZephyrConfig.cmake:92 (include_boilerplate)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      arm-zephyr-eabi-gcc (Zephyr SDK 0.17.0) 12.2.0
      Copyright (C) 2022 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
...
