{"InstallScripts": ["/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/arch/arm/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/arch/arm/core/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/arch/arm/core/cortex_m/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/arch/arm/core/cortex_m/cmse/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/arch/arch/arm/core/mpu/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/crc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/libc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/libc/picolibc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/libc/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/posix/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/posix/options/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/hash/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/heap/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/mem_blocks/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/net_buf/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/os/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/os/zvfs/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/lib/utils/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/soc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/soc/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/soc/soc/nrf5340/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/soc/soc/nrf5340/nrf53/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/soc/soc/nrf5340/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/boards/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/boards/nordic/nrf7002dk/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/boards/shields/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/canbus/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/debug/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/debug/coredump/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/fb/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/fs/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/fs/nvs/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/fs/zms/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/ipc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/logging/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/logging/backends/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/logging/frontends/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/mem_mgmt/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/mgmt/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/modbus/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/pm/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/pm/policy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/portability/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/random/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/rtio/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/sd/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/stats/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/storage/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/storage/flash_map/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/task_wdt/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/testsuite/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/tracing/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/usb/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/l2/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/l2/ethernet/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/l2/wifi/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/pkt_filter/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/ip/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/utils/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/config/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/sockets/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/wifi_credentials/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/dhcpv4/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/lib/dns/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/net/conn_mgr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/settings/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/settings/src/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/shell/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/shell/modules/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/shell/modules/kernel_service/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/shell/modules/kernel_service/thread/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/subsys/shell/backends/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/disk/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/firmware/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/interrupt_controller/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/misc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/misc/coresight/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/pcie/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/usb/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/usb/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/usb/common/nrf_usbd_common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/usb_c/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/clock_control/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/console/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/entropy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/ethernet/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/ethernet/phy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/ethernet/eth_nxp_enet_qos/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/ethernet/nxp_enet/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/ethernet/dwc_xgmac/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/flash/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/gpio/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/pinctrl/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/pinctrl/renesas/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/serial/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/spi/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/timer/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/wifi/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/drivers/wifi/nrf_wifi/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/ext/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/bin/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/dk_buttons_and_leds/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/flash_patch/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/fatal_error/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/lib/boot_banner/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/samples/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/samples/common/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/samples/common/mcumgr_bt_ota_dfu/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/crypto_copy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/core/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/core/nrf_oberon/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/drivers/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/drivers/nrf_cc3xx_platform/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/drivers/nrf_oberon/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/drivers/zephyr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/drivers/legacy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/legacy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/nrf_security/src/zephyr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/lib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/lib/wifi_ready/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/lib/nrf70_fw_ext/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/lib/hostap_crypto/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/net/l2_wifi_if_conn/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/dfu/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/mpsl/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/mpsl/cx/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/logging/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/shell/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/debug/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/partition_manager/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/suit/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/suit/app_tools/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/mgmt/mcumgr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/mgmt/mcumgr/grp/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/settings/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/subsys/settings/src/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/modules/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/modules/wfa-qt/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/entropy/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/flash/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/gpio/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/hw_cc3xx/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/mpsl/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/mpsl/flash_sync/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/net/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/drivers/serial/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/tests/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf/tests/mocks/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hostap/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/mcuboot/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/mcuboot/boot/bootutil/zephyr/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/mbedtls/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/trusted-firmware-m/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cjson/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/azure-sdk-for-c/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cirrus-logic/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/openthread/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/suit-processor/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/memfault-firmware-sdk/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/canopennode/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/chre/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/lz4/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nanopb/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/zscilib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis/cmsis/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis/cmsis/CMSIS/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis/cmsis/CMSIS/Core/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis-dsp/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/cmsis-nn/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/fatfs/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_nordic/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_nordic/modules/hal_nordic/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_nordic/modules/hal_nordic/nrfx/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_nordic/nrfx/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_nxp/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_st/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_stm32/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_tdk/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/hal_wurthelektronik/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/liblc3/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/libmetal/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/littlefs/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/loramac-node/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/lvgl/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/mipi-sys-t/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf_wifi/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf_wifi/os/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf_wifi/os/nrf_wifi_osal/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf_wifi/bus/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/open-amp/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/percepio/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/picolibc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/segger/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/tinycrypt/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/uoscore-uedhoc/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/zcbor/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrfxlib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrfxlib/nrfxlib/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrfxlib/nrfxlib/crypto/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrfxlib/nrf_802154/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/nrf_hw_models/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/modules/connectedhomeip/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/kernel/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/cmake/flash/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/cmake/usage/cmake_install.cmake", "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency/zephyr/cmake/reports/cmake_install.cmake"], "Parallel": false}