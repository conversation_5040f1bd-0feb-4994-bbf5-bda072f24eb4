set(CMAKE_ASM_COMPILER "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar")
set(CMAKE_ASM_COMPILER_AR "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar")
set(CMAKE_RANLIB "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib")
set(CMAKE_ASM_COMPILER_RANLIB "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib")
set(CMAKE_LINKER "/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/bin/ld.bfd")
set(CMAKE_LINKER_LINK "")
set(CMAKE_LINKER_LLD "")
set(CMAKE_ASM_COMPILER_LINKER "")
set(CMAKE_ASM_COMPILER_LINKER_ID "")
set(CMAKE_ASM_COMPILER_LINKER_VERSION )
set(CMAKE_ASM_COMPILER_LINKER_FRONTEND_VARIANT )
set(CMAKE_MT "")
set(CMAKE_TAPI "CMAKE_TAPI-NOTFOUND")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)
set(CMAKE_ASM_LINKER_DEPFILE_SUPPORTED )
set(CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED )
set(CMAKE_ASM_LINKER_PUSHPOP_STATE_SUPPORTED )


