# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: wifi_udp_latency
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__app_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__app_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__zephyr_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__offsets_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_pre0_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_pre0_
  command = $PRE_LINK && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES -L"/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp" -lc -lgcc && $POST_BUILD
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_final_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_final_
  command = $PRE_LINK && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES -L"/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp" -lc -lgcc && $POST_BUILD
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__common_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__common_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__isr_tables_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__isr_tables_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core__cortex_m_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__cortex_m_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__cortex_m_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__cortex_m__cmse_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__cortex_m__cmse_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__mpu_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__mpu_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__picolibc_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__picolibc_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__common_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__common_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__posix__options_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__posix__options_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__net_buf_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__net_buf_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__os__zvfs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__os__zvfs_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__soc__nordic_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__soc__nordic_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__debug__coredump_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__debug__coredump_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__fs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__fs_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__random_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__random_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__l2__ethernet_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__l2__ethernet_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__l2__wifi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__l2__wifi_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__ip_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__ip_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__wifi_credentials_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__wifi_credentials_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__lib__dhcpv4_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__lib__dhcpv4_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__lib__dns_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__lib__dns_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net__conn_mgr_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net__conn_mgr_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__interrupt_controller_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__interrupt_controller_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__clock_control_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__clock_control_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__console_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__console_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__entropy_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__entropy_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__ethernet_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__ethernet_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__flash_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__flash_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__gpio_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__gpio_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__pinctrl_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__pinctrl_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__serial_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__serial_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__spi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__spi_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__timer_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__timer_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__wifi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__wifi_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__nrf_wifi_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__nrf_wifi_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__.2e.2e__nrf__lib__dk_buttons_and_leds_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__.2e.2e__nrf__lib__dk_buttons_and_leds_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedcrypto_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedcrypto_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__nrf_security_utils_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__nrf_security_utils_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedcrypto_base_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedcrypto_base_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedx509_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedx509_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedtls_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedtls_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__oberon_psa_core_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__oberon_psa_core_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__oberon_psa_driver_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__oberon_psa_driver_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedtls_zephyr_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedtls_zephyr_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__.2e.2e__nrf__subsys__net__l2_wifi_if_conn_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__.2e.2e__nrf__subsys__net__l2_wifi_if_conn_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__.2e.2e__nrf__drivers__hw_cc3xx_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__.2e.2e__nrf__drivers__hw_cc3xx_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__modules__hal_nordic__nrfx_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__modules__hal_nordic__nrfx_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__modules__littlefs_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__modules__littlefs_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__nrf-wifi-shim_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__nrf-wifi-shim_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__nrf-wifi-osal_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__nrf-wifi-osal_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__nrf70-buslib_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__nrf70-buslib_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__kernel_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache ${LAUNCHER}${CODE_CHECK}/opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__kernel_
  command = $PRE_LINK && ccache /opt/homebrew/bin/cmake -E rm -f $TARGET_FILE && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar qc $TARGET_FILE $LINK_FLAGS $in && ccache /opt/nordic/ncs/toolchains/ef4fc6722e/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib $TARGET_FILE && ccache /opt/homebrew/bin/cmake -E touch $TARGET_FILE && $POST_BUILD
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /opt/homebrew/bin/cmake --regenerate-during-build -S/opt/nordic/ncs/v3.0.2/wifi_udp_latency -B/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = /opt/homebrew/bin/cmake -DCONFIG=$CONFIG -P CMakeFiles/clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /opt/homebrew/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /opt/homebrew/bin/ninja -t targets
  description = All primary targets available:

