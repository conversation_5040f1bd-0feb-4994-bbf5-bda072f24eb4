# Generated devicetree Kconfig
#
# SPDX-License-Identifier: Apache-2.0

DT_COMPAT_ADAFRUIT_FEATHER_HEADER := adafruit-feather-header

config DT_HAS_ADAFRUIT_FEATHER_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADAFRUIT_FEATHER_HEADER))

DT_COMPAT_ADC_KEYS := adc-keys

config DT_HAS_ADC_KEYS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADC_KEYS))

DT_COMPAT_ADI_AD559X := adi,ad559x

config DT_HAS_ADI_AD559X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD559X))

DT_COMPAT_ADI_AD559X_ADC := adi,ad559x-adc

config DT_HAS_ADI_AD559X_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD559X_ADC))

DT_COMPAT_ADI_AD559X_DAC := adi,ad559x-dac

config DT_HAS_ADI_AD559X_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD559X_DAC))

DT_COMPAT_ADI_AD559X_GPIO := adi,ad559x-gpio

config DT_HAS_ADI_AD559X_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD559X_GPIO))

DT_COMPAT_ADI_AD5628 := adi,ad5628

config DT_HAS_ADI_AD5628_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5628))

DT_COMPAT_ADI_AD5648 := adi,ad5648

config DT_HAS_ADI_AD5648_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5648))

DT_COMPAT_ADI_AD5668 := adi,ad5668

config DT_HAS_ADI_AD5668_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5668))

DT_COMPAT_ADI_AD5672 := adi,ad5672

config DT_HAS_ADI_AD5672_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5672))

DT_COMPAT_ADI_AD5674 := adi,ad5674

config DT_HAS_ADI_AD5674_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5674))

DT_COMPAT_ADI_AD5676 := adi,ad5676

config DT_HAS_ADI_AD5676_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5676))

DT_COMPAT_ADI_AD5679 := adi,ad5679

config DT_HAS_ADI_AD5679_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5679))

DT_COMPAT_ADI_AD5684 := adi,ad5684

config DT_HAS_ADI_AD5684_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5684))

DT_COMPAT_ADI_AD5686 := adi,ad5686

config DT_HAS_ADI_AD5686_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5686))

DT_COMPAT_ADI_AD5687 := adi,ad5687

config DT_HAS_ADI_AD5687_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5687))

DT_COMPAT_ADI_AD5689 := adi,ad5689

config DT_HAS_ADI_AD5689_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5689))

DT_COMPAT_ADI_AD5691 := adi,ad5691

config DT_HAS_ADI_AD5691_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5691))

DT_COMPAT_ADI_AD5692 := adi,ad5692

config DT_HAS_ADI_AD5692_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5692))

DT_COMPAT_ADI_AD5693 := adi,ad5693

config DT_HAS_ADI_AD5693_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_AD5693))

DT_COMPAT_ADI_ADIN1100_PHY := adi,adin1100-phy

config DT_HAS_ADI_ADIN1100_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN1100_PHY))

DT_COMPAT_ADI_ADIN1110 := adi,adin1110

config DT_HAS_ADI_ADIN1110_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN1110))

DT_COMPAT_ADI_ADIN2111 := adi,adin2111

config DT_HAS_ADI_ADIN2111_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111))

DT_COMPAT_ADI_ADIN2111_MDIO := adi,adin2111-mdio

config DT_HAS_ADI_ADIN2111_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111_MDIO))

DT_COMPAT_ADI_ADIN2111_PHY := adi,adin2111-phy

config DT_HAS_ADI_ADIN2111_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111_PHY))

DT_COMPAT_ADI_ADLTC2990 := adi,adltc2990

config DT_HAS_ADI_ADLTC2990_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADLTC2990))

DT_COMPAT_ADI_ADP5360 := adi,adp5360

config DT_HAS_ADI_ADP5360_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5360))

DT_COMPAT_ADI_ADP5360_REGULATOR := adi,adp5360-regulator

config DT_HAS_ADI_ADP5360_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5360_REGULATOR))

DT_COMPAT_ADI_ADP5585 := adi,adp5585

config DT_HAS_ADI_ADP5585_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5585))

DT_COMPAT_ADI_ADP5585_GPIO := adi,adp5585-gpio

config DT_HAS_ADI_ADP5585_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5585_GPIO))

DT_COMPAT_ADI_ADT7310 := adi,adt7310

config DT_HAS_ADI_ADT7310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADT7310))

DT_COMPAT_ADI_ADT7420 := adi,adt7420

config DT_HAS_ADI_ADT7420_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADT7420))

DT_COMPAT_ADI_ADXL345 := adi,adxl345

config DT_HAS_ADI_ADXL345_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL345))

DT_COMPAT_ADI_ADXL362 := adi,adxl362

config DT_HAS_ADI_ADXL362_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL362))

DT_COMPAT_ADI_ADXL366 := adi,adxl366

config DT_HAS_ADI_ADXL366_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL366))

DT_COMPAT_ADI_ADXL367 := adi,adxl367

config DT_HAS_ADI_ADXL367_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL367))

DT_COMPAT_ADI_ADXL372 := adi,adxl372

config DT_HAS_ADI_ADXL372_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL372))

DT_COMPAT_ADI_MAX14906_GPIO := adi,max14906-gpio

config DT_HAS_ADI_MAX14906_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX14906_GPIO))

DT_COMPAT_ADI_MAX14916_GPIO := adi,max14916-gpio

config DT_HAS_ADI_MAX14916_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX14916_GPIO))

DT_COMPAT_ADI_MAX22190_GPIO := adi,max22190-gpio

config DT_HAS_ADI_MAX22190_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX22190_GPIO))

DT_COMPAT_ADI_MAX32_ADC := adi,max32-adc

config DT_HAS_ADI_MAX32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_ADC))

DT_COMPAT_ADI_MAX32_ADC_10B := adi,max32-adc-10b

config DT_HAS_ADI_MAX32_ADC_10B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_ADC_10B))

DT_COMPAT_ADI_MAX32_ADC_SAR := adi,max32-adc-sar

config DT_HAS_ADI_MAX32_ADC_SAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_ADC_SAR))

DT_COMPAT_ADI_MAX32_COUNTER := adi,max32-counter

config DT_HAS_ADI_MAX32_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_COUNTER))

DT_COMPAT_ADI_MAX32_DMA := adi,max32-dma

config DT_HAS_ADI_MAX32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_DMA))

DT_COMPAT_ADI_MAX32_FLASH_CONTROLLER := adi,max32-flash-controller

config DT_HAS_ADI_MAX32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_FLASH_CONTROLLER))

DT_COMPAT_ADI_MAX32_GCR := adi,max32-gcr

config DT_HAS_ADI_MAX32_GCR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_GCR))

DT_COMPAT_ADI_MAX32_GPIO := adi,max32-gpio

config DT_HAS_ADI_MAX32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_GPIO))

DT_COMPAT_ADI_MAX32_I2C := adi,max32-i2c

config DT_HAS_ADI_MAX32_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_I2C))

DT_COMPAT_ADI_MAX32_PINCTRL := adi,max32-pinctrl

config DT_HAS_ADI_MAX32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_PINCTRL))

DT_COMPAT_ADI_MAX32_PWM := adi,max32-pwm

config DT_HAS_ADI_MAX32_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_PWM))

DT_COMPAT_ADI_MAX32_RTC_COUNTER := adi,max32-rtc-counter

config DT_HAS_ADI_MAX32_RTC_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_RTC_COUNTER))

DT_COMPAT_ADI_MAX32_SPI := adi,max32-spi

config DT_HAS_ADI_MAX32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_SPI))

DT_COMPAT_ADI_MAX32_TIMER := adi,max32-timer

config DT_HAS_ADI_MAX32_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_TIMER))

DT_COMPAT_ADI_MAX32_TRNG := adi,max32-trng

config DT_HAS_ADI_MAX32_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_TRNG))

DT_COMPAT_ADI_MAX32_UART := adi,max32-uart

config DT_HAS_ADI_MAX32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_UART))

DT_COMPAT_ADI_MAX32_W1 := adi,max32-w1

config DT_HAS_ADI_MAX32_W1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_W1))

DT_COMPAT_ADI_MAX32_WATCHDOG := adi,max32-watchdog

config DT_HAS_ADI_MAX32_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_MAX32_WATCHDOG))

DT_COMPAT_ADI_SDP_120 := adi,sdp-120

config DT_HAS_ADI_SDP_120_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_SDP_120))

DT_COMPAT_ADI_TMC2209 := adi,tmc2209

config DT_HAS_ADI_TMC2209_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_TMC2209))

DT_COMPAT_ADI_TMC5041 := adi,tmc5041

config DT_HAS_ADI_TMC5041_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_TMC5041))

DT_COMPAT_ALTR_JTAG_UART := altr,jtag-uart

config DT_HAS_ALTR_JTAG_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_JTAG_UART))

DT_COMPAT_ALTR_MSGDMA := altr,msgdma

config DT_HAS_ALTR_MSGDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_MSGDMA))

DT_COMPAT_ALTR_NIOS2_I2C := altr,nios2-i2c

config DT_HAS_ALTR_NIOS2_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_I2C))

DT_COMPAT_ALTR_NIOS2_QSPI := altr,nios2-qspi

config DT_HAS_ALTR_NIOS2_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_QSPI))

DT_COMPAT_ALTR_NIOS2_QSPI_NOR := altr,nios2-qspi-nor

config DT_HAS_ALTR_NIOS2_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_QSPI_NOR))

DT_COMPAT_ALTR_NIOS2F := altr,nios2f

config DT_HAS_ALTR_NIOS2F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2F))

DT_COMPAT_ALTR_PIO_1_0 := altr,pio-1.0

config DT_HAS_ALTR_PIO_1_0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_PIO_1_0))

DT_COMPAT_ALTR_UART := altr,uart

config DT_HAS_ALTR_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_UART))

DT_COMPAT_AMBIQ_ADC := ambiq,adc

config DT_HAS_AMBIQ_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_ADC))

DT_COMPAT_AMBIQ_AM1805 := ambiq,am1805

config DT_HAS_AMBIQ_AM1805_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_AM1805))

DT_COMPAT_AMBIQ_APOLLO3_PINCTRL := ambiq,apollo3-pinctrl

config DT_HAS_AMBIQ_APOLLO3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_APOLLO3_PINCTRL))

DT_COMPAT_AMBIQ_APOLLO4_PINCTRL := ambiq,apollo4-pinctrl

config DT_HAS_AMBIQ_APOLLO4_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_APOLLO4_PINCTRL))

DT_COMPAT_AMBIQ_BT_HCI_SPI := ambiq,bt-hci-spi

config DT_HAS_AMBIQ_BT_HCI_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_BT_HCI_SPI))

DT_COMPAT_AMBIQ_CLKCTRL := ambiq,clkctrl

config DT_HAS_AMBIQ_CLKCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_CLKCTRL))

DT_COMPAT_AMBIQ_COUNTER := ambiq,counter

config DT_HAS_AMBIQ_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_COUNTER))

DT_COMPAT_AMBIQ_FLASH_CONTROLLER := ambiq,flash-controller

config DT_HAS_AMBIQ_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_FLASH_CONTROLLER))

DT_COMPAT_AMBIQ_GPIO := ambiq,gpio

config DT_HAS_AMBIQ_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_GPIO))

DT_COMPAT_AMBIQ_GPIO_BANK := ambiq,gpio-bank

config DT_HAS_AMBIQ_GPIO_BANK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_GPIO_BANK))

DT_COMPAT_AMBIQ_I2C := ambiq,i2c

config DT_HAS_AMBIQ_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_I2C))

DT_COMPAT_AMBIQ_MSPI := ambiq,mspi

config DT_HAS_AMBIQ_MSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_MSPI))

DT_COMPAT_AMBIQ_MSPI_CONTROLLER := ambiq,mspi-controller

config DT_HAS_AMBIQ_MSPI_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_MSPI_CONTROLLER))

DT_COMPAT_AMBIQ_MSPI_DEVICE := ambiq,mspi-device

config DT_HAS_AMBIQ_MSPI_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_MSPI_DEVICE))

DT_COMPAT_AMBIQ_PWRCTRL := ambiq,pwrctrl

config DT_HAS_AMBIQ_PWRCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_PWRCTRL))

DT_COMPAT_AMBIQ_RTC := ambiq,rtc

config DT_HAS_AMBIQ_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_RTC))

DT_COMPAT_AMBIQ_SPI := ambiq,spi

config DT_HAS_AMBIQ_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_SPI))

DT_COMPAT_AMBIQ_SPI_BLEIF := ambiq,spi-bleif

config DT_HAS_AMBIQ_SPI_BLEIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_SPI_BLEIF))

DT_COMPAT_AMBIQ_SPID := ambiq,spid

config DT_HAS_AMBIQ_SPID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_SPID))

DT_COMPAT_AMBIQ_STIMER := ambiq,stimer

config DT_HAS_AMBIQ_STIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_STIMER))

DT_COMPAT_AMBIQ_UART := ambiq,uart

config DT_HAS_AMBIQ_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_UART))

DT_COMPAT_AMBIQ_USB := ambiq,usb

config DT_HAS_AMBIQ_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_USB))

DT_COMPAT_AMBIQ_WATCHDOG := ambiq,watchdog

config DT_HAS_AMBIQ_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_WATCHDOG))

DT_COMPAT_AMBIQ_HEADER := ambiq-header

config DT_HAS_AMBIQ_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_HEADER))

DT_COMPAT_AMD_SB_TSI := amd,sb-tsi

config DT_HAS_AMD_SB_TSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMD_SB_TSI))

DT_COMPAT_AMS_AS5600 := ams,as5600

config DT_HAS_AMS_AS5600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_AS5600))

DT_COMPAT_AMS_AS6212 := ams,as6212

config DT_HAS_AMS_AS6212_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_AS6212))

DT_COMPAT_AMS_CCS811 := ams,ccs811

config DT_HAS_AMS_CCS811_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_CCS811))

DT_COMPAT_AMS_ENS210 := ams,ens210

config DT_HAS_AMS_ENS210_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_ENS210))

DT_COMPAT_AMS_IAQCORE := ams,iaqcore

config DT_HAS_AMS_IAQCORE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_IAQCORE))

DT_COMPAT_AMS_TCS3400 := ams,tcs3400

config DT_HAS_AMS_TCS3400_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TCS3400))

DT_COMPAT_AMS_TMD2620 := ams,tmd2620

config DT_HAS_AMS_TMD2620_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TMD2620))

DT_COMPAT_AMS_TSL2540 := ams,tsl2540

config DT_HAS_AMS_TSL2540_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TSL2540))

DT_COMPAT_AMS_TSL2561 := ams,tsl2561

config DT_HAS_AMS_TSL2561_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TSL2561))

DT_COMPAT_AMS_TSL2591 := ams,tsl2591

config DT_HAS_AMS_TSL2591_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TSL2591))

DT_COMPAT_ANALOG_AXIS := analog-axis

config DT_HAS_ANALOG_AXIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANALOG_AXIS))

DT_COMPAT_ANDESTECH_ANDESCORE_V5 := andestech,andescore-v5

config DT_HAS_ANDESTECH_ANDESCORE_V5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ANDESCORE_V5))

DT_COMPAT_ANDESTECH_ATCDMAC300 := andestech,atcdmac300

config DT_HAS_ANDESTECH_ATCDMAC300_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCDMAC300))

DT_COMPAT_ANDESTECH_ATCGPIO100 := andestech,atcgpio100

config DT_HAS_ANDESTECH_ATCGPIO100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCGPIO100))

DT_COMPAT_ANDESTECH_ATCIIC100 := andestech,atciic100

config DT_HAS_ANDESTECH_ATCIIC100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCIIC100))

DT_COMPAT_ANDESTECH_ATCPIT100 := andestech,atcpit100

config DT_HAS_ANDESTECH_ATCPIT100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCPIT100))

DT_COMPAT_ANDESTECH_ATCSPI200 := andestech,atcspi200

config DT_HAS_ANDESTECH_ATCSPI200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCSPI200))

DT_COMPAT_ANDESTECH_ATCWDT200 := andestech,atcwdt200

config DT_HAS_ANDESTECH_ATCWDT200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCWDT200))

DT_COMPAT_ANDESTECH_L2C := andestech,l2c

config DT_HAS_ANDESTECH_L2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_L2C))

DT_COMPAT_ANDESTECH_MACHINE_TIMER := andestech,machine-timer

config DT_HAS_ANDESTECH_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_MACHINE_TIMER))

DT_COMPAT_ANDESTECH_MBOX_PLIC_SW := andestech,mbox-plic-sw

config DT_HAS_ANDESTECH_MBOX_PLIC_SW_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_MBOX_PLIC_SW))

DT_COMPAT_ANDESTECH_NCEPLIC100 := andestech,nceplic100

config DT_HAS_ANDESTECH_NCEPLIC100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_NCEPLIC100))

DT_COMPAT_ANDESTECH_QSPI_NOR := andestech,qspi-nor

config DT_HAS_ANDESTECH_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_QSPI_NOR))

DT_COMPAT_AOSONG_AGS10 := aosong,ags10

config DT_HAS_AOSONG_AGS10_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_AGS10))

DT_COMPAT_AOSONG_AHT20 := aosong,aht20

config DT_HAS_AOSONG_AHT20_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_AHT20))

DT_COMPAT_AOSONG_AM2301B := aosong,am2301b

config DT_HAS_AOSONG_AM2301B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_AM2301B))

DT_COMPAT_AOSONG_DHT := aosong,dht

config DT_HAS_AOSONG_DHT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_DHT))

DT_COMPAT_AOSONG_DHT20 := aosong,dht20

config DT_HAS_AOSONG_DHT20_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_DHT20))

DT_COMPAT_AP_FCX_MLDX5 := ap,fcx-mldx5

config DT_HAS_AP_FCX_MLDX5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AP_FCX_MLDX5))

DT_COMPAT_APA_APA102 := apa,apa102

config DT_HAS_APA_APA102_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_APA_APA102))

DT_COMPAT_APTINA_MT9M114 := aptina,mt9m114

config DT_HAS_APTINA_MT9M114_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_APTINA_MT9M114))

DT_COMPAT_ARC_DCCM := arc,dccm

config DT_HAS_ARC_DCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_DCCM))

DT_COMPAT_ARC_ICCM := arc,iccm

config DT_HAS_ARC_ICCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_ICCM))

DT_COMPAT_ARC_XCCM := arc,xccm

config DT_HAS_ARC_XCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_XCCM))

DT_COMPAT_ARC_YCCM := arc,yccm

config DT_HAS_ARC_YCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_YCCM))

DT_COMPAT_ARDUINO_UNO_ADC := arduino,uno-adc

config DT_HAS_ARDUINO_UNO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_UNO_ADC))

DT_COMPAT_ARDUINO_HEADER_R3 := arduino-header-r3

config DT_HAS_ARDUINO_HEADER_R3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_HEADER_R3))

DT_COMPAT_ARDUINO_MKR_HEADER := arduino-mkr-header

config DT_HAS_ARDUINO_MKR_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_MKR_HEADER))

DT_COMPAT_ARDUINO_NANO_HEADER_R3 := arduino-nano-header-r3

config DT_HAS_ARDUINO_NANO_HEADER_R3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_NANO_HEADER_R3))

DT_COMPAT_ARM_ARMV6M_MPU := arm,armv6m-mpu

config DT_HAS_ARM_ARMV6M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV6M_MPU))

DT_COMPAT_ARM_ARMV6M_SYSTICK := arm,armv6m-systick

config DT_HAS_ARM_ARMV6M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV6M_SYSTICK))

DT_COMPAT_ARM_ARMV7M_ITM := arm,armv7m-itm

config DT_HAS_ARM_ARMV7M_ITM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_ITM))

DT_COMPAT_ARM_ARMV7M_MPU := arm,armv7m-mpu

config DT_HAS_ARM_ARMV7M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_MPU))

DT_COMPAT_ARM_ARMV7M_SYSTICK := arm,armv7m-systick

config DT_HAS_ARM_ARMV7M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_SYSTICK))

DT_COMPAT_ARM_ARMV8_TIMER := arm,armv8-timer

config DT_HAS_ARM_ARMV8_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_TIMER))

DT_COMPAT_ARM_ARMV8_1M_MPU := arm,armv8.1m-mpu

config DT_HAS_ARM_ARMV8_1M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_1M_MPU))

DT_COMPAT_ARM_ARMV8_1M_SYSTICK := arm,armv8.1m-systick

config DT_HAS_ARM_ARMV8_1M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_1M_SYSTICK))

DT_COMPAT_ARM_ARMV8M_ITM := arm,armv8m-itm

config DT_HAS_ARM_ARMV8M_ITM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_ITM))

DT_COMPAT_ARM_ARMV8M_MPU := arm,armv8m-mpu

config DT_HAS_ARM_ARMV8M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_MPU))

DT_COMPAT_ARM_ARMV8M_SYSTICK := arm,armv8m-systick

config DT_HAS_ARM_ARMV8M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_SYSTICK))

DT_COMPAT_ARM_BEETLE_SYSCON := arm,beetle-syscon

config DT_HAS_ARM_BEETLE_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_BEETLE_SYSCON))

DT_COMPAT_ARM_CMSDK_DTIMER := arm,cmsdk-dtimer

config DT_HAS_ARM_CMSDK_DTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_DTIMER))

DT_COMPAT_ARM_CMSDK_GPIO := arm,cmsdk-gpio

config DT_HAS_ARM_CMSDK_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_GPIO))

DT_COMPAT_ARM_CMSDK_TIMER := arm,cmsdk-timer

config DT_HAS_ARM_CMSDK_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_TIMER))

DT_COMPAT_ARM_CMSDK_UART := arm,cmsdk-uart

config DT_HAS_ARM_CMSDK_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_UART))

DT_COMPAT_ARM_CMSDK_WATCHDOG := arm,cmsdk-watchdog

config DT_HAS_ARM_CMSDK_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_WATCHDOG))

DT_COMPAT_ARM_CORTEX_A53 := arm,cortex-a53

config DT_HAS_ARM_CORTEX_A53_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A53))

DT_COMPAT_ARM_CORTEX_A55 := arm,cortex-a55

config DT_HAS_ARM_CORTEX_A55_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A55))

DT_COMPAT_ARM_CORTEX_A72 := arm,cortex-a72

config DT_HAS_ARM_CORTEX_A72_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A72))

DT_COMPAT_ARM_CORTEX_A76 := arm,cortex-a76

config DT_HAS_ARM_CORTEX_A76_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A76))

DT_COMPAT_ARM_CORTEX_M0 := arm,cortex-m0

config DT_HAS_ARM_CORTEX_M0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M0))

DT_COMPAT_ARM_CORTEX_M0_ := arm,cortex-m0+

config DT_HAS_ARM_CORTEX_M0__ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M0_))

DT_COMPAT_ARM_CORTEX_M1 := arm,cortex-m1

config DT_HAS_ARM_CORTEX_M1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M1))

DT_COMPAT_ARM_CORTEX_M23 := arm,cortex-m23

config DT_HAS_ARM_CORTEX_M23_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M23))

DT_COMPAT_ARM_CORTEX_M3 := arm,cortex-m3

config DT_HAS_ARM_CORTEX_M3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M3))

DT_COMPAT_ARM_CORTEX_M33 := arm,cortex-m33

config DT_HAS_ARM_CORTEX_M33_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M33))

DT_COMPAT_ARM_CORTEX_M33F := arm,cortex-m33f

config DT_HAS_ARM_CORTEX_M33F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M33F))

DT_COMPAT_ARM_CORTEX_M4 := arm,cortex-m4

config DT_HAS_ARM_CORTEX_M4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M4))

DT_COMPAT_ARM_CORTEX_M4F := arm,cortex-m4f

config DT_HAS_ARM_CORTEX_M4F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M4F))

DT_COMPAT_ARM_CORTEX_M55 := arm,cortex-m55

config DT_HAS_ARM_CORTEX_M55_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M55))

DT_COMPAT_ARM_CORTEX_M55F := arm,cortex-m55f

config DT_HAS_ARM_CORTEX_M55F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M55F))

DT_COMPAT_ARM_CORTEX_M7 := arm,cortex-m7

config DT_HAS_ARM_CORTEX_M7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M7))

DT_COMPAT_ARM_CORTEX_M85 := arm,cortex-m85

config DT_HAS_ARM_CORTEX_M85_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M85))

DT_COMPAT_ARM_CORTEX_M85F := arm,cortex-m85f

config DT_HAS_ARM_CORTEX_M85F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M85F))

DT_COMPAT_ARM_CORTEX_R4 := arm,cortex-r4

config DT_HAS_ARM_CORTEX_R4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R4))

DT_COMPAT_ARM_CORTEX_R4F := arm,cortex-r4f

config DT_HAS_ARM_CORTEX_R4F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R4F))

DT_COMPAT_ARM_CORTEX_R5 := arm,cortex-r5

config DT_HAS_ARM_CORTEX_R5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R5))

DT_COMPAT_ARM_CORTEX_R52 := arm,cortex-r52

config DT_HAS_ARM_CORTEX_R52_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R52))

DT_COMPAT_ARM_CORTEX_R5F := arm,cortex-r5f

config DT_HAS_ARM_CORTEX_R5F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R5F))

DT_COMPAT_ARM_CORTEX_R7 := arm,cortex-r7

config DT_HAS_ARM_CORTEX_R7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R7))

DT_COMPAT_ARM_CORTEX_R82 := arm,cortex-r82

config DT_HAS_ARM_CORTEX_R82_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R82))

DT_COMPAT_ARM_CRYPTOCELL_310 := arm,cryptocell-310

config DT_HAS_ARM_CRYPTOCELL_310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CRYPTOCELL_310))

DT_COMPAT_ARM_CRYPTOCELL_312 := arm,cryptocell-312

config DT_HAS_ARM_CRYPTOCELL_312_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CRYPTOCELL_312))

DT_COMPAT_ARM_DMA_PL330 := arm,dma-pl330

config DT_HAS_ARM_DMA_PL330_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_DMA_PL330))

DT_COMPAT_ARM_DTCM := arm,dtcm

config DT_HAS_ARM_DTCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_DTCM))

DT_COMPAT_ARM_ETHOS_U := arm,ethos-u

config DT_HAS_ARM_ETHOS_U_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ETHOS_U))

DT_COMPAT_ARM_GIC := arm,gic

config DT_HAS_ARM_GIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC))

DT_COMPAT_ARM_GIC_V1 := arm,gic-v1

config DT_HAS_ARM_GIC_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V1))

DT_COMPAT_ARM_GIC_V2 := arm,gic-v2

config DT_HAS_ARM_GIC_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V2))

DT_COMPAT_ARM_GIC_V3 := arm,gic-v3

config DT_HAS_ARM_GIC_V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V3))

DT_COMPAT_ARM_GIC_V3_ITS := arm,gic-v3-its

config DT_HAS_ARM_GIC_V3_ITS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V3_ITS))

DT_COMPAT_ARM_ITCM := arm,itcm

config DT_HAS_ARM_ITCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ITCM))

DT_COMPAT_ARM_MHU := arm,mhu

config DT_HAS_ARM_MHU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MHU))

DT_COMPAT_ARM_MPS2_FPGAIO_GPIO := arm,mps2-fpgaio-gpio

config DT_HAS_ARM_MPS2_FPGAIO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MPS2_FPGAIO_GPIO))

DT_COMPAT_ARM_MPS3_FPGAIO_GPIO := arm,mps3-fpgaio-gpio

config DT_HAS_ARM_MPS3_FPGAIO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MPS3_FPGAIO_GPIO))

DT_COMPAT_ARM_PL011 := arm,pl011

config DT_HAS_ARM_PL011_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PL011))

DT_COMPAT_ARM_PL022 := arm,pl022

config DT_HAS_ARM_PL022_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PL022))

DT_COMPAT_ARM_PSCI_0_2 := arm,psci-0.2

config DT_HAS_ARM_PSCI_0_2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PSCI_0_2))

DT_COMPAT_ARM_PSCI_1_1 := arm,psci-1.1

config DT_HAS_ARM_PSCI_1_1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PSCI_1_1))

DT_COMPAT_ARM_SBSA_UART := arm,sbsa-uart

config DT_HAS_ARM_SBSA_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SBSA_UART))

DT_COMPAT_ARM_SCC := arm,scc

config DT_HAS_ARM_SCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCC))

DT_COMPAT_ARM_SCMI := arm,scmi

config DT_HAS_ARM_SCMI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCMI))

DT_COMPAT_ARM_SCMI_CLOCK := arm,scmi-clock

config DT_HAS_ARM_SCMI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCMI_CLOCK))

DT_COMPAT_ARM_SCMI_PINCTRL := arm,scmi-pinctrl

config DT_HAS_ARM_SCMI_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCMI_PINCTRL))

DT_COMPAT_ARM_SCMI_SHMEM := arm,scmi-shmem

config DT_HAS_ARM_SCMI_SHMEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCMI_SHMEM))

DT_COMPAT_ARM_V6M_NVIC := arm,v6m-nvic

config DT_HAS_ARM_V6M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V6M_NVIC))

DT_COMPAT_ARM_V7M_NVIC := arm,v7m-nvic

config DT_HAS_ARM_V7M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V7M_NVIC))

DT_COMPAT_ARM_V8_1M_NVIC := arm,v8.1m-nvic

config DT_HAS_ARM_V8_1M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V8_1M_NVIC))

DT_COMPAT_ARM_V8M_NVIC := arm,v8m-nvic

config DT_HAS_ARM_V8M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V8M_NVIC))

DT_COMPAT_ARM_VERSATILE_I2C := arm,versatile-i2c

config DT_HAS_ARM_VERSATILE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_VERSATILE_I2C))

DT_COMPAT_ASAHI_KASEI_AK8975 := asahi-kasei,ak8975

config DT_HAS_ASAHI_KASEI_AK8975_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASAHI_KASEI_AK8975))

DT_COMPAT_ASAHI_KASEI_AKM09918C := asahi-kasei,akm09918c

config DT_HAS_ASAHI_KASEI_AKM09918C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASAHI_KASEI_AKM09918C))

DT_COMPAT_ASMEDIA_ASM2364 := asmedia,asm2364

config DT_HAS_ASMEDIA_ASM2364_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASMEDIA_ASM2364))

DT_COMPAT_ASPEED_AST10X0_CLOCK := aspeed,ast10x0-clock

config DT_HAS_ASPEED_AST10X0_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASPEED_AST10X0_CLOCK))

DT_COMPAT_ASPEED_AST10X0_RESET := aspeed,ast10x0-reset

config DT_HAS_ASPEED_AST10X0_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASPEED_AST10X0_RESET))

DT_COMPAT_ATMEL_AT24 := atmel,at24

config DT_HAS_ATMEL_AT24_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT24))

DT_COMPAT_ATMEL_AT24MAC402 := atmel,at24mac402

config DT_HAS_ATMEL_AT24MAC402_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT24MAC402))

DT_COMPAT_ATMEL_AT25 := atmel,at25

config DT_HAS_ATMEL_AT25_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT25))

DT_COMPAT_ATMEL_AT45 := atmel,at45

config DT_HAS_ATMEL_AT45_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT45))

DT_COMPAT_ATMEL_ATAES132A := atmel,ataes132a

config DT_HAS_ATMEL_ATAES132A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_ATAES132A))

DT_COMPAT_ATMEL_RF2XX := atmel,rf2xx

config DT_HAS_ATMEL_RF2XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_RF2XX))

DT_COMPAT_ATMEL_SAM_ADC := atmel,sam-adc

config DT_HAS_ATMEL_SAM_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_ADC))

DT_COMPAT_ATMEL_SAM_AFEC := atmel,sam-afec

config DT_HAS_ATMEL_SAM_AFEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_AFEC))

DT_COMPAT_ATMEL_SAM_CAN := atmel,sam-can

config DT_HAS_ATMEL_SAM_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_CAN))

DT_COMPAT_ATMEL_SAM_DAC := atmel,sam-dac

config DT_HAS_ATMEL_SAM_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_DAC))

DT_COMPAT_ATMEL_SAM_FLASH := atmel,sam-flash

config DT_HAS_ATMEL_SAM_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_FLASH))

DT_COMPAT_ATMEL_SAM_FLASH_CONTROLLER := atmel,sam-flash-controller

config DT_HAS_ATMEL_SAM_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_FLASH_CONTROLLER))

DT_COMPAT_ATMEL_SAM_GMAC := atmel,sam-gmac

config DT_HAS_ATMEL_SAM_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_GMAC))

DT_COMPAT_ATMEL_SAM_GPIO := atmel,sam-gpio

config DT_HAS_ATMEL_SAM_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_GPIO))

DT_COMPAT_ATMEL_SAM_HSMCI := atmel,sam-hsmci

config DT_HAS_ATMEL_SAM_HSMCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_HSMCI))

DT_COMPAT_ATMEL_SAM_I2C_TWI := atmel,sam-i2c-twi

config DT_HAS_ATMEL_SAM_I2C_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWI))

DT_COMPAT_ATMEL_SAM_I2C_TWIHS := atmel,sam-i2c-twihs

config DT_HAS_ATMEL_SAM_I2C_TWIHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWIHS))

DT_COMPAT_ATMEL_SAM_I2C_TWIM := atmel,sam-i2c-twim

config DT_HAS_ATMEL_SAM_I2C_TWIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWIM))

DT_COMPAT_ATMEL_SAM_MDIO := atmel,sam-mdio

config DT_HAS_ATMEL_SAM_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_MDIO))

DT_COMPAT_ATMEL_SAM_PINCTRL := atmel,sam-pinctrl

config DT_HAS_ATMEL_SAM_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PINCTRL))

DT_COMPAT_ATMEL_SAM_PMC := atmel,sam-pmc

config DT_HAS_ATMEL_SAM_PMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PMC))

DT_COMPAT_ATMEL_SAM_PWM := atmel,sam-pwm

config DT_HAS_ATMEL_SAM_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PWM))

DT_COMPAT_ATMEL_SAM_RSTC := atmel,sam-rstc

config DT_HAS_ATMEL_SAM_RSTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_RSTC))

DT_COMPAT_ATMEL_SAM_RTC := atmel,sam-rtc

config DT_HAS_ATMEL_SAM_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_RTC))

DT_COMPAT_ATMEL_SAM_SMC := atmel,sam-smc

config DT_HAS_ATMEL_SAM_SMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SMC))

DT_COMPAT_ATMEL_SAM_SPI := atmel,sam-spi

config DT_HAS_ATMEL_SAM_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SPI))

DT_COMPAT_ATMEL_SAM_SSC := atmel,sam-ssc

config DT_HAS_ATMEL_SAM_SSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SSC))

DT_COMPAT_ATMEL_SAM_SUPC := atmel,sam-supc

config DT_HAS_ATMEL_SAM_SUPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SUPC))

DT_COMPAT_ATMEL_SAM_TC := atmel,sam-tc

config DT_HAS_ATMEL_SAM_TC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TC))

DT_COMPAT_ATMEL_SAM_TC_QDEC := atmel,sam-tc-qdec

config DT_HAS_ATMEL_SAM_TC_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TC_QDEC))

DT_COMPAT_ATMEL_SAM_TRNG := atmel,sam-trng

config DT_HAS_ATMEL_SAM_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TRNG))

DT_COMPAT_ATMEL_SAM_UART := atmel,sam-uart

config DT_HAS_ATMEL_SAM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_UART))

DT_COMPAT_ATMEL_SAM_USART := atmel,sam-usart

config DT_HAS_ATMEL_SAM_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USART))

DT_COMPAT_ATMEL_SAM_USBC := atmel,sam-usbc

config DT_HAS_ATMEL_SAM_USBC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USBC))

DT_COMPAT_ATMEL_SAM_USBHS := atmel,sam-usbhs

config DT_HAS_ATMEL_SAM_USBHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USBHS))

DT_COMPAT_ATMEL_SAM_WATCHDOG := atmel,sam-watchdog

config DT_HAS_ATMEL_SAM_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_WATCHDOG))

DT_COMPAT_ATMEL_SAM_XDMAC := atmel,sam-xdmac

config DT_HAS_ATMEL_SAM_XDMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_XDMAC))

DT_COMPAT_ATMEL_SAM0_ADC := atmel,sam0-adc

config DT_HAS_ATMEL_SAM0_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_ADC))

DT_COMPAT_ATMEL_SAM0_CAN := atmel,sam0-can

config DT_HAS_ATMEL_SAM0_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_CAN))

DT_COMPAT_ATMEL_SAM0_DAC := atmel,sam0-dac

config DT_HAS_ATMEL_SAM0_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_DAC))

DT_COMPAT_ATMEL_SAM0_DMAC := atmel,sam0-dmac

config DT_HAS_ATMEL_SAM0_DMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_DMAC))

DT_COMPAT_ATMEL_SAM0_EIC := atmel,sam0-eic

config DT_HAS_ATMEL_SAM0_EIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_EIC))

DT_COMPAT_ATMEL_SAM0_GMAC := atmel,sam0-gmac

config DT_HAS_ATMEL_SAM0_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_GMAC))

DT_COMPAT_ATMEL_SAM0_GPIO := atmel,sam0-gpio

config DT_HAS_ATMEL_SAM0_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_GPIO))

DT_COMPAT_ATMEL_SAM0_I2C := atmel,sam0-i2c

config DT_HAS_ATMEL_SAM0_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_I2C))

DT_COMPAT_ATMEL_SAM0_ID := atmel,sam0-id

config DT_HAS_ATMEL_SAM0_ID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_ID))

DT_COMPAT_ATMEL_SAM0_NVMCTRL := atmel,sam0-nvmctrl

config DT_HAS_ATMEL_SAM0_NVMCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_NVMCTRL))

DT_COMPAT_ATMEL_SAM0_PINCTRL := atmel,sam0-pinctrl

config DT_HAS_ATMEL_SAM0_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_PINCTRL))

DT_COMPAT_ATMEL_SAM0_PINMUX := atmel,sam0-pinmux

config DT_HAS_ATMEL_SAM0_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_PINMUX))

DT_COMPAT_ATMEL_SAM0_RTC := atmel,sam0-rtc

config DT_HAS_ATMEL_SAM0_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_RTC))

DT_COMPAT_ATMEL_SAM0_SERCOM := atmel,sam0-sercom

config DT_HAS_ATMEL_SAM0_SERCOM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_SERCOM))

DT_COMPAT_ATMEL_SAM0_SPI := atmel,sam0-spi

config DT_HAS_ATMEL_SAM0_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_SPI))

DT_COMPAT_ATMEL_SAM0_TC_PWM := atmel,sam0-tc-pwm

config DT_HAS_ATMEL_SAM0_TC_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_TC_PWM))

DT_COMPAT_ATMEL_SAM0_TC32 := atmel,sam0-tc32

config DT_HAS_ATMEL_SAM0_TC32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_TC32))

DT_COMPAT_ATMEL_SAM0_TCC_PWM := atmel,sam0-tcc-pwm

config DT_HAS_ATMEL_SAM0_TCC_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_TCC_PWM))

DT_COMPAT_ATMEL_SAM0_UART := atmel,sam0-uart

config DT_HAS_ATMEL_SAM0_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_UART))

DT_COMPAT_ATMEL_SAM0_USB := atmel,sam0-usb

config DT_HAS_ATMEL_SAM0_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_USB))

DT_COMPAT_ATMEL_SAM0_WATCHDOG := atmel,sam0-watchdog

config DT_HAS_ATMEL_SAM0_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_WATCHDOG))

DT_COMPAT_ATMEL_SAM4L_FLASHCALW_CONTROLLER := atmel,sam4l-flashcalw-controller

config DT_HAS_ATMEL_SAM4L_FLASHCALW_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_FLASHCALW_CONTROLLER))

DT_COMPAT_ATMEL_SAM4L_GPIO := atmel,sam4l-gpio

config DT_HAS_ATMEL_SAM4L_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_GPIO))

DT_COMPAT_ATMEL_SAM4L_UID := atmel,sam4l-uid

config DT_HAS_ATMEL_SAM4L_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_UID))

DT_COMPAT_ATMEL_SAMC2X_GCLK := atmel,samc2x-gclk

config DT_HAS_ATMEL_SAMC2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMC2X_GCLK))

DT_COMPAT_ATMEL_SAMC2X_MCLK := atmel,samc2x-mclk

config DT_HAS_ATMEL_SAMC2X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMC2X_MCLK))

DT_COMPAT_ATMEL_SAMD2X_GCLK := atmel,samd2x-gclk

config DT_HAS_ATMEL_SAMD2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD2X_GCLK))

DT_COMPAT_ATMEL_SAMD2X_PM := atmel,samd2x-pm

config DT_HAS_ATMEL_SAMD2X_PM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD2X_PM))

DT_COMPAT_ATMEL_SAMD5X_GCLK := atmel,samd5x-gclk

config DT_HAS_ATMEL_SAMD5X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD5X_GCLK))

DT_COMPAT_ATMEL_SAMD5X_MCLK := atmel,samd5x-mclk

config DT_HAS_ATMEL_SAMD5X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD5X_MCLK))

DT_COMPAT_ATMEL_SAML2X_GCLK := atmel,saml2x-gclk

config DT_HAS_ATMEL_SAML2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAML2X_GCLK))

DT_COMPAT_ATMEL_SAML2X_MCLK := atmel,saml2x-mclk

config DT_HAS_ATMEL_SAML2X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAML2X_MCLK))

DT_COMPAT_ATMEL_WINC1500 := atmel,winc1500

config DT_HAS_ATMEL_WINC1500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_WINC1500))

DT_COMPAT_ATMEL_XPLAINED_HEADER := atmel-xplained-header

config DT_HAS_ATMEL_XPLAINED_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_XPLAINED_HEADER))

DT_COMPAT_ATMEL_XPLAINED_PRO_HEADER := atmel-xplained-pro-header

config DT_HAS_ATMEL_XPLAINED_PRO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_XPLAINED_PRO_HEADER))

DT_COMPAT_AVAGO_APDS9253 := avago,apds9253

config DT_HAS_AVAGO_APDS9253_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AVAGO_APDS9253))

DT_COMPAT_AVAGO_APDS9306 := avago,apds9306

config DT_HAS_AVAGO_APDS9306_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AVAGO_APDS9306))

DT_COMPAT_AVAGO_APDS9960 := avago,apds9960

config DT_HAS_AVAGO_APDS9960_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AVAGO_APDS9960))

DT_COMPAT_AWINIC_AW9523B := awinic,aw9523b

config DT_HAS_AWINIC_AW9523B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AWINIC_AW9523B))

DT_COMPAT_AWINIC_AW9523B_GPIO := awinic,aw9523b-gpio

config DT_HAS_AWINIC_AW9523B_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AWINIC_AW9523B_GPIO))

DT_COMPAT_BOSCH_BMA280 := bosch,bma280

config DT_HAS_BOSCH_BMA280_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMA280))

DT_COMPAT_BOSCH_BMA4XX := bosch,bma4xx

config DT_HAS_BOSCH_BMA4XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMA4XX))

DT_COMPAT_BOSCH_BMC150_MAGN := bosch,bmc150_magn

config DT_HAS_BOSCH_BMC150_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMC150_MAGN))

DT_COMPAT_BOSCH_BME280 := bosch,bme280

config DT_HAS_BOSCH_BME280_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BME280))

DT_COMPAT_BOSCH_BME680 := bosch,bme680

config DT_HAS_BOSCH_BME680_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BME680))

DT_COMPAT_BOSCH_BMG160 := bosch,bmg160

config DT_HAS_BOSCH_BMG160_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMG160))

DT_COMPAT_BOSCH_BMI08X_ACCEL := bosch,bmi08x-accel

config DT_HAS_BOSCH_BMI08X_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI08X_ACCEL))

DT_COMPAT_BOSCH_BMI08X_GYRO := bosch,bmi08x-gyro

config DT_HAS_BOSCH_BMI08X_GYRO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI08X_GYRO))

DT_COMPAT_BOSCH_BMI160 := bosch,bmi160

config DT_HAS_BOSCH_BMI160_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI160))

DT_COMPAT_BOSCH_BMI270 := bosch,bmi270

config DT_HAS_BOSCH_BMI270_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI270))

DT_COMPAT_BOSCH_BMI323 := bosch,bmi323

config DT_HAS_BOSCH_BMI323_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI323))

DT_COMPAT_BOSCH_BMM150 := bosch,bmm150

config DT_HAS_BOSCH_BMM150_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMM150))

DT_COMPAT_BOSCH_BMM350 := bosch,bmm350

config DT_HAS_BOSCH_BMM350_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMM350))

DT_COMPAT_BOSCH_BMP180 := bosch,bmp180

config DT_HAS_BOSCH_BMP180_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMP180))

DT_COMPAT_BOSCH_BMP388 := bosch,bmp388

config DT_HAS_BOSCH_BMP388_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMP388))

DT_COMPAT_BOSCH_BMP390 := bosch,bmp390

config DT_HAS_BOSCH_BMP390_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMP390))

DT_COMPAT_BOSCH_BMP581 := bosch,bmp581

config DT_HAS_BOSCH_BMP581_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMP581))

DT_COMPAT_BRCM_BCM2711_AUX_UART := brcm,bcm2711-aux-uart

config DT_HAS_BRCM_BCM2711_AUX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_BCM2711_AUX_UART))

DT_COMPAT_BRCM_BCM2711_GPIO := brcm,bcm2711-gpio

config DT_HAS_BRCM_BCM2711_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_BCM2711_GPIO))

DT_COMPAT_BRCM_BRCMSTB_GPIO := brcm,brcmstb-gpio

config DT_HAS_BRCM_BRCMSTB_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_BRCMSTB_GPIO))

DT_COMPAT_BRCM_IPROC_GPIO := brcm,iproc-gpio

config DT_HAS_BRCM_IPROC_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_GPIO))

DT_COMPAT_BRCM_IPROC_I2C := brcm,iproc-i2c

config DT_HAS_BRCM_IPROC_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_I2C))

DT_COMPAT_BRCM_IPROC_PAX_DMA_V1 := brcm,iproc-pax-dma-v1

config DT_HAS_BRCM_IPROC_PAX_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PAX_DMA_V1))

DT_COMPAT_BRCM_IPROC_PAX_DMA_V2 := brcm,iproc-pax-dma-v2

config DT_HAS_BRCM_IPROC_PAX_DMA_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PAX_DMA_V2))

DT_COMPAT_BRCM_IPROC_PCIE_EP := brcm,iproc-pcie-ep

config DT_HAS_BRCM_IPROC_PCIE_EP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PCIE_EP))

DT_COMPAT_CAF_AGGREGATOR := caf,aggregator

config DT_HAS_CAF_AGGREGATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CAF_AGGREGATOR))

DT_COMPAT_CAN_TRANSCEIVER_GPIO := can-transceiver-gpio

config DT_HAS_CAN_TRANSCEIVER_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CAN_TRANSCEIVER_GPIO))

DT_COMPAT_CDNS_I3C := cdns,i3c

config DT_HAS_CDNS_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_I3C))

DT_COMPAT_CDNS_NAND := cdns,nand

config DT_HAS_CDNS_NAND_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_NAND))

DT_COMPAT_CDNS_QSPI_NOR := cdns,qspi-nor

config DT_HAS_CDNS_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_QSPI_NOR))

DT_COMPAT_CDNS_SDHC := cdns,sdhc

config DT_HAS_CDNS_SDHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_SDHC))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX3 := cdns,tensilica-xtensa-lx3

config DT_HAS_CDNS_TENSILICA_XTENSA_LX3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX3))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX4 := cdns,tensilica-xtensa-lx4

config DT_HAS_CDNS_TENSILICA_XTENSA_LX4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX4))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX6 := cdns,tensilica-xtensa-lx6

config DT_HAS_CDNS_TENSILICA_XTENSA_LX6_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX6))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX7 := cdns,tensilica-xtensa-lx7

config DT_HAS_CDNS_TENSILICA_XTENSA_LX7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX7))

DT_COMPAT_CDNS_UART := cdns,uart

config DT_HAS_CDNS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_UART))

DT_COMPAT_CDNS_XTENSA_CORE_INTC := cdns,xtensa-core-intc

config DT_HAS_CDNS_XTENSA_CORE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_XTENSA_CORE_INTC))

DT_COMPAT_CHIPSEMI_CHSC6X := chipsemi,chsc6x

config DT_HAS_CHIPSEMI_CHSC6X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CHIPSEMI_CHSC6X))

DT_COMPAT_CIRQUE_PINNACLE := cirque,pinnacle

config DT_HAS_CIRQUE_PINNACLE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CIRQUE_PINNACLE))

DT_COMPAT_CIRRUS_CP9314 := cirrus,cp9314

config DT_HAS_CIRRUS_CP9314_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CIRRUS_CP9314))

DT_COMPAT_CIRRUS_CS47L63 := cirrus,cs47l63

config DT_HAS_CIRRUS_CS47L63_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CIRRUS_CS47L63))

DT_COMPAT_CURRENT_SENSE_AMPLIFIER := current-sense-amplifier

config DT_HAS_CURRENT_SENSE_AMPLIFIER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CURRENT_SENSE_AMPLIFIER))

DT_COMPAT_CURRENT_SENSE_SHUNT := current-sense-shunt

config DT_HAS_CURRENT_SENSE_SHUNT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CURRENT_SENSE_SHUNT))

DT_COMPAT_CYPRESS_CY8C95XX_GPIO := cypress,cy8c95xx-gpio

config DT_HAS_CYPRESS_CY8C95XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_CY8C95XX_GPIO))

DT_COMPAT_CYPRESS_CY8C95XX_GPIO_PORT := cypress,cy8c95xx-gpio-port

config DT_HAS_CYPRESS_CY8C95XX_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_CY8C95XX_GPIO_PORT))

DT_COMPAT_CYPRESS_PSOC6_FLASH_CONTROLLER := cypress,psoc6-flash-controller

config DT_HAS_CYPRESS_PSOC6_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_FLASH_CONTROLLER))

DT_COMPAT_CYPRESS_PSOC6_GPIO := cypress,psoc6-gpio

config DT_HAS_CYPRESS_PSOC6_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_GPIO))

DT_COMPAT_CYPRESS_PSOC6_HSIOM := cypress,psoc6-hsiom

config DT_HAS_CYPRESS_PSOC6_HSIOM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_HSIOM))

DT_COMPAT_CYPRESS_PSOC6_INTMUX := cypress,psoc6-intmux

config DT_HAS_CYPRESS_PSOC6_INTMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_INTMUX))

DT_COMPAT_CYPRESS_PSOC6_INTMUX_CH := cypress,psoc6-intmux-ch

config DT_HAS_CYPRESS_PSOC6_INTMUX_CH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_INTMUX_CH))

DT_COMPAT_CYPRESS_PSOC6_SPI := cypress,psoc6-spi

config DT_HAS_CYPRESS_PSOC6_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_SPI))

DT_COMPAT_CYPRESS_PSOC6_UART := cypress,psoc6-uart

config DT_HAS_CYPRESS_PSOC6_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_UART))

DT_COMPAT_CYPRESS_PSOC6_UID := cypress,psoc6-uid

config DT_HAS_CYPRESS_PSOC6_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_UID))

DT_COMPAT_DAVICOM_DM8806_PHY := davicom,dm8806-phy

config DT_HAS_DAVICOM_DM8806_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DAVICOM_DM8806_PHY))

DT_COMPAT_DECAWAVE_DW1000 := decawave,dw1000

config DT_HAS_DECAWAVE_DW1000_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DECAWAVE_DW1000))

DT_COMPAT_DFROBOT_A01NYUB := dfrobot,a01nyub

config DT_HAS_DFROBOT_A01NYUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DFROBOT_A01NYUB))

DT_COMPAT_DIGILENT_PMOD := digilent,pmod

config DT_HAS_DIGILENT_PMOD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DIGILENT_PMOD))

DT_COMPAT_DIODES_PI3USB9201 := diodes,pi3usb9201

config DT_HAS_DIODES_PI3USB9201_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DIODES_PI3USB9201))

DT_COMPAT_EFINIX_SAPPHIRE_GPIO := efinix,sapphire-gpio

config DT_HAS_EFINIX_SAPPHIRE_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_GPIO))

DT_COMPAT_EFINIX_SAPPHIRE_TIMER0 := efinix,sapphire-timer0

config DT_HAS_EFINIX_SAPPHIRE_TIMER0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_TIMER0))

DT_COMPAT_EFINIX_SAPPHIRE_UART0 := efinix,sapphire-uart0

config DT_HAS_EFINIX_SAPPHIRE_UART0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_UART0))

DT_COMPAT_EFINIX_VEXRISCV_SAPPHIRE := efinix,vexriscv-sapphire

config DT_HAS_EFINIX_VEXRISCV_SAPPHIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_VEXRISCV_SAPPHIRE))

DT_COMPAT_ENE_KB1200_ADC := ene,kb1200-adc

config DT_HAS_ENE_KB1200_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_ADC))

DT_COMPAT_ENE_KB1200_GCFG := ene,kb1200-gcfg

config DT_HAS_ENE_KB1200_GCFG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_GCFG))

DT_COMPAT_ENE_KB1200_GPIO := ene,kb1200-gpio

config DT_HAS_ENE_KB1200_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_GPIO))

DT_COMPAT_ENE_KB1200_I2C := ene,kb1200-i2c

config DT_HAS_ENE_KB1200_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_I2C))

DT_COMPAT_ENE_KB1200_PINCTRL := ene,kb1200-pinctrl

config DT_HAS_ENE_KB1200_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_PINCTRL))

DT_COMPAT_ENE_KB1200_PMU := ene,kb1200-pmu

config DT_HAS_ENE_KB1200_PMU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_PMU))

DT_COMPAT_ENE_KB1200_PWM := ene,kb1200-pwm

config DT_HAS_ENE_KB1200_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_PWM))

DT_COMPAT_ENE_KB1200_TACH := ene,kb1200-tach

config DT_HAS_ENE_KB1200_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_TACH))

DT_COMPAT_ENE_KB1200_UART := ene,kb1200-uart

config DT_HAS_ENE_KB1200_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_UART))

DT_COMPAT_ENE_KB1200_WATCHDOG := ene,kb1200-watchdog

config DT_HAS_ENE_KB1200_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ENE_KB1200_WATCHDOG))

DT_COMPAT_EPCOS_B57861S0103A039 := epcos,b57861s0103a039

config DT_HAS_EPCOS_B57861S0103A039_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EPCOS_B57861S0103A039))

DT_COMPAT_ESPRESSIF_ESP_AT := espressif,esp-at

config DT_HAS_ESPRESSIF_ESP_AT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP_AT))

DT_COMPAT_ESPRESSIF_ESP32_ADC := espressif,esp32-adc

config DT_HAS_ESPRESSIF_ESP32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_ADC))

DT_COMPAT_ESPRESSIF_ESP32_BT_HCI := espressif,esp32-bt-hci

config DT_HAS_ESPRESSIF_ESP32_BT_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_BT_HCI))

DT_COMPAT_ESPRESSIF_ESP32_DAC := espressif,esp32-dac

config DT_HAS_ESPRESSIF_ESP32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_DAC))

DT_COMPAT_ESPRESSIF_ESP32_ETH := espressif,esp32-eth

config DT_HAS_ESPRESSIF_ESP32_ETH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_ETH))

DT_COMPAT_ESPRESSIF_ESP32_FLASH_CONTROLLER := espressif,esp32-flash-controller

config DT_HAS_ESPRESSIF_ESP32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_FLASH_CONTROLLER))

DT_COMPAT_ESPRESSIF_ESP32_GDMA := espressif,esp32-gdma

config DT_HAS_ESPRESSIF_ESP32_GDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_GDMA))

DT_COMPAT_ESPRESSIF_ESP32_GPIO := espressif,esp32-gpio

config DT_HAS_ESPRESSIF_ESP32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_GPIO))

DT_COMPAT_ESPRESSIF_ESP32_I2C := espressif,esp32-i2c

config DT_HAS_ESPRESSIF_ESP32_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_I2C))

DT_COMPAT_ESPRESSIF_ESP32_I2S := espressif,esp32-i2s

config DT_HAS_ESPRESSIF_ESP32_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_I2S))

DT_COMPAT_ESPRESSIF_ESP32_INTC := espressif,esp32-intc

config DT_HAS_ESPRESSIF_ESP32_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_INTC))

DT_COMPAT_ESPRESSIF_ESP32_IPM := espressif,esp32-ipm

config DT_HAS_ESPRESSIF_ESP32_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_IPM))

DT_COMPAT_ESPRESSIF_ESP32_LCD_CAM := espressif,esp32-lcd-cam

config DT_HAS_ESPRESSIF_ESP32_LCD_CAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_LCD_CAM))

DT_COMPAT_ESPRESSIF_ESP32_LEDC := espressif,esp32-ledc

config DT_HAS_ESPRESSIF_ESP32_LEDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_LEDC))

DT_COMPAT_ESPRESSIF_ESP32_MCPWM := espressif,esp32-mcpwm

config DT_HAS_ESPRESSIF_ESP32_MCPWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_MCPWM))

DT_COMPAT_ESPRESSIF_ESP32_MDIO := espressif,esp32-mdio

config DT_HAS_ESPRESSIF_ESP32_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_MDIO))

DT_COMPAT_ESPRESSIF_ESP32_PCNT := espressif,esp32-pcnt

config DT_HAS_ESPRESSIF_ESP32_PCNT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_PCNT))

DT_COMPAT_ESPRESSIF_ESP32_PINCTRL := espressif,esp32-pinctrl

config DT_HAS_ESPRESSIF_ESP32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_PINCTRL))

DT_COMPAT_ESPRESSIF_ESP32_RTC := espressif,esp32-rtc

config DT_HAS_ESPRESSIF_ESP32_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_RTC))

DT_COMPAT_ESPRESSIF_ESP32_RTC_TIMER := espressif,esp32-rtc-timer

config DT_HAS_ESPRESSIF_ESP32_RTC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_RTC_TIMER))

DT_COMPAT_ESPRESSIF_ESP32_SDHC := espressif,esp32-sdhc

config DT_HAS_ESPRESSIF_ESP32_SDHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SDHC))

DT_COMPAT_ESPRESSIF_ESP32_SDHC_SLOT := espressif,esp32-sdhc-slot

config DT_HAS_ESPRESSIF_ESP32_SDHC_SLOT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SDHC_SLOT))

DT_COMPAT_ESPRESSIF_ESP32_SPI := espressif,esp32-spi

config DT_HAS_ESPRESSIF_ESP32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SPI))

DT_COMPAT_ESPRESSIF_ESP32_SYSTIMER := espressif,esp32-systimer

config DT_HAS_ESPRESSIF_ESP32_SYSTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SYSTIMER))

DT_COMPAT_ESPRESSIF_ESP32_TEMP := espressif,esp32-temp

config DT_HAS_ESPRESSIF_ESP32_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TEMP))

DT_COMPAT_ESPRESSIF_ESP32_TIMER := espressif,esp32-timer

config DT_HAS_ESPRESSIF_ESP32_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TIMER))

DT_COMPAT_ESPRESSIF_ESP32_TOUCH := espressif,esp32-touch

config DT_HAS_ESPRESSIF_ESP32_TOUCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TOUCH))

DT_COMPAT_ESPRESSIF_ESP32_TRNG := espressif,esp32-trng

config DT_HAS_ESPRESSIF_ESP32_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TRNG))

DT_COMPAT_ESPRESSIF_ESP32_TWAI := espressif,esp32-twai

config DT_HAS_ESPRESSIF_ESP32_TWAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TWAI))

DT_COMPAT_ESPRESSIF_ESP32_UART := espressif,esp32-uart

config DT_HAS_ESPRESSIF_ESP32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_UART))

DT_COMPAT_ESPRESSIF_ESP32_USB_SERIAL := espressif,esp32-usb-serial

config DT_HAS_ESPRESSIF_ESP32_USB_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_USB_SERIAL))

DT_COMPAT_ESPRESSIF_ESP32_WATCHDOG := espressif,esp32-watchdog

config DT_HAS_ESPRESSIF_ESP32_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_WATCHDOG))

DT_COMPAT_ESPRESSIF_ESP32_WIFI := espressif,esp32-wifi

config DT_HAS_ESPRESSIF_ESP32_WIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_WIFI))

DT_COMPAT_ESPRESSIF_ESP32_XT_WDT := espressif,esp32-xt-wdt

config DT_HAS_ESPRESSIF_ESP32_XT_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_XT_WDT))

DT_COMPAT_ESPRESSIF_MBOX_ESP32 := espressif,mbox-esp32

config DT_HAS_ESPRESSIF_MBOX_ESP32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_MBOX_ESP32))

DT_COMPAT_ESPRESSIF_RISCV := espressif,riscv

config DT_HAS_ESPRESSIF_RISCV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_RISCV))

DT_COMPAT_ESPRESSIF_XTENSA_LX6 := espressif,xtensa-lx6

config DT_HAS_ESPRESSIF_XTENSA_LX6_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_XTENSA_LX6))

DT_COMPAT_ESPRESSIF_XTENSA_LX7 := espressif,xtensa-lx7

config DT_HAS_ESPRESSIF_XTENSA_LX7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_XTENSA_LX7))

DT_COMPAT_ETHERNET_PHY := ethernet-phy

config DT_HAS_ETHERNET_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ETHERNET_PHY))

DT_COMPAT_FCS_FXL6408 := fcs,fxl6408

config DT_HAS_FCS_FXL6408_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FCS_FXL6408))

DT_COMPAT_FESTO_VEAA_X_3 := festo,veaa-x-3

config DT_HAS_FESTO_VEAA_X_3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FESTO_VEAA_X_3))

DT_COMPAT_FINTEK_F75303 := fintek,f75303

config DT_HAS_FINTEK_F75303_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FINTEK_F75303))

DT_COMPAT_FIXED_CLOCK := fixed-clock

config DT_HAS_FIXED_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_CLOCK))

DT_COMPAT_FIXED_FACTOR_CLOCK := fixed-factor-clock

config DT_HAS_FIXED_FACTOR_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_FACTOR_CLOCK))

DT_COMPAT_FIXED_PARTITIONS := fixed-partitions

config DT_HAS_FIXED_PARTITIONS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_PARTITIONS))

DT_COMPAT_FOCALTECH_FT5336 := focaltech,ft5336

config DT_HAS_FOCALTECH_FT5336_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FOCALTECH_FT5336))

DT_COMPAT_FRIDA_NT35510 := frida,nt35510

config DT_HAS_FRIDA_NT35510_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FRIDA_NT35510))

DT_COMPAT_FSL_IMX21_I2C := fsl,imx21-i2c

config DT_HAS_FSL_IMX21_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FSL_IMX21_I2C))

DT_COMPAT_FSL_IMX27_PWM := fsl,imx27-pwm

config DT_HAS_FSL_IMX27_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FSL_IMX27_PWM))

DT_COMPAT_FTDI_FT800 := ftdi,ft800

config DT_HAS_FTDI_FT800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FTDI_FT800))

DT_COMPAT_FUJITSU_MB85RCXX := fujitsu,mb85rcxx

config DT_HAS_FUJITSU_MB85RCXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FUJITSU_MB85RCXX))

DT_COMPAT_FUJITSU_MB85RSXX := fujitsu,mb85rsxx

config DT_HAS_FUJITSU_MB85RSXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FUJITSU_MB85RSXX))

DT_COMPAT_FUTABA_SBUS := futaba,sbus

config DT_HAS_FUTABA_SBUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FUTABA_SBUS))

DT_COMPAT_GAISLER_APBUART := gaisler,apbuart

config DT_HAS_GAISLER_APBUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_APBUART))

DT_COMPAT_GAISLER_GPTIMER := gaisler,gptimer

config DT_HAS_GAISLER_GPTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_GPTIMER))

DT_COMPAT_GAISLER_GRGPIO := gaisler,grgpio

config DT_HAS_GAISLER_GRGPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_GRGPIO))

DT_COMPAT_GAISLER_IRQMP := gaisler,irqmp

config DT_HAS_GAISLER_IRQMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_IRQMP))

DT_COMPAT_GAISLER_LEON3 := gaisler,leon3

config DT_HAS_GAISLER_LEON3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_LEON3))

DT_COMPAT_GAISLER_SPIMCTRL := gaisler,spimctrl

config DT_HAS_GAISLER_SPIMCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_SPIMCTRL))

DT_COMPAT_GALAXYCORE_GC2145 := galaxycore,gc2145

config DT_HAS_GALAXYCORE_GC2145_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GALAXYCORE_GC2145))

DT_COMPAT_GALAXYCORE_GC9X01X := galaxycore,gc9x01x

config DT_HAS_GALAXYCORE_GC9X01X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GALAXYCORE_GC9X01X))

DT_COMPAT_GD_GD32_ADC := gd,gd32-adc

config DT_HAS_GD_GD32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_ADC))

DT_COMPAT_GD_GD32_AFIO := gd,gd32-afio

config DT_HAS_GD_GD32_AFIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_AFIO))

DT_COMPAT_GD_GD32_CCTL := gd,gd32-cctl

config DT_HAS_GD_GD32_CCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_CCTL))

DT_COMPAT_GD_GD32_DAC := gd,gd32-dac

config DT_HAS_GD_GD32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DAC))

DT_COMPAT_GD_GD32_DMA := gd,gd32-dma

config DT_HAS_GD_GD32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DMA))

DT_COMPAT_GD_GD32_DMA_V1 := gd,gd32-dma-v1

config DT_HAS_GD_GD32_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DMA_V1))

DT_COMPAT_GD_GD32_EXTI := gd,gd32-exti

config DT_HAS_GD_GD32_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_EXTI))

DT_COMPAT_GD_GD32_FLASH_CONTROLLER := gd,gd32-flash-controller

config DT_HAS_GD_GD32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_FLASH_CONTROLLER))

DT_COMPAT_GD_GD32_FWDGT := gd,gd32-fwdgt

config DT_HAS_GD_GD32_FWDGT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_FWDGT))

DT_COMPAT_GD_GD32_GPIO := gd,gd32-gpio

config DT_HAS_GD_GD32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_GPIO))

DT_COMPAT_GD_GD32_I2C := gd,gd32-i2c

config DT_HAS_GD_GD32_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_I2C))

DT_COMPAT_GD_GD32_NV_FLASH_V1 := gd,gd32-nv-flash-v1

config DT_HAS_GD_GD32_NV_FLASH_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V1))

DT_COMPAT_GD_GD32_NV_FLASH_V2 := gd,gd32-nv-flash-v2

config DT_HAS_GD_GD32_NV_FLASH_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V2))

DT_COMPAT_GD_GD32_NV_FLASH_V3 := gd,gd32-nv-flash-v3

config DT_HAS_GD_GD32_NV_FLASH_V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V3))

DT_COMPAT_GD_GD32_PINCTRL_AF := gd,gd32-pinctrl-af

config DT_HAS_GD_GD32_PINCTRL_AF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PINCTRL_AF))

DT_COMPAT_GD_GD32_PINCTRL_AFIO := gd,gd32-pinctrl-afio

config DT_HAS_GD_GD32_PINCTRL_AFIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PINCTRL_AFIO))

DT_COMPAT_GD_GD32_PWM := gd,gd32-pwm

config DT_HAS_GD_GD32_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PWM))

DT_COMPAT_GD_GD32_RCTL := gd,gd32-rctl

config DT_HAS_GD_GD32_RCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_RCTL))

DT_COMPAT_GD_GD32_RCU := gd,gd32-rcu

config DT_HAS_GD_GD32_RCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_RCU))

DT_COMPAT_GD_GD32_SPI := gd,gd32-spi

config DT_HAS_GD_GD32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_SPI))

DT_COMPAT_GD_GD32_SYSCFG := gd,gd32-syscfg

config DT_HAS_GD_GD32_SYSCFG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_SYSCFG))

DT_COMPAT_GD_GD32_TIMER := gd,gd32-timer

config DT_HAS_GD_GD32_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_TIMER))

DT_COMPAT_GD_GD32_USART := gd,gd32-usart

config DT_HAS_GD_GD32_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_USART))

DT_COMPAT_GD_GD32_WWDGT := gd,gd32-wwdgt

config DT_HAS_GD_GD32_WWDGT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_WWDGT))

DT_COMPAT_GENERIC_FEM_TWO_CTRL_PINS := generic-fem-two-ctrl-pins

config DT_HAS_GENERIC_FEM_TWO_CTRL_PINS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_FEM_TWO_CTRL_PINS))

DT_COMPAT_GENERIC_RADIO_COEX_ONE_WIRE := generic-radio-coex-one-wire

config DT_HAS_GENERIC_RADIO_COEX_ONE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_RADIO_COEX_ONE_WIRE))

DT_COMPAT_GENERIC_RADIO_COEX_THREE_WIRE := generic-radio-coex-three-wire

config DT_HAS_GENERIC_RADIO_COEX_THREE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_RADIO_COEX_THREE_WIRE))

DT_COMPAT_GNSS_NMEA_GENERIC := gnss-nmea-generic

config DT_HAS_GNSS_NMEA_GENERIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GNSS_NMEA_GENERIC))

DT_COMPAT_GOODIX_GT911 := goodix,gt911

config DT_HAS_GOODIX_GT911_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GOODIX_GT911))

DT_COMPAT_GPIO_I2C := gpio-i2c

config DT_HAS_GPIO_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_I2C))

DT_COMPAT_GPIO_I2C_SWITCH := gpio-i2c-switch

config DT_HAS_GPIO_I2C_SWITCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_I2C_SWITCH))

DT_COMPAT_GPIO_KBD_MATRIX := gpio-kbd-matrix

config DT_HAS_GPIO_KBD_MATRIX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_KBD_MATRIX))

DT_COMPAT_GPIO_KEYS := gpio-keys

config DT_HAS_GPIO_KEYS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_KEYS))

DT_COMPAT_GPIO_LEDS := gpio-leds

config DT_HAS_GPIO_LEDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_LEDS))

DT_COMPAT_GPIO_QDEC := gpio-qdec

config DT_HAS_GPIO_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_QDEC))

DT_COMPAT_GPIO_RADIO_COEX := gpio-radio-coex

config DT_HAS_GPIO_RADIO_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_RADIO_COEX))

DT_COMPAT_GREELED_LPD8803 := greeled,lpd8803

config DT_HAS_GREELED_LPD8803_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GREELED_LPD8803))

DT_COMPAT_GREELED_LPD8806 := greeled,lpd8806

config DT_HAS_GREELED_LPD8806_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GREELED_LPD8806))

DT_COMPAT_GROVE_HEADER := grove-header

config DT_HAS_GROVE_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GROVE_HEADER))

DT_COMPAT_GSS_EXPLORIR_M := gss,explorir-m

config DT_HAS_GSS_EXPLORIR_M_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GSS_EXPLORIR_M))

DT_COMPAT_HAMAMATSU_S11059 := hamamatsu,s11059

config DT_HAS_HAMAMATSU_S11059_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HAMAMATSU_S11059))

DT_COMPAT_HC_SR04 := hc-sr04

config DT_HAS_HC_SR04_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HC_SR04))

DT_COMPAT_HIMAX_HX8394 := himax,hx8394

config DT_HAS_HIMAX_HX8394_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HIMAX_HX8394))

DT_COMPAT_HIT_HD44780 := hit,hd44780

config DT_HAS_HIT_HD44780_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HIT_HD44780))

DT_COMPAT_HOLTEK_HT16K33 := holtek,ht16k33

config DT_HAS_HOLTEK_HT16K33_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOLTEK_HT16K33))

DT_COMPAT_HONEYWELL_HMC5883L := honeywell,hmc5883l

config DT_HAS_HONEYWELL_HMC5883L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_HMC5883L))

DT_COMPAT_HONEYWELL_MPR := honeywell,mpr

config DT_HAS_HONEYWELL_MPR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_MPR))

DT_COMPAT_HONEYWELL_SM351LT := honeywell,sm351lt

config DT_HAS_HONEYWELL_SM351LT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_SM351LT))

DT_COMPAT_HOPERF_HP206C := hoperf,hp206c

config DT_HAS_HOPERF_HP206C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOPERF_HP206C))

DT_COMPAT_HOPERF_TH02 := hoperf,th02

config DT_HAS_HOPERF_TH02_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOPERF_TH02))

DT_COMPAT_HYNITRON_CST816S := hynitron,cst816s

config DT_HAS_HYNITRON_CST816S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HYNITRON_CST816S))

DT_COMPAT_HZGROW_R502A := hzgrow,r502a

config DT_HAS_HZGROW_R502A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HZGROW_R502A))

DT_COMPAT_ILITEK_ILI2132A := ilitek,ili2132a

config DT_HAS_ILITEK_ILI2132A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI2132A))

DT_COMPAT_ILITEK_ILI9340 := ilitek,ili9340

config DT_HAS_ILITEK_ILI9340_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9340))

DT_COMPAT_ILITEK_ILI9341 := ilitek,ili9341

config DT_HAS_ILITEK_ILI9341_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9341))

DT_COMPAT_ILITEK_ILI9342C := ilitek,ili9342c

config DT_HAS_ILITEK_ILI9342C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9342C))

DT_COMPAT_ILITEK_ILI9488 := ilitek,ili9488

config DT_HAS_ILITEK_ILI9488_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9488))

DT_COMPAT_ILITEK_ILI9806E_DSI := ilitek,ili9806e-dsi

config DT_HAS_ILITEK_ILI9806E_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9806E_DSI))

DT_COMPAT_INFINEON_AIROC_WIFI := infineon,airoc-wifi

config DT_HAS_INFINEON_AIROC_WIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_AIROC_WIFI))

DT_COMPAT_INFINEON_CAT1_ADC := infineon,cat1-adc

config DT_HAS_INFINEON_CAT1_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_ADC))

DT_COMPAT_INFINEON_CAT1_BLESS_HCI := infineon,cat1-bless-hci

config DT_HAS_INFINEON_CAT1_BLESS_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_BLESS_HCI))

DT_COMPAT_INFINEON_CAT1_COUNTER := infineon,cat1-counter

config DT_HAS_INFINEON_CAT1_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_COUNTER))

DT_COMPAT_INFINEON_CAT1_FLASH_CONTROLLER := infineon,cat1-flash-controller

config DT_HAS_INFINEON_CAT1_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_FLASH_CONTROLLER))

DT_COMPAT_INFINEON_CAT1_GPIO := infineon,cat1-gpio

config DT_HAS_INFINEON_CAT1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_GPIO))

DT_COMPAT_INFINEON_CAT1_I2C := infineon,cat1-i2c

config DT_HAS_INFINEON_CAT1_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_I2C))

DT_COMPAT_INFINEON_CAT1_PINCTRL := infineon,cat1-pinctrl

config DT_HAS_INFINEON_CAT1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_PINCTRL))

DT_COMPAT_INFINEON_CAT1_PWM := infineon,cat1-pwm

config DT_HAS_INFINEON_CAT1_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_PWM))

DT_COMPAT_INFINEON_CAT1_QSPI_FLASH := infineon,cat1-qspi-flash

config DT_HAS_INFINEON_CAT1_QSPI_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_QSPI_FLASH))

DT_COMPAT_INFINEON_CAT1_RTC := infineon,cat1-rtc

config DT_HAS_INFINEON_CAT1_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_RTC))

DT_COMPAT_INFINEON_CAT1_SCB := infineon,cat1-scb

config DT_HAS_INFINEON_CAT1_SCB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_SCB))

DT_COMPAT_INFINEON_CAT1_SDHC_SDIO := infineon,cat1-sdhc-sdio

config DT_HAS_INFINEON_CAT1_SDHC_SDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_SDHC_SDIO))

DT_COMPAT_INFINEON_CAT1_SPI := infineon,cat1-spi

config DT_HAS_INFINEON_CAT1_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_SPI))

DT_COMPAT_INFINEON_CAT1_UART := infineon,cat1-uart

config DT_HAS_INFINEON_CAT1_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_UART))

DT_COMPAT_INFINEON_CAT1_WATCHDOG := infineon,cat1-watchdog

config DT_HAS_INFINEON_CAT1_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_WATCHDOG))

DT_COMPAT_INFINEON_CYW208XX_HCI := infineon,cyw208xx-hci

config DT_HAS_INFINEON_CYW208XX_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CYW208XX_HCI))

DT_COMPAT_INFINEON_CYW43XXX_BT_HCI := infineon,cyw43xxx-bt-hci

config DT_HAS_INFINEON_CYW43XXX_BT_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CYW43XXX_BT_HCI))

DT_COMPAT_INFINEON_DPS310 := infineon,dps310

config DT_HAS_INFINEON_DPS310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_DPS310))

DT_COMPAT_INFINEON_TLE9104 := infineon,tle9104

config DT_HAS_INFINEON_TLE9104_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_TLE9104))

DT_COMPAT_INFINEON_TLE9104_DIAGNOSTICS := infineon,tle9104-diagnostics

config DT_HAS_INFINEON_TLE9104_DIAGNOSTICS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_TLE9104_DIAGNOSTICS))

DT_COMPAT_INFINEON_TLE9104_GPIO := infineon,tle9104-gpio

config DT_HAS_INFINEON_TLE9104_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_TLE9104_GPIO))

DT_COMPAT_INFINEON_XMC4XXX_ADC := infineon,xmc4xxx-adc

config DT_HAS_INFINEON_XMC4XXX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_ADC))

DT_COMPAT_INFINEON_XMC4XXX_CAN := infineon,xmc4xxx-can

config DT_HAS_INFINEON_XMC4XXX_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CAN))

DT_COMPAT_INFINEON_XMC4XXX_CAN_NODE := infineon,xmc4xxx-can-node

config DT_HAS_INFINEON_XMC4XXX_CAN_NODE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CAN_NODE))

DT_COMPAT_INFINEON_XMC4XXX_CCU4_PWM := infineon,xmc4xxx-ccu4-pwm

config DT_HAS_INFINEON_XMC4XXX_CCU4_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CCU4_PWM))

DT_COMPAT_INFINEON_XMC4XXX_CCU8_PWM := infineon,xmc4xxx-ccu8-pwm

config DT_HAS_INFINEON_XMC4XXX_CCU8_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CCU8_PWM))

DT_COMPAT_INFINEON_XMC4XXX_DMA := infineon,xmc4xxx-dma

config DT_HAS_INFINEON_XMC4XXX_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_DMA))

DT_COMPAT_INFINEON_XMC4XXX_ETHERNET := infineon,xmc4xxx-ethernet

config DT_HAS_INFINEON_XMC4XXX_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_ETHERNET))

DT_COMPAT_INFINEON_XMC4XXX_FLASH_CONTROLLER := infineon,xmc4xxx-flash-controller

config DT_HAS_INFINEON_XMC4XXX_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_FLASH_CONTROLLER))

DT_COMPAT_INFINEON_XMC4XXX_GPIO := infineon,xmc4xxx-gpio

config DT_HAS_INFINEON_XMC4XXX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_GPIO))

DT_COMPAT_INFINEON_XMC4XXX_I2C := infineon,xmc4xxx-i2c

config DT_HAS_INFINEON_XMC4XXX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_I2C))

DT_COMPAT_INFINEON_XMC4XXX_INTC := infineon,xmc4xxx-intc

config DT_HAS_INFINEON_XMC4XXX_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_INTC))

DT_COMPAT_INFINEON_XMC4XXX_MDIO := infineon,xmc4xxx-mdio

config DT_HAS_INFINEON_XMC4XXX_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_MDIO))

DT_COMPAT_INFINEON_XMC4XXX_NV_FLASH := infineon,xmc4xxx-nv-flash

config DT_HAS_INFINEON_XMC4XXX_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_NV_FLASH))

DT_COMPAT_INFINEON_XMC4XXX_PINCTRL := infineon,xmc4xxx-pinctrl

config DT_HAS_INFINEON_XMC4XXX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_PINCTRL))

DT_COMPAT_INFINEON_XMC4XXX_RTC := infineon,xmc4xxx-rtc

config DT_HAS_INFINEON_XMC4XXX_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_RTC))

DT_COMPAT_INFINEON_XMC4XXX_SPI := infineon,xmc4xxx-spi

config DT_HAS_INFINEON_XMC4XXX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_SPI))

DT_COMPAT_INFINEON_XMC4XXX_TEMP := infineon,xmc4xxx-temp

config DT_HAS_INFINEON_XMC4XXX_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_TEMP))

DT_COMPAT_INFINEON_XMC4XXX_UART := infineon,xmc4xxx-uart

config DT_HAS_INFINEON_XMC4XXX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_UART))

DT_COMPAT_INFINEON_XMC4XXX_WATCHDOG := infineon,xmc4xxx-watchdog

config DT_HAS_INFINEON_XMC4XXX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_WATCHDOG))

DT_COMPAT_INPUT_KEYMAP := input-keymap

config DT_HAS_INPUT_KEYMAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INPUT_KEYMAP))

DT_COMPAT_INTEL_ACE_ART_COUNTER := intel,ace-art-counter

config DT_HAS_INTEL_ACE_ART_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_ART_COUNTER))

DT_COMPAT_INTEL_ACE_INTC := intel,ace-intc

config DT_HAS_INTEL_ACE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_INTC))

DT_COMPAT_INTEL_ACE_RTC_COUNTER := intel,ace-rtc-counter

config DT_HAS_INTEL_ACE_RTC_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_RTC_COUNTER))

DT_COMPAT_INTEL_ACE_TIMESTAMP := intel,ace-timestamp

config DT_HAS_INTEL_ACE_TIMESTAMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_TIMESTAMP))

DT_COMPAT_INTEL_ADSP_COMMUNICATION_WIDGET := intel,adsp-communication-widget

config DT_HAS_INTEL_ADSP_COMMUNICATION_WIDGET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_COMMUNICATION_WIDGET))

DT_COMPAT_INTEL_ADSP_DFPMCCH := intel,adsp-dfpmcch

config DT_HAS_INTEL_ADSP_DFPMCCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DFPMCCH))

DT_COMPAT_INTEL_ADSP_DFPMCCU := intel,adsp-dfpmccu

config DT_HAS_INTEL_ADSP_DFPMCCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DFPMCCU))

DT_COMPAT_INTEL_ADSP_DMIC_VSS := intel,adsp-dmic-vss

config DT_HAS_INTEL_ADSP_DMIC_VSS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DMIC_VSS))

DT_COMPAT_INTEL_ADSP_GPDMA := intel,adsp-gpdma

config DT_HAS_INTEL_ADSP_GPDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_GPDMA))

DT_COMPAT_INTEL_ADSP_HDA_DMIC_CAP := intel,adsp-hda-dmic-cap

config DT_HAS_INTEL_ADSP_HDA_DMIC_CAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_DMIC_CAP))

DT_COMPAT_INTEL_ADSP_HDA_HOST_IN := intel,adsp-hda-host-in

config DT_HAS_INTEL_ADSP_HDA_HOST_IN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_HOST_IN))

DT_COMPAT_INTEL_ADSP_HDA_HOST_OUT := intel,adsp-hda-host-out

config DT_HAS_INTEL_ADSP_HDA_HOST_OUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_HOST_OUT))

DT_COMPAT_INTEL_ADSP_HDA_LINK_IN := intel,adsp-hda-link-in

config DT_HAS_INTEL_ADSP_HDA_LINK_IN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_LINK_IN))

DT_COMPAT_INTEL_ADSP_HDA_LINK_OUT := intel,adsp-hda-link-out

config DT_HAS_INTEL_ADSP_HDA_LINK_OUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_LINK_OUT))

DT_COMPAT_INTEL_ADSP_HDA_SSP_CAP := intel,adsp-hda-ssp-cap

config DT_HAS_INTEL_ADSP_HDA_SSP_CAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_SSP_CAP))

DT_COMPAT_INTEL_ADSP_HOST_IPC := intel,adsp-host-ipc

config DT_HAS_INTEL_ADSP_HOST_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HOST_IPC))

DT_COMPAT_INTEL_ADSP_IDC := intel,adsp-idc

config DT_HAS_INTEL_ADSP_IDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_IDC))

DT_COMPAT_INTEL_ADSP_IMR := intel,adsp-imr

config DT_HAS_INTEL_ADSP_IMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_IMR))

DT_COMPAT_INTEL_ADSP_MAILBOX := intel,adsp-mailbox

config DT_HAS_INTEL_ADSP_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MAILBOX))

DT_COMPAT_INTEL_ADSP_MEM_WINDOW := intel,adsp-mem-window

config DT_HAS_INTEL_ADSP_MEM_WINDOW_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MEM_WINDOW))

DT_COMPAT_INTEL_ADSP_MTL_TLB := intel,adsp-mtl-tlb

config DT_HAS_INTEL_ADSP_MTL_TLB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MTL_TLB))

DT_COMPAT_INTEL_ADSP_POWER_DOMAIN := intel,adsp-power-domain

config DT_HAS_INTEL_ADSP_POWER_DOMAIN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_POWER_DOMAIN))

DT_COMPAT_INTEL_ADSP_SHA := intel,adsp-sha

config DT_HAS_INTEL_ADSP_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_SHA))

DT_COMPAT_INTEL_ADSP_SHIM_CLKCTL := intel,adsp-shim-clkctl

config DT_HAS_INTEL_ADSP_SHIM_CLKCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_SHIM_CLKCTL))

DT_COMPAT_INTEL_ADSP_TIMER := intel,adsp-timer

config DT_HAS_INTEL_ADSP_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_TIMER))

DT_COMPAT_INTEL_ADSP_TLB := intel,adsp-tlb

config DT_HAS_INTEL_ADSP_TLB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_TLB))

DT_COMPAT_INTEL_ADSP_WATCHDOG := intel,adsp-watchdog

config DT_HAS_INTEL_ADSP_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_WATCHDOG))

DT_COMPAT_INTEL_AGILEX_CLOCK := intel,agilex-clock

config DT_HAS_INTEL_AGILEX_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_AGILEX_CLOCK))

DT_COMPAT_INTEL_AGILEX5_CLOCK := intel,agilex5-clock

config DT_HAS_INTEL_AGILEX5_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_AGILEX5_CLOCK))

DT_COMPAT_INTEL_ALDER_LAKE := intel,alder-lake

config DT_HAS_INTEL_ALDER_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ALDER_LAKE))

DT_COMPAT_INTEL_ALH_DAI := intel,alh-dai

config DT_HAS_INTEL_ALH_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ALH_DAI))

DT_COMPAT_INTEL_APOLLO_LAKE := intel,apollo-lake

config DT_HAS_INTEL_APOLLO_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_APOLLO_LAKE))

DT_COMPAT_INTEL_BLINKY_PWM := intel,blinky-pwm

config DT_HAS_INTEL_BLINKY_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_BLINKY_PWM))

DT_COMPAT_INTEL_CAVS_I2S := intel,cavs-i2s

config DT_HAS_INTEL_CAVS_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_CAVS_I2S))

DT_COMPAT_INTEL_CAVS_INTC := intel,cavs-intc

config DT_HAS_INTEL_CAVS_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_CAVS_INTC))

DT_COMPAT_INTEL_DAI_DMIC := intel,dai-dmic

config DT_HAS_INTEL_DAI_DMIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_DAI_DMIC))

DT_COMPAT_INTEL_E1000 := intel,e1000

config DT_HAS_INTEL_E1000_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_E1000))

DT_COMPAT_INTEL_ELKHART_LAKE := intel,elkhart-lake

config DT_HAS_INTEL_ELKHART_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ELKHART_LAKE))

DT_COMPAT_INTEL_EMMC_HOST := intel,emmc-host

config DT_HAS_INTEL_EMMC_HOST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_EMMC_HOST))

DT_COMPAT_INTEL_GPIO := intel,gpio

config DT_HAS_INTEL_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_GPIO))

DT_COMPAT_INTEL_HDA_DAI := intel,hda-dai

config DT_HAS_INTEL_HDA_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_HDA_DAI))

DT_COMPAT_INTEL_HPET := intel,hpet

config DT_HAS_INTEL_HPET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_HPET))

DT_COMPAT_INTEL_IBECC := intel,ibecc

config DT_HAS_INTEL_IBECC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_IBECC))

DT_COMPAT_INTEL_IOAPIC := intel,ioapic

config DT_HAS_INTEL_IOAPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_IOAPIC))

DT_COMPAT_INTEL_ISH := intel,ish

config DT_HAS_INTEL_ISH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ISH))

DT_COMPAT_INTEL_LAKEMONT := intel,lakemont

config DT_HAS_INTEL_LAKEMONT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LAKEMONT))

DT_COMPAT_INTEL_LOAPIC := intel,loapic

config DT_HAS_INTEL_LOAPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LOAPIC))

DT_COMPAT_INTEL_LPSS := intel,lpss

config DT_HAS_INTEL_LPSS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LPSS))

DT_COMPAT_INTEL_LW_UART := intel,lw-uart

config DT_HAS_INTEL_LW_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LW_UART))

DT_COMPAT_INTEL_MULTIBOOT_FRAMEBUFFER := intel,multiboot-framebuffer

config DT_HAS_INTEL_MULTIBOOT_FRAMEBUFFER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_MULTIBOOT_FRAMEBUFFER))

DT_COMPAT_INTEL_NIOSV := intel,niosv

config DT_HAS_INTEL_NIOSV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_NIOSV))

DT_COMPAT_INTEL_PCH_SMBUS := intel,pch-smbus

config DT_HAS_INTEL_PCH_SMBUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_PCH_SMBUS))

DT_COMPAT_INTEL_PENWELL_SPI := intel,penwell-spi

config DT_HAS_INTEL_PENWELL_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_PENWELL_SPI))

DT_COMPAT_INTEL_RAPTOR_LAKE := intel,raptor-lake

config DT_HAS_INTEL_RAPTOR_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_RAPTOR_LAKE))

DT_COMPAT_INTEL_SEDI_DMA := intel,sedi-dma

config DT_HAS_INTEL_SEDI_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_DMA))

DT_COMPAT_INTEL_SEDI_GPIO := intel,sedi-gpio

config DT_HAS_INTEL_SEDI_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_GPIO))

DT_COMPAT_INTEL_SEDI_I2C := intel,sedi-i2c

config DT_HAS_INTEL_SEDI_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_I2C))

DT_COMPAT_INTEL_SEDI_IPM := intel,sedi-ipm

config DT_HAS_INTEL_SEDI_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_IPM))

DT_COMPAT_INTEL_SEDI_SPI := intel,sedi-spi

config DT_HAS_INTEL_SEDI_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_SPI))

DT_COMPAT_INTEL_SEDI_UART := intel,sedi-uart

config DT_HAS_INTEL_SEDI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_UART))

DT_COMPAT_INTEL_SOCFPGA_AGILEX_SIP_SMC := intel,socfpga-agilex-sip-smc

config DT_HAS_INTEL_SOCFPGA_AGILEX_SIP_SMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SOCFPGA_AGILEX_SIP_SMC))

DT_COMPAT_INTEL_SOCFPGA_RESET := intel,socfpga-reset

config DT_HAS_INTEL_SOCFPGA_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SOCFPGA_RESET))

DT_COMPAT_INTEL_SSP := intel,ssp

config DT_HAS_INTEL_SSP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SSP))

DT_COMPAT_INTEL_SSP_DAI := intel,ssp-dai

config DT_HAS_INTEL_SSP_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SSP_DAI))

DT_COMPAT_INTEL_SSP_SSPBASE := intel,ssp-sspbase

config DT_HAS_INTEL_SSP_SSPBASE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SSP_SSPBASE))

DT_COMPAT_INTEL_TCO_WDT := intel,tco-wdt

config DT_HAS_INTEL_TCO_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_TCO_WDT))

DT_COMPAT_INTEL_TIMEAWARE_GPIO := intel,timeaware-gpio

config DT_HAS_INTEL_TIMEAWARE_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_TIMEAWARE_GPIO))

DT_COMPAT_INTEL_VT_D := intel,vt-d

config DT_HAS_INTEL_VT_D_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_VT_D))

DT_COMPAT_INTEL_X86 := intel,x86

config DT_HAS_INTEL_X86_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_X86))

DT_COMPAT_INVENSENSE_ICM42605 := invensense,icm42605

config DT_HAS_INVENSENSE_ICM42605_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42605))

DT_COMPAT_INVENSENSE_ICM42670P := invensense,icm42670p

config DT_HAS_INVENSENSE_ICM42670P_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42670P))

DT_COMPAT_INVENSENSE_ICM42670S := invensense,icm42670s

config DT_HAS_INVENSENSE_ICM42670S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42670S))

DT_COMPAT_INVENSENSE_ICM42688 := invensense,icm42688

config DT_HAS_INVENSENSE_ICM42688_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42688))

DT_COMPAT_INVENSENSE_ICP10125 := invensense,icp10125

config DT_HAS_INVENSENSE_ICP10125_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICP10125))

DT_COMPAT_INVENSENSE_MPU6050 := invensense,mpu6050

config DT_HAS_INVENSENSE_MPU6050_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_MPU6050))

DT_COMPAT_INVENSENSE_MPU9250 := invensense,mpu9250

config DT_HAS_INVENSENSE_MPU9250_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_MPU9250))

DT_COMPAT_INVENTEK_ESWIFI := inventek,eswifi

config DT_HAS_INVENTEK_ESWIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENTEK_ESWIFI))

DT_COMPAT_INVENTEK_ESWIFI_UART := inventek,eswifi-uart

config DT_HAS_INVENTEK_ESWIFI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENTEK_ESWIFI_UART))

DT_COMPAT_ISENTEK_IST8310 := isentek,ist8310

config DT_HAS_ISENTEK_IST8310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISENTEK_IST8310))

DT_COMPAT_ISIL_ISL29035 := isil,isl29035

config DT_HAS_ISIL_ISL29035_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISIL_ISL29035))

DT_COMPAT_ISSI_IS31FL3194 := issi,is31fl3194

config DT_HAS_ISSI_IS31FL3194_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISSI_IS31FL3194))

DT_COMPAT_ISSI_IS31FL3216A := issi,is31fl3216a

config DT_HAS_ISSI_IS31FL3216A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISSI_IS31FL3216A))

DT_COMPAT_ISSI_IS31FL3733 := issi,is31fl3733

config DT_HAS_ISSI_IS31FL3733_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISSI_IS31FL3733))

DT_COMPAT_IST_TSIC_XX6 := ist,tsic-xx6

config DT_HAS_IST_TSIC_XX6_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_IST_TSIC_XX6))

DT_COMPAT_ISTECH_IST3931 := istech,ist3931

config DT_HAS_ISTECH_IST3931_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISTECH_IST3931))

DT_COMPAT_ITE_ENHANCE_I2C := ite,enhance-i2c

config DT_HAS_ITE_ENHANCE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_ENHANCE_I2C))

DT_COMPAT_ITE_IT82XX2_USB := ite,it82xx2-usb

config DT_HAS_ITE_IT82XX2_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT82XX2_USB))

DT_COMPAT_ITE_IT8801_ALTCTRL := ite,it8801-altctrl

config DT_HAS_ITE_IT8801_ALTCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_ALTCTRL))

DT_COMPAT_ITE_IT8801_GPIO := ite,it8801-gpio

config DT_HAS_ITE_IT8801_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_GPIO))

DT_COMPAT_ITE_IT8801_KBD := ite,it8801-kbd

config DT_HAS_ITE_IT8801_KBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_KBD))

DT_COMPAT_ITE_IT8801_MFD := ite,it8801-mfd

config DT_HAS_ITE_IT8801_MFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_MFD))

DT_COMPAT_ITE_IT8801_MFD_MAP := ite,it8801-mfd-map

config DT_HAS_ITE_IT8801_MFD_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_MFD_MAP))

DT_COMPAT_ITE_IT8801_PWM := ite,it8801-pwm

config DT_HAS_ITE_IT8801_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8801_PWM))

DT_COMPAT_ITE_IT8XXX2_ADC := ite,it8xxx2-adc

config DT_HAS_ITE_IT8XXX2_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ADC))

DT_COMPAT_ITE_IT8XXX2_BBRAM := ite,it8xxx2-bbram

config DT_HAS_ITE_IT8XXX2_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_BBRAM))

DT_COMPAT_ITE_IT8XXX2_ESPI := ite,it8xxx2-espi

config DT_HAS_ITE_IT8XXX2_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ESPI))

DT_COMPAT_ITE_IT8XXX2_FLASH_CONTROLLER := ite,it8xxx2-flash-controller

config DT_HAS_ITE_IT8XXX2_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_FLASH_CONTROLLER))

DT_COMPAT_ITE_IT8XXX2_GPIO := ite,it8xxx2-gpio

config DT_HAS_ITE_IT8XXX2_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIO))

DT_COMPAT_ITE_IT8XXX2_GPIO_V2 := ite,it8xxx2-gpio-v2

config DT_HAS_ITE_IT8XXX2_GPIO_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIO_V2))

DT_COMPAT_ITE_IT8XXX2_GPIOKSCAN := ite,it8xxx2-gpiokscan

config DT_HAS_ITE_IT8XXX2_GPIOKSCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIOKSCAN))

DT_COMPAT_ITE_IT8XXX2_I2C := ite,it8xxx2-i2c

config DT_HAS_ITE_IT8XXX2_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_I2C))

DT_COMPAT_ITE_IT8XXX2_ILM := ite,it8xxx2-ilm

config DT_HAS_ITE_IT8XXX2_ILM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ILM))

DT_COMPAT_ITE_IT8XXX2_INTC := ite,it8xxx2-intc

config DT_HAS_ITE_IT8XXX2_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_INTC))

DT_COMPAT_ITE_IT8XXX2_INTC_V2 := ite,it8xxx2-intc-v2

config DT_HAS_ITE_IT8XXX2_INTC_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_INTC_V2))

DT_COMPAT_ITE_IT8XXX2_KBD := ite,it8xxx2-kbd

config DT_HAS_ITE_IT8XXX2_KBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_KBD))

DT_COMPAT_ITE_IT8XXX2_PECI := ite,it8xxx2-peci

config DT_HAS_ITE_IT8XXX2_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PECI))

DT_COMPAT_ITE_IT8XXX2_PINCTRL := ite,it8xxx2-pinctrl

config DT_HAS_ITE_IT8XXX2_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PINCTRL))

DT_COMPAT_ITE_IT8XXX2_PINCTRL_FUNC := ite,it8xxx2-pinctrl-func

config DT_HAS_ITE_IT8XXX2_PINCTRL_FUNC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PINCTRL_FUNC))

DT_COMPAT_ITE_IT8XXX2_PWM := ite,it8xxx2-pwm

config DT_HAS_ITE_IT8XXX2_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PWM))

DT_COMPAT_ITE_IT8XXX2_PWMPRS := ite,it8xxx2-pwmprs

config DT_HAS_ITE_IT8XXX2_PWMPRS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PWMPRS))

DT_COMPAT_ITE_IT8XXX2_SHA := ite,it8xxx2-sha

config DT_HAS_ITE_IT8XXX2_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SHA))

DT_COMPAT_ITE_IT8XXX2_SHA_V2 := ite,it8xxx2-sha-v2

config DT_HAS_ITE_IT8XXX2_SHA_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SHA_V2))

DT_COMPAT_ITE_IT8XXX2_SHI := ite,it8xxx2-shi

config DT_HAS_ITE_IT8XXX2_SHI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SHI))

DT_COMPAT_ITE_IT8XXX2_SPI := ite,it8xxx2-spi

config DT_HAS_ITE_IT8XXX2_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SPI))

DT_COMPAT_ITE_IT8XXX2_TACH := ite,it8xxx2-tach

config DT_HAS_ITE_IT8XXX2_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_TACH))

DT_COMPAT_ITE_IT8XXX2_TIMER := ite,it8xxx2-timer

config DT_HAS_ITE_IT8XXX2_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_TIMER))

DT_COMPAT_ITE_IT8XXX2_UART := ite,it8xxx2-uart

config DT_HAS_ITE_IT8XXX2_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_UART))

DT_COMPAT_ITE_IT8XXX2_USBPD := ite,it8xxx2-usbpd

config DT_HAS_ITE_IT8XXX2_USBPD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_USBPD))

DT_COMPAT_ITE_IT8XXX2_VCMP := ite,it8xxx2-vcmp

config DT_HAS_ITE_IT8XXX2_VCMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_VCMP))

DT_COMPAT_ITE_IT8XXX2_WATCHDOG := ite,it8xxx2-watchdog

config DT_HAS_ITE_IT8XXX2_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WATCHDOG))

DT_COMPAT_ITE_IT8XXX2_WUC := ite,it8xxx2-wuc

config DT_HAS_ITE_IT8XXX2_WUC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WUC))

DT_COMPAT_ITE_IT8XXX2_WUC_MAP := ite,it8xxx2-wuc-map

config DT_HAS_ITE_IT8XXX2_WUC_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WUC_MAP))

DT_COMPAT_ITE_RISCV_ITE := ite,riscv-ite

config DT_HAS_ITE_RISCV_ITE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_RISCV_ITE))

DT_COMPAT_JEDEC_JC_42_4_TEMP := jedec,jc-42.4-temp

config DT_HAS_JEDEC_JC_42_4_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JEDEC_JC_42_4_TEMP))

DT_COMPAT_JEDEC_MSPI_NOR := jedec,mspi-nor

config DT_HAS_JEDEC_MSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JEDEC_MSPI_NOR))

DT_COMPAT_JEDEC_SPI_NOR := jedec,spi-nor

config DT_HAS_JEDEC_SPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JEDEC_SPI_NOR))

DT_COMPAT_JHD_JHD1313 := jhd,jhd1313

config DT_HAS_JHD_JHD1313_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JHD_JHD1313))

DT_COMPAT_KVASER_PCICAN := kvaser,pcican

config DT_HAS_KVASER_PCICAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_KVASER_PCICAN))

DT_COMPAT_LATTICE_ICE40_FPGA := lattice,ice40-fpga

config DT_HAS_LATTICE_ICE40_FPGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LATTICE_ICE40_FPGA))

DT_COMPAT_LATTICE_ICE40_FPGA_BASE := lattice,ice40-fpga-base

config DT_HAS_LATTICE_ICE40_FPGA_BASE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LATTICE_ICE40_FPGA_BASE))

DT_COMPAT_LATTICE_ICE40_FPGA_BITBANG := lattice,ice40-fpga-bitbang

config DT_HAS_LATTICE_ICE40_FPGA_BITBANG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LATTICE_ICE40_FPGA_BITBANG))

DT_COMPAT_LED_STRIP_MATRIX := led-strip-matrix

config DT_HAS_LED_STRIP_MATRIX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LED_STRIP_MATRIX))

DT_COMPAT_LINARO_96B_LSCON_1V8 := linaro,96b-lscon-1v8

config DT_HAS_LINARO_96B_LSCON_1V8_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_96B_LSCON_1V8))

DT_COMPAT_LINARO_96B_LSCON_3V3 := linaro,96b-lscon-3v3

config DT_HAS_LINARO_96B_LSCON_3V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_96B_LSCON_3V3))

DT_COMPAT_LINARO_IVSHMEM_IPM := linaro,ivshmem-ipm

config DT_HAS_LINARO_IVSHMEM_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_IVSHMEM_IPM))

DT_COMPAT_LINARO_IVSHMEM_MBOX := linaro,ivshmem-mbox

config DT_HAS_LINARO_IVSHMEM_MBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_IVSHMEM_MBOX))

DT_COMPAT_LINARO_OPTEE_TZ := linaro,optee-tz

config DT_HAS_LINARO_OPTEE_TZ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_OPTEE_TZ))

DT_COMPAT_LITEX_CLK := litex,clk

config DT_HAS_LITEX_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_CLK))

DT_COMPAT_LITEX_CLKOUT := litex,clkout

config DT_HAS_LITEX_CLKOUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_CLKOUT))

DT_COMPAT_LITEX_DNA0 := litex,dna0

config DT_HAS_LITEX_DNA0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_DNA0))

DT_COMPAT_LITEX_GPIO := litex,gpio

config DT_HAS_LITEX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_GPIO))

DT_COMPAT_LITEX_I2C := litex,i2c

config DT_HAS_LITEX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_I2C))

DT_COMPAT_LITEX_I2S := litex,i2s

config DT_HAS_LITEX_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_I2S))

DT_COMPAT_LITEX_LITEETH := litex,liteeth

config DT_HAS_LITEX_LITEETH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_LITEETH))

DT_COMPAT_LITEX_LITEETH_MDIO := litex,liteeth-mdio

config DT_HAS_LITEX_LITEETH_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_LITEETH_MDIO))

DT_COMPAT_LITEX_PRBS := litex,prbs

config DT_HAS_LITEX_PRBS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_PRBS))

DT_COMPAT_LITEX_PWM := litex,pwm

config DT_HAS_LITEX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_PWM))

DT_COMPAT_LITEX_SOC_CONTROLLER := litex,soc-controller

config DT_HAS_LITEX_SOC_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_SOC_CONTROLLER))

DT_COMPAT_LITEX_SPI := litex,spi

config DT_HAS_LITEX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_SPI))

DT_COMPAT_LITEX_SPI_LITESPI := litex,spi-litespi

config DT_HAS_LITEX_SPI_LITESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_SPI_LITESPI))

DT_COMPAT_LITEX_TIMER0 := litex,timer0

config DT_HAS_LITEX_TIMER0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_TIMER0))

DT_COMPAT_LITEX_UART := litex,uart

config DT_HAS_LITEX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_UART))

DT_COMPAT_LITEX_VEXRISCV_INTC0 := litex,vexriscv-intc0

config DT_HAS_LITEX_VEXRISCV_INTC0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_VEXRISCV_INTC0))

DT_COMPAT_LITEX_VEXRISCV_STANDARD := litex,vexriscv-standard

config DT_HAS_LITEX_VEXRISCV_STANDARD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_VEXRISCV_STANDARD))

DT_COMPAT_LITEX_WATCHDOG := litex,watchdog

config DT_HAS_LITEX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_WATCHDOG))

DT_COMPAT_LLTC_LTC1660 := lltc,ltc1660

config DT_HAS_LLTC_LTC1660_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LLTC_LTC1660))

DT_COMPAT_LLTC_LTC1665 := lltc,ltc1665

config DT_HAS_LLTC_LTC1665_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LLTC_LTC1665))

DT_COMPAT_LLTC_LTC2451 := lltc,ltc2451

config DT_HAS_LLTC_LTC2451_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LLTC_LTC2451))

DT_COMPAT_LM35 := lm35

config DT_HAS_LM35_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LM35))

DT_COMPAT_LM75 := lm75

config DT_HAS_LM75_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LM75))

DT_COMPAT_LM77 := lm77

config DT_HAS_LM77_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LM77))

DT_COMPAT_LOWRISC_IBEX := lowrisc,ibex

config DT_HAS_LOWRISC_IBEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_IBEX))

DT_COMPAT_LOWRISC_MACHINE_TIMER := lowrisc,machine-timer

config DT_HAS_LOWRISC_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_MACHINE_TIMER))

DT_COMPAT_LOWRISC_OPENTITAN_AONTIMER := lowrisc,opentitan-aontimer

config DT_HAS_LOWRISC_OPENTITAN_AONTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_AONTIMER))

DT_COMPAT_LOWRISC_OPENTITAN_PWRMGR := lowrisc,opentitan-pwrmgr

config DT_HAS_LOWRISC_OPENTITAN_PWRMGR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_PWRMGR))

DT_COMPAT_LOWRISC_OPENTITAN_SPI := lowrisc,opentitan-spi

config DT_HAS_LOWRISC_OPENTITAN_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_SPI))

DT_COMPAT_LOWRISC_OPENTITAN_UART := lowrisc,opentitan-uart

config DT_HAS_LOWRISC_OPENTITAN_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_UART))

DT_COMPAT_LTR_F216A := ltr,f216a

config DT_HAS_LTR_F216A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LTR_F216A))

DT_COMPAT_LUATOS_AIR530Z := luatos,air530z

config DT_HAS_LUATOS_AIR530Z_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LUATOS_AIR530Z))

DT_COMPAT_M5STACK_ATOM_HEADER := m5stack,atom-header

config DT_HAS_M5STACK_ATOM_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_M5STACK_ATOM_HEADER))

DT_COMPAT_M5STACK_MBUS_HEADER := m5stack,mbus-header

config DT_HAS_M5STACK_MBUS_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_M5STACK_MBUS_HEADER))

DT_COMPAT_M5STACK_STAMPS3_HEADER := m5stack,stamps3-header

config DT_HAS_M5STACK_STAMPS3_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_M5STACK_STAMPS3_HEADER))

DT_COMPAT_MAXIM_DS1307 := maxim,ds1307

config DT_HAS_MAXIM_DS1307_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS1307))

DT_COMPAT_MAXIM_DS18B20 := maxim,ds18b20

config DT_HAS_MAXIM_DS18B20_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS18B20))

DT_COMPAT_MAXIM_DS18S20 := maxim,ds18s20

config DT_HAS_MAXIM_DS18S20_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS18S20))

DT_COMPAT_MAXIM_DS2482_800 := maxim,ds2482-800

config DT_HAS_MAXIM_DS2482_800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2482_800))

DT_COMPAT_MAXIM_DS2482_800_CHANNEL := maxim,ds2482-800-channel

config DT_HAS_MAXIM_DS2482_800_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2482_800_CHANNEL))

DT_COMPAT_MAXIM_DS2484 := maxim,ds2484

config DT_HAS_MAXIM_DS2484_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2484))

DT_COMPAT_MAXIM_DS2485 := maxim,ds2485

config DT_HAS_MAXIM_DS2485_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2485))

DT_COMPAT_MAXIM_DS3231 := maxim,ds3231

config DT_HAS_MAXIM_DS3231_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS3231))

DT_COMPAT_MAXIM_DS3231_MFD := maxim,ds3231-mfd

config DT_HAS_MAXIM_DS3231_MFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS3231_MFD))

DT_COMPAT_MAXIM_DS3231_RTC := maxim,ds3231-rtc

config DT_HAS_MAXIM_DS3231_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS3231_RTC))

DT_COMPAT_MAXIM_DS3231_SENSOR := maxim,ds3231-sensor

config DT_HAS_MAXIM_DS3231_SENSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS3231_SENSOR))

DT_COMPAT_MAXIM_MAX11102 := maxim,max11102

config DT_HAS_MAXIM_MAX11102_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11102))

DT_COMPAT_MAXIM_MAX11103 := maxim,max11103

config DT_HAS_MAXIM_MAX11103_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11103))

DT_COMPAT_MAXIM_MAX11105 := maxim,max11105

config DT_HAS_MAXIM_MAX11105_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11105))

DT_COMPAT_MAXIM_MAX11106 := maxim,max11106

config DT_HAS_MAXIM_MAX11106_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11106))

DT_COMPAT_MAXIM_MAX11110 := maxim,max11110

config DT_HAS_MAXIM_MAX11110_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11110))

DT_COMPAT_MAXIM_MAX11111 := maxim,max11111

config DT_HAS_MAXIM_MAX11111_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11111))

DT_COMPAT_MAXIM_MAX11115 := maxim,max11115

config DT_HAS_MAXIM_MAX11115_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11115))

DT_COMPAT_MAXIM_MAX11116 := maxim,max11116

config DT_HAS_MAXIM_MAX11116_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11116))

DT_COMPAT_MAXIM_MAX11117 := maxim,max11117

config DT_HAS_MAXIM_MAX11117_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11117))

DT_COMPAT_MAXIM_MAX11253 := maxim,max11253

config DT_HAS_MAXIM_MAX11253_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11253))

DT_COMPAT_MAXIM_MAX11254 := maxim,max11254

config DT_HAS_MAXIM_MAX11254_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11254))

DT_COMPAT_MAXIM_MAX17048 := maxim,max17048

config DT_HAS_MAXIM_MAX17048_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17048))

DT_COMPAT_MAXIM_MAX17055 := maxim,max17055

config DT_HAS_MAXIM_MAX17055_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17055))

DT_COMPAT_MAXIM_MAX17262 := maxim,max17262

config DT_HAS_MAXIM_MAX17262_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17262))

DT_COMPAT_MAXIM_MAX20335 := maxim,max20335

config DT_HAS_MAXIM_MAX20335_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX20335))

DT_COMPAT_MAXIM_MAX20335_CHARGER := maxim,max20335-charger

config DT_HAS_MAXIM_MAX20335_CHARGER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX20335_CHARGER))

DT_COMPAT_MAXIM_MAX20335_REGULATOR := maxim,max20335-regulator

config DT_HAS_MAXIM_MAX20335_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX20335_REGULATOR))

DT_COMPAT_MAXIM_MAX30101 := maxim,max30101

config DT_HAS_MAXIM_MAX30101_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX30101))

DT_COMPAT_MAXIM_MAX31790 := maxim,max31790

config DT_HAS_MAXIM_MAX31790_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31790))

DT_COMPAT_MAXIM_MAX31790_FAN_FAULT := maxim,max31790-fan-fault

config DT_HAS_MAXIM_MAX31790_FAN_FAULT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31790_FAN_FAULT))

DT_COMPAT_MAXIM_MAX31790_FAN_SPEED := maxim,max31790-fan-speed

config DT_HAS_MAXIM_MAX31790_FAN_SPEED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31790_FAN_SPEED))

DT_COMPAT_MAXIM_MAX31790_PWM := maxim,max31790-pwm

config DT_HAS_MAXIM_MAX31790_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31790_PWM))

DT_COMPAT_MAXIM_MAX31855 := maxim,max31855

config DT_HAS_MAXIM_MAX31855_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31855))

DT_COMPAT_MAXIM_MAX31865 := maxim,max31865

config DT_HAS_MAXIM_MAX31865_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31865))

DT_COMPAT_MAXIM_MAX31875 := maxim,max31875

config DT_HAS_MAXIM_MAX31875_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31875))

DT_COMPAT_MAXIM_MAX3421E_SPI := maxim,max3421e_spi

config DT_HAS_MAXIM_MAX3421E_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX3421E_SPI))

DT_COMPAT_MAXIM_MAX44009 := maxim,max44009

config DT_HAS_MAXIM_MAX44009_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX44009))

DT_COMPAT_MAXIM_MAX6675 := maxim,max6675

config DT_HAS_MAXIM_MAX6675_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX6675))

DT_COMPAT_MAXIM_MAX7219 := maxim,max7219

config DT_HAS_MAXIM_MAX7219_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX7219))

DT_COMPAT_MEAS_MS5607 := meas,ms5607

config DT_HAS_MEAS_MS5607_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEAS_MS5607))

DT_COMPAT_MEAS_MS5837 := meas,ms5837

config DT_HAS_MEAS_MS5837_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEAS_MS5837))

DT_COMPAT_MEDIATEK_ADSP_INTC := mediatek,adsp_intc

config DT_HAS_MEDIATEK_ADSP_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEDIATEK_ADSP_INTC))

DT_COMPAT_MEDIATEK_MT8195_CPUCLK := mediatek,mt8195_cpuclk

config DT_HAS_MEDIATEK_MT8195_CPUCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEDIATEK_MT8195_CPUCLK))

DT_COMPAT_MEDIATEK_OSTIMER64 := mediatek,ostimer64

config DT_HAS_MEDIATEK_OSTIMER64_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEDIATEK_OSTIMER64))

DT_COMPAT_MEMSIC_MC3419 := memsic,mc3419

config DT_HAS_MEMSIC_MC3419_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEMSIC_MC3419))

DT_COMPAT_MEMSIC_MMC56X3 := memsic,mmc56x3

config DT_HAS_MEMSIC_MMC56X3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEMSIC_MMC56X3))

DT_COMPAT_MICROBIT_EDGE_CONNECTOR := microbit,edge-connector

config DT_HAS_MICROBIT_EDGE_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROBIT_EDGE_CONNECTOR))

DT_COMPAT_MICROCHIP_CAP12XX := microchip,cap12xx

config DT_HAS_MICROCHIP_CAP12XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_CAP12XX))

DT_COMPAT_MICROCHIP_COREUART := microchip,coreuart

config DT_HAS_MICROCHIP_COREUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_COREUART))

DT_COMPAT_MICROCHIP_ENC28J60 := microchip,enc28j60

config DT_HAS_MICROCHIP_ENC28J60_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_ENC28J60))

DT_COMPAT_MICROCHIP_ENC424J600 := microchip,enc424j600

config DT_HAS_MICROCHIP_ENC424J600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_ENC424J600))

DT_COMPAT_MICROCHIP_KSZ8081 := microchip,ksz8081

config DT_HAS_MICROCHIP_KSZ8081_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_KSZ8081))

DT_COMPAT_MICROCHIP_KSZ8794 := microchip,ksz8794

config DT_HAS_MICROCHIP_KSZ8794_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_KSZ8794))

DT_COMPAT_MICROCHIP_KSZ8863 := microchip,ksz8863

config DT_HAS_MICROCHIP_KSZ8863_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_KSZ8863))

DT_COMPAT_MICROCHIP_LAN865X := microchip,lan865x

config DT_HAS_MICROCHIP_LAN865X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_LAN865X))

DT_COMPAT_MICROCHIP_LAN9250 := microchip,lan9250

config DT_HAS_MICROCHIP_LAN9250_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_LAN9250))

DT_COMPAT_MICROCHIP_MCP23008 := microchip,mcp23008

config DT_HAS_MICROCHIP_MCP23008_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23008))

DT_COMPAT_MICROCHIP_MCP23009 := microchip,mcp23009

config DT_HAS_MICROCHIP_MCP23009_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23009))

DT_COMPAT_MICROCHIP_MCP23016 := microchip,mcp23016

config DT_HAS_MICROCHIP_MCP23016_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23016))

DT_COMPAT_MICROCHIP_MCP23017 := microchip,mcp23017

config DT_HAS_MICROCHIP_MCP23017_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23017))

DT_COMPAT_MICROCHIP_MCP23018 := microchip,mcp23018

config DT_HAS_MICROCHIP_MCP23018_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23018))

DT_COMPAT_MICROCHIP_MCP23S08 := microchip,mcp23s08

config DT_HAS_MICROCHIP_MCP23S08_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23S08))

DT_COMPAT_MICROCHIP_MCP23S09 := microchip,mcp23s09

config DT_HAS_MICROCHIP_MCP23S09_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23S09))

DT_COMPAT_MICROCHIP_MCP23S17 := microchip,mcp23s17

config DT_HAS_MICROCHIP_MCP23S17_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23S17))

DT_COMPAT_MICROCHIP_MCP23S18 := microchip,mcp23s18

config DT_HAS_MICROCHIP_MCP23S18_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23S18))

DT_COMPAT_MICROCHIP_MCP2515 := microchip,mcp2515

config DT_HAS_MICROCHIP_MCP2515_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP2515))

DT_COMPAT_MICROCHIP_MCP251XFD := microchip,mcp251xfd

config DT_HAS_MICROCHIP_MCP251XFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP251XFD))

DT_COMPAT_MICROCHIP_MCP3204 := microchip,mcp3204

config DT_HAS_MICROCHIP_MCP3204_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP3204))

DT_COMPAT_MICROCHIP_MCP3208 := microchip,mcp3208

config DT_HAS_MICROCHIP_MCP3208_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP3208))

DT_COMPAT_MICROCHIP_MCP4725 := microchip,mcp4725

config DT_HAS_MICROCHIP_MCP4725_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP4725))

DT_COMPAT_MICROCHIP_MCP4728 := microchip,mcp4728

config DT_HAS_MICROCHIP_MCP4728_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP4728))

DT_COMPAT_MICROCHIP_MCP7940N := microchip,mcp7940n

config DT_HAS_MICROCHIP_MCP7940N_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP7940N))

DT_COMPAT_MICROCHIP_MCP9600 := microchip,mcp9600

config DT_HAS_MICROCHIP_MCP9600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP9600))

DT_COMPAT_MICROCHIP_MCP970X := microchip,mcp970x

config DT_HAS_MICROCHIP_MCP970X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP970X))

DT_COMPAT_MICROCHIP_MEC5_GPIO := microchip,mec5-gpio

config DT_HAS_MICROCHIP_MEC5_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MEC5_GPIO))

DT_COMPAT_MICROCHIP_MEC5_KTIMER := microchip,mec5-ktimer

config DT_HAS_MICROCHIP_MEC5_KTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MEC5_KTIMER))

DT_COMPAT_MICROCHIP_MEC5_PINCTRL := microchip,mec5-pinctrl

config DT_HAS_MICROCHIP_MEC5_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MEC5_PINCTRL))

DT_COMPAT_MICROCHIP_MPFS_GPIO := microchip,mpfs-gpio

config DT_HAS_MICROCHIP_MPFS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_GPIO))

DT_COMPAT_MICROCHIP_MPFS_I2C := microchip,mpfs-i2c

config DT_HAS_MICROCHIP_MPFS_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_I2C))

DT_COMPAT_MICROCHIP_MPFS_QSPI := microchip,mpfs-qspi

config DT_HAS_MICROCHIP_MPFS_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_QSPI))

DT_COMPAT_MICROCHIP_MPFS_SPI := microchip,mpfs-spi

config DT_HAS_MICROCHIP_MPFS_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_SPI))

DT_COMPAT_MICROCHIP_TCN75A := microchip,tcn75a

config DT_HAS_MICROCHIP_TCN75A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_TCN75A))

DT_COMPAT_MICROCHIP_XEC_ADC := microchip,xec-adc

config DT_HAS_MICROCHIP_XEC_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ADC))

DT_COMPAT_MICROCHIP_XEC_BBLED := microchip,xec-bbled

config DT_HAS_MICROCHIP_XEC_BBLED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_BBLED))

DT_COMPAT_MICROCHIP_XEC_BBRAM := microchip,xec-bbram

config DT_HAS_MICROCHIP_XEC_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_BBRAM))

DT_COMPAT_MICROCHIP_XEC_DMAC := microchip,xec-dmac

config DT_HAS_MICROCHIP_XEC_DMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_DMAC))

DT_COMPAT_MICROCHIP_XEC_ECIA := microchip,xec-ecia

config DT_HAS_MICROCHIP_XEC_ECIA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECIA))

DT_COMPAT_MICROCHIP_XEC_ECIA_GIRQ := microchip,xec-ecia-girq

config DT_HAS_MICROCHIP_XEC_ECIA_GIRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECIA_GIRQ))

DT_COMPAT_MICROCHIP_XEC_ECS := microchip,xec-ecs

config DT_HAS_MICROCHIP_XEC_ECS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECS))

DT_COMPAT_MICROCHIP_XEC_EEPROM := microchip,xec-eeprom

config DT_HAS_MICROCHIP_XEC_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_EEPROM))

DT_COMPAT_MICROCHIP_XEC_ESPI := microchip,xec-espi

config DT_HAS_MICROCHIP_XEC_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI))

DT_COMPAT_MICROCHIP_XEC_ESPI_HOST_DEV := microchip,xec-espi-host-dev

config DT_HAS_MICROCHIP_XEC_ESPI_HOST_DEV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_HOST_DEV))

DT_COMPAT_MICROCHIP_XEC_ESPI_SAF := microchip,xec-espi-saf

config DT_HAS_MICROCHIP_XEC_ESPI_SAF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_SAF))

DT_COMPAT_MICROCHIP_XEC_ESPI_SAF_V2 := microchip,xec-espi-saf-v2

config DT_HAS_MICROCHIP_XEC_ESPI_SAF_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_SAF_V2))

DT_COMPAT_MICROCHIP_XEC_ESPI_V2 := microchip,xec-espi-v2

config DT_HAS_MICROCHIP_XEC_ESPI_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_V2))

DT_COMPAT_MICROCHIP_XEC_ESPI_VW_ROUTING := microchip,xec-espi-vw-routing

config DT_HAS_MICROCHIP_XEC_ESPI_VW_ROUTING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_VW_ROUTING))

DT_COMPAT_MICROCHIP_XEC_GPIO := microchip,xec-gpio

config DT_HAS_MICROCHIP_XEC_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_GPIO))

DT_COMPAT_MICROCHIP_XEC_GPIO_V2 := microchip,xec-gpio-v2

config DT_HAS_MICROCHIP_XEC_GPIO_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_GPIO_V2))

DT_COMPAT_MICROCHIP_XEC_I2C := microchip,xec-i2c

config DT_HAS_MICROCHIP_XEC_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_I2C))

DT_COMPAT_MICROCHIP_XEC_I2C_V2 := microchip,xec-i2c-v2

config DT_HAS_MICROCHIP_XEC_I2C_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_I2C_V2))

DT_COMPAT_MICROCHIP_XEC_KBD := microchip,xec-kbd

config DT_HAS_MICROCHIP_XEC_KBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_KBD))

DT_COMPAT_MICROCHIP_XEC_PCR := microchip,xec-pcr

config DT_HAS_MICROCHIP_XEC_PCR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PCR))

DT_COMPAT_MICROCHIP_XEC_PECI := microchip,xec-peci

config DT_HAS_MICROCHIP_XEC_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PECI))

DT_COMPAT_MICROCHIP_XEC_PINCTRL := microchip,xec-pinctrl

config DT_HAS_MICROCHIP_XEC_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PINCTRL))

DT_COMPAT_MICROCHIP_XEC_PS2 := microchip,xec-ps2

config DT_HAS_MICROCHIP_XEC_PS2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PS2))

DT_COMPAT_MICROCHIP_XEC_PWM := microchip,xec-pwm

config DT_HAS_MICROCHIP_XEC_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PWM))

DT_COMPAT_MICROCHIP_XEC_PWMBBLED := microchip,xec-pwmbbled

config DT_HAS_MICROCHIP_XEC_PWMBBLED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PWMBBLED))

DT_COMPAT_MICROCHIP_XEC_QMSPI := microchip,xec-qmspi

config DT_HAS_MICROCHIP_XEC_QMSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_QMSPI))

DT_COMPAT_MICROCHIP_XEC_QMSPI_LDMA := microchip,xec-qmspi-ldma

config DT_HAS_MICROCHIP_XEC_QMSPI_LDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_QMSPI_LDMA))

DT_COMPAT_MICROCHIP_XEC_RTOS_TIMER := microchip,xec-rtos-timer

config DT_HAS_MICROCHIP_XEC_RTOS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_RTOS_TIMER))

DT_COMPAT_MICROCHIP_XEC_SYMCR := microchip,xec-symcr

config DT_HAS_MICROCHIP_XEC_SYMCR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_SYMCR))

DT_COMPAT_MICROCHIP_XEC_TACH := microchip,xec-tach

config DT_HAS_MICROCHIP_XEC_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_TACH))

DT_COMPAT_MICROCHIP_XEC_TIMER := microchip,xec-timer

config DT_HAS_MICROCHIP_XEC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_TIMER))

DT_COMPAT_MICROCHIP_XEC_UART := microchip,xec-uart

config DT_HAS_MICROCHIP_XEC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_UART))

DT_COMPAT_MICROCHIP_XEC_WATCHDOG := microchip,xec-watchdog

config DT_HAS_MICROCHIP_XEC_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_WATCHDOG))

DT_COMPAT_MICROCRYSTAL_RV_8263_C8 := microcrystal,rv-8263-c8

config DT_HAS_MICROCRYSTAL_RV_8263_C8_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCRYSTAL_RV_8263_C8))

DT_COMPAT_MICROCRYSTAL_RV3028 := microcrystal,rv3028

config DT_HAS_MICROCRYSTAL_RV3028_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCRYSTAL_RV3028))

DT_COMPAT_MICRON_MT25QU02G := micron,mt25qu02g

config DT_HAS_MICRON_MT25QU02G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICRON_MT25QU02G))

DT_COMPAT_MIKRO_BUS := mikro-bus

config DT_HAS_MIKRO_BUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MIKRO_BUS))

DT_COMPAT_MMIO_SRAM := mmio-sram

config DT_HAS_MMIO_SRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MMIO_SRAM))

DT_COMPAT_MOTOROLA_MC146818 := motorola,mc146818

config DT_HAS_MOTOROLA_MC146818_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MOTOROLA_MC146818))

DT_COMPAT_MPS_MPM54304 := mps,mpm54304

config DT_HAS_MPS_MPM54304_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MPS_MPM54304))

DT_COMPAT_MSPI_APS6404L := mspi-aps6404l

config DT_HAS_MSPI_APS6404L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MSPI_APS6404L))

DT_COMPAT_MSPI_ATXP032 := mspi-atxp032

config DT_HAS_MSPI_ATXP032_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MSPI_ATXP032))

DT_COMPAT_MTI_CPU_INTC := mti,cpu-intc

config DT_HAS_MTI_CPU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MTI_CPU_INTC))

DT_COMPAT_MURATA_NCP15WB473 := murata,ncp15wb473

config DT_HAS_MURATA_NCP15WB473_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MURATA_NCP15WB473))

DT_COMPAT_MURATA_NCP15XH103 := murata,ncp15xh103

config DT_HAS_MURATA_NCP15XH103_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MURATA_NCP15XH103))

DT_COMPAT_NATIONAL_LM95234 := national,lm95234

config DT_HAS_NATIONAL_LM95234_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NATIONAL_LM95234))

DT_COMPAT_NEORV32_CPU := neorv32-cpu

config DT_HAS_NEORV32_CPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_CPU))

DT_COMPAT_NEORV32_GPIO := neorv32-gpio

config DT_HAS_NEORV32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_GPIO))

DT_COMPAT_NEORV32_MACHINE_TIMER := neorv32-machine-timer

config DT_HAS_NEORV32_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_MACHINE_TIMER))

DT_COMPAT_NEORV32_TRNG := neorv32-trng

config DT_HAS_NEORV32_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_TRNG))

DT_COMPAT_NEORV32_UART := neorv32-uart

config DT_HAS_NEORV32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_UART))

DT_COMPAT_NINTENDO_NUNCHUK := nintendo,nunchuk

config DT_HAS_NINTENDO_NUNCHUK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NINTENDO_NUNCHUK))

DT_COMPAT_NIOSV_MACHINE_TIMER := niosv-machine-timer

config DT_HAS_NIOSV_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NIOSV_MACHINE_TIMER))

DT_COMPAT_NORDIC_BT_CS_ANTENNA_SWITCH := nordic,bt-cs-antenna-switch

config DT_HAS_NORDIC_BT_CS_ANTENNA_SWITCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_BT_CS_ANTENNA_SWITCH))

DT_COMPAT_NORDIC_BT_HCI_SDC := nordic,bt-hci-sdc

config DT_HAS_NORDIC_BT_HCI_SDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_BT_HCI_SDC))

DT_COMPAT_NORDIC_ENTROPY_PRNG := nordic,entropy-prng

config DT_HAS_NORDIC_ENTROPY_PRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_ENTROPY_PRNG))

DT_COMPAT_NORDIC_GPIO_PINS := nordic,gpio-pins

config DT_HAS_NORDIC_GPIO_PINS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_GPIO_PINS))

DT_COMPAT_NORDIC_HPF_GPIO := nordic,hpf-gpio

config DT_HAS_NORDIC_HPF_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_HPF_GPIO))

DT_COMPAT_NORDIC_HPF_MSPI_CONTROLLER := nordic,hpf-mspi-controller

config DT_HAS_NORDIC_HPF_MSPI_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_HPF_MSPI_CONTROLLER))

DT_COMPAT_NORDIC_MBOX_NRF_IPC := nordic,mbox-nrf-ipc

config DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_MBOX_NRF_IPC))

DT_COMPAT_NORDIC_MRAM := nordic,mram

config DT_HAS_NORDIC_MRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_MRAM))

DT_COMPAT_NORDIC_NPM1100 := nordic,npm1100

config DT_HAS_NORDIC_NPM1100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1100))

DT_COMPAT_NORDIC_NPM1300 := nordic,npm1300

config DT_HAS_NORDIC_NPM1300_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300))

DT_COMPAT_NORDIC_NPM1300_CHARGER := nordic,npm1300-charger

config DT_HAS_NORDIC_NPM1300_CHARGER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_CHARGER))

DT_COMPAT_NORDIC_NPM1300_GPIO := nordic,npm1300-gpio

config DT_HAS_NORDIC_NPM1300_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_GPIO))

DT_COMPAT_NORDIC_NPM1300_LED := nordic,npm1300-led

config DT_HAS_NORDIC_NPM1300_LED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_LED))

DT_COMPAT_NORDIC_NPM1300_REGULATOR := nordic,npm1300-regulator

config DT_HAS_NORDIC_NPM1300_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_REGULATOR))

DT_COMPAT_NORDIC_NPM1300_WDT := nordic,npm1300-wdt

config DT_HAS_NORDIC_NPM1300_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_WDT))

DT_COMPAT_NORDIC_NPM2100 := nordic,npm2100

config DT_HAS_NORDIC_NPM2100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM2100))

DT_COMPAT_NORDIC_NPM2100_GPIO := nordic,npm2100-gpio

config DT_HAS_NORDIC_NPM2100_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM2100_GPIO))

DT_COMPAT_NORDIC_NPM2100_REGULATOR := nordic,npm2100-regulator

config DT_HAS_NORDIC_NPM2100_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM2100_REGULATOR))

DT_COMPAT_NORDIC_NPM2100_VBAT := nordic,npm2100-vbat

config DT_HAS_NORDIC_NPM2100_VBAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM2100_VBAT))

DT_COMPAT_NORDIC_NPM2100_WDT := nordic,npm2100-wdt

config DT_HAS_NORDIC_NPM2100_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM2100_WDT))

DT_COMPAT_NORDIC_NPM6001 := nordic,npm6001

config DT_HAS_NORDIC_NPM6001_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001))

DT_COMPAT_NORDIC_NPM6001_GPIO := nordic,npm6001-gpio

config DT_HAS_NORDIC_NPM6001_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_GPIO))

DT_COMPAT_NORDIC_NPM6001_REGULATOR := nordic,npm6001-regulator

config DT_HAS_NORDIC_NPM6001_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_REGULATOR))

DT_COMPAT_NORDIC_NPM6001_WDT := nordic,npm6001-wdt

config DT_HAS_NORDIC_NPM6001_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_WDT))

DT_COMPAT_NORDIC_NRF_ACL := nordic,nrf-acl

config DT_HAS_NORDIC_NRF_ACL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ACL))

DT_COMPAT_NORDIC_NRF_ACLK := nordic,nrf-aclk

config DT_HAS_NORDIC_NRF_ACLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ACLK))

DT_COMPAT_NORDIC_NRF_ADC := nordic,nrf-adc

config DT_HAS_NORDIC_NRF_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ADC))

DT_COMPAT_NORDIC_NRF_AUXPLL := nordic,nrf-auxpll

config DT_HAS_NORDIC_NRF_AUXPLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_AUXPLL))

DT_COMPAT_NORDIC_NRF_BELLBOARD_RX := nordic,nrf-bellboard-rx

config DT_HAS_NORDIC_NRF_BELLBOARD_RX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_BELLBOARD_RX))

DT_COMPAT_NORDIC_NRF_BELLBOARD_TX := nordic,nrf-bellboard-tx

config DT_HAS_NORDIC_NRF_BELLBOARD_TX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_BELLBOARD_TX))

DT_COMPAT_NORDIC_NRF_BICR := nordic,nrf-bicr

config DT_HAS_NORDIC_NRF_BICR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_BICR))

DT_COMPAT_NORDIC_NRF_BPROT := nordic,nrf-bprot

config DT_HAS_NORDIC_NRF_BPROT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_BPROT))

DT_COMPAT_NORDIC_NRF_CAN := nordic,nrf-can

config DT_HAS_NORDIC_NRF_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CAN))

DT_COMPAT_NORDIC_NRF_CCM := nordic,nrf-ccm

config DT_HAS_NORDIC_NRF_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CCM))

DT_COMPAT_NORDIC_NRF_CLIC := nordic,nrf-clic

config DT_HAS_NORDIC_NRF_CLIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CLIC))

DT_COMPAT_NORDIC_NRF_CLOCK := nordic,nrf-clock

config DT_HAS_NORDIC_NRF_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CLOCK))

DT_COMPAT_NORDIC_NRF_COMP := nordic,nrf-comp

config DT_HAS_NORDIC_NRF_COMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_COMP))

DT_COMPAT_NORDIC_NRF_CTRLAPPERI := nordic,nrf-ctrlapperi

config DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CTRLAPPERI))

DT_COMPAT_NORDIC_NRF_DCNF := nordic,nrf-dcnf

config DT_HAS_NORDIC_NRF_DCNF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DCNF))

DT_COMPAT_NORDIC_NRF_DPPIC := nordic,nrf-dppic

config DT_HAS_NORDIC_NRF_DPPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DPPIC))

DT_COMPAT_NORDIC_NRF_DPPIC_GLOBAL := nordic,nrf-dppic-global

config DT_HAS_NORDIC_NRF_DPPIC_GLOBAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DPPIC_GLOBAL))

DT_COMPAT_NORDIC_NRF_DPPIC_LOCAL := nordic,nrf-dppic-local

config DT_HAS_NORDIC_NRF_DPPIC_LOCAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DPPIC_LOCAL))

DT_COMPAT_NORDIC_NRF_ECB := nordic,nrf-ecb

config DT_HAS_NORDIC_NRF_ECB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ECB))

DT_COMPAT_NORDIC_NRF_EGU := nordic,nrf-egu

config DT_HAS_NORDIC_NRF_EGU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_EGU))

DT_COMPAT_NORDIC_NRF_EXMIF := nordic,nrf-exmif

config DT_HAS_NORDIC_NRF_EXMIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_EXMIF))

DT_COMPAT_NORDIC_NRF_EXMIF_SPI := nordic,nrf-exmif-spi

config DT_HAS_NORDIC_NRF_EXMIF_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_EXMIF_SPI))

DT_COMPAT_NORDIC_NRF_FICR := nordic,nrf-ficr

config DT_HAS_NORDIC_NRF_FICR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_FICR))

DT_COMPAT_NORDIC_NRF_FLL16M := nordic,nrf-fll16m

config DT_HAS_NORDIC_NRF_FLL16M_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_FLL16M))

DT_COMPAT_NORDIC_NRF_GPD := nordic,nrf-gpd

config DT_HAS_NORDIC_NRF_GPD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPD))

DT_COMPAT_NORDIC_NRF_GPIO := nordic,nrf-gpio

config DT_HAS_NORDIC_NRF_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIO))

DT_COMPAT_NORDIC_NRF_GPIO_FORWARDER := nordic,nrf-gpio-forwarder

config DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIO_FORWARDER))

DT_COMPAT_NORDIC_NRF_GPIOTE := nordic,nrf-gpiote

config DT_HAS_NORDIC_NRF_GPIOTE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIOTE))

DT_COMPAT_NORDIC_NRF_GPREGRET := nordic,nrf-gpregret

config DT_HAS_NORDIC_NRF_GPREGRET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPREGRET))

DT_COMPAT_NORDIC_NRF_GRTC := nordic,nrf-grtc

config DT_HAS_NORDIC_NRF_GRTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GRTC))

DT_COMPAT_NORDIC_NRF_HFXO := nordic,nrf-hfxo

config DT_HAS_NORDIC_NRF_HFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_HFXO))

DT_COMPAT_NORDIC_NRF_HSFLL_GLOBAL := nordic,nrf-hsfll-global

config DT_HAS_NORDIC_NRF_HSFLL_GLOBAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_HSFLL_GLOBAL))

DT_COMPAT_NORDIC_NRF_HSFLL_LOCAL := nordic,nrf-hsfll-local

config DT_HAS_NORDIC_NRF_HSFLL_LOCAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_HSFLL_LOCAL))

DT_COMPAT_NORDIC_NRF_I2S := nordic,nrf-i2s

config DT_HAS_NORDIC_NRF_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_I2S))

DT_COMPAT_NORDIC_NRF_IEEE802154 := nordic,nrf-ieee802154

config DT_HAS_NORDIC_NRF_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IEEE802154))

DT_COMPAT_NORDIC_NRF_IPC := nordic,nrf-ipc

config DT_HAS_NORDIC_NRF_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPC))

DT_COMPAT_NORDIC_NRF_IPC_UART := nordic,nrf-ipc-uart

config DT_HAS_NORDIC_NRF_IPC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPC_UART))

DT_COMPAT_NORDIC_NRF_IPCT_GLOBAL := nordic,nrf-ipct-global

config DT_HAS_NORDIC_NRF_IPCT_GLOBAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPCT_GLOBAL))

DT_COMPAT_NORDIC_NRF_IPCT_LOCAL := nordic,nrf-ipct-local

config DT_HAS_NORDIC_NRF_IPCT_LOCAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPCT_LOCAL))

DT_COMPAT_NORDIC_NRF_KMU := nordic,nrf-kmu

config DT_HAS_NORDIC_NRF_KMU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_KMU))

DT_COMPAT_NORDIC_NRF_LED_MATRIX := nordic,nrf-led-matrix

config DT_HAS_NORDIC_NRF_LED_MATRIX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LED_MATRIX))

DT_COMPAT_NORDIC_NRF_LFCLK := nordic,nrf-lfclk

config DT_HAS_NORDIC_NRF_LFCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LFCLK))

DT_COMPAT_NORDIC_NRF_LFXO := nordic,nrf-lfxo

config DT_HAS_NORDIC_NRF_LFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LFXO))

DT_COMPAT_NORDIC_NRF_LPCOMP := nordic,nrf-lpcomp

config DT_HAS_NORDIC_NRF_LPCOMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LPCOMP))

DT_COMPAT_NORDIC_NRF_MPC := nordic,nrf-mpc

config DT_HAS_NORDIC_NRF_MPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MPC))

DT_COMPAT_NORDIC_NRF_MPU := nordic,nrf-mpu

config DT_HAS_NORDIC_NRF_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MPU))

DT_COMPAT_NORDIC_NRF_MUTEX := nordic,nrf-mutex

config DT_HAS_NORDIC_NRF_MUTEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MUTEX))

DT_COMPAT_NORDIC_NRF_MWU := nordic,nrf-mwu

config DT_HAS_NORDIC_NRF_MWU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MWU))

DT_COMPAT_NORDIC_NRF_NFCT := nordic,nrf-nfct

config DT_HAS_NORDIC_NRF_NFCT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_NFCT))

DT_COMPAT_NORDIC_NRF_PDM := nordic,nrf-pdm

config DT_HAS_NORDIC_NRF_PDM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PDM))

DT_COMPAT_NORDIC_NRF_PINCTRL := nordic,nrf-pinctrl

config DT_HAS_NORDIC_NRF_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PINCTRL))

DT_COMPAT_NORDIC_NRF_POWER := nordic,nrf-power

config DT_HAS_NORDIC_NRF_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_POWER))

DT_COMPAT_NORDIC_NRF_PPI := nordic,nrf-ppi

config DT_HAS_NORDIC_NRF_PPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PPI))

DT_COMPAT_NORDIC_NRF_PPIB := nordic,nrf-ppib

config DT_HAS_NORDIC_NRF_PPIB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PPIB))

DT_COMPAT_NORDIC_NRF_PWM := nordic,nrf-pwm

config DT_HAS_NORDIC_NRF_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PWM))

DT_COMPAT_NORDIC_NRF_QDEC := nordic,nrf-qdec

config DT_HAS_NORDIC_NRF_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_QDEC))

DT_COMPAT_NORDIC_NRF_QSPI := nordic,nrf-qspi

config DT_HAS_NORDIC_NRF_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_QSPI))

DT_COMPAT_NORDIC_NRF_RADIO := nordic,nrf-radio

config DT_HAS_NORDIC_NRF_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RADIO))

DT_COMPAT_NORDIC_NRF_RESET := nordic,nrf-reset

config DT_HAS_NORDIC_NRF_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RESET))

DT_COMPAT_NORDIC_NRF_RESETINFO := nordic,nrf-resetinfo

config DT_HAS_NORDIC_NRF_RESETINFO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RESETINFO))

DT_COMPAT_NORDIC_NRF_RNG := nordic,nrf-rng

config DT_HAS_NORDIC_NRF_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RNG))

DT_COMPAT_NORDIC_NRF_RTC := nordic,nrf-rtc

config DT_HAS_NORDIC_NRF_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RTC))

DT_COMPAT_NORDIC_NRF_SAADC := nordic,nrf-saadc

config DT_HAS_NORDIC_NRF_SAADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SAADC))

DT_COMPAT_NORDIC_NRF_SPI := nordic,nrf-spi

config DT_HAS_NORDIC_NRF_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPI))

DT_COMPAT_NORDIC_NRF_SPIM := nordic,nrf-spim

config DT_HAS_NORDIC_NRF_SPIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPIM))

DT_COMPAT_NORDIC_NRF_SPIS := nordic,nrf-spis

config DT_HAS_NORDIC_NRF_SPIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPIS))

DT_COMPAT_NORDIC_NRF_SPU := nordic,nrf-spu

config DT_HAS_NORDIC_NRF_SPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPU))

DT_COMPAT_NORDIC_NRF_SQSPI := nordic,nrf-sqspi

config DT_HAS_NORDIC_NRF_SQSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SQSPI))

DT_COMPAT_NORDIC_NRF_SW_LPUART := nordic,nrf-sw-lpuart

config DT_HAS_NORDIC_NRF_SW_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SW_LPUART))

DT_COMPAT_NORDIC_NRF_SW_PWM := nordic,nrf-sw-pwm

config DT_HAS_NORDIC_NRF_SW_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SW_PWM))

DT_COMPAT_NORDIC_NRF_SWI := nordic,nrf-swi

config DT_HAS_NORDIC_NRF_SWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SWI))

DT_COMPAT_NORDIC_NRF_TBM := nordic,nrf-tbm

config DT_HAS_NORDIC_NRF_TBM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TBM))

DT_COMPAT_NORDIC_NRF_TDDCONF := nordic,nrf-tddconf

config DT_HAS_NORDIC_NRF_TDDCONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TDDCONF))

DT_COMPAT_NORDIC_NRF_TDM := nordic,nrf-tdm

config DT_HAS_NORDIC_NRF_TDM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TDM))

DT_COMPAT_NORDIC_NRF_TEMP := nordic,nrf-temp

config DT_HAS_NORDIC_NRF_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TEMP))

DT_COMPAT_NORDIC_NRF_TEMP_NRFS := nordic,nrf-temp-nrfs

config DT_HAS_NORDIC_NRF_TEMP_NRFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TEMP_NRFS))

DT_COMPAT_NORDIC_NRF_TIMER := nordic,nrf-timer

config DT_HAS_NORDIC_NRF_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TIMER))

DT_COMPAT_NORDIC_NRF_TWI := nordic,nrf-twi

config DT_HAS_NORDIC_NRF_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWI))

DT_COMPAT_NORDIC_NRF_TWIM := nordic,nrf-twim

config DT_HAS_NORDIC_NRF_TWIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWIM))

DT_COMPAT_NORDIC_NRF_TWIS := nordic,nrf-twis

config DT_HAS_NORDIC_NRF_TWIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWIS))

DT_COMPAT_NORDIC_NRF_UART := nordic,nrf-uart

config DT_HAS_NORDIC_NRF_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UART))

DT_COMPAT_NORDIC_NRF_UARTE := nordic,nrf-uarte

config DT_HAS_NORDIC_NRF_UARTE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UARTE))

DT_COMPAT_NORDIC_NRF_UICR := nordic,nrf-uicr

config DT_HAS_NORDIC_NRF_UICR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UICR))

DT_COMPAT_NORDIC_NRF_UICR_V2 := nordic,nrf-uicr-v2

config DT_HAS_NORDIC_NRF_UICR_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UICR_V2))

DT_COMPAT_NORDIC_NRF_USBD := nordic,nrf-usbd

config DT_HAS_NORDIC_NRF_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_USBD))

DT_COMPAT_NORDIC_NRF_USBREG := nordic,nrf-usbreg

config DT_HAS_NORDIC_NRF_USBREG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_USBREG))

DT_COMPAT_NORDIC_NRF_VEVIF_EVENT_RX := nordic,nrf-vevif-event-rx

config DT_HAS_NORDIC_NRF_VEVIF_EVENT_RX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VEVIF_EVENT_RX))

DT_COMPAT_NORDIC_NRF_VEVIF_EVENT_TX := nordic,nrf-vevif-event-tx

config DT_HAS_NORDIC_NRF_VEVIF_EVENT_TX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VEVIF_EVENT_TX))

DT_COMPAT_NORDIC_NRF_VEVIF_TASK_RX := nordic,nrf-vevif-task-rx

config DT_HAS_NORDIC_NRF_VEVIF_TASK_RX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VEVIF_TASK_RX))

DT_COMPAT_NORDIC_NRF_VEVIF_TASK_TX := nordic,nrf-vevif-task-tx

config DT_HAS_NORDIC_NRF_VEVIF_TASK_TX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VEVIF_TASK_TX))

DT_COMPAT_NORDIC_NRF_VMC := nordic,nrf-vmc

config DT_HAS_NORDIC_NRF_VMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VMC))

DT_COMPAT_NORDIC_NRF_VPR_COPROCESSOR := nordic,nrf-vpr-coprocessor

config DT_HAS_NORDIC_NRF_VPR_COPROCESSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VPR_COPROCESSOR))

DT_COMPAT_NORDIC_NRF_WDT := nordic,nrf-wdt

config DT_HAS_NORDIC_NRF_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_WDT))

DT_COMPAT_NORDIC_NRF21540_FEM := nordic,nrf21540-fem

config DT_HAS_NORDIC_NRF21540_FEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF21540_FEM))

DT_COMPAT_NORDIC_NRF21540_FEM_SPI := nordic,nrf21540-fem-spi

config DT_HAS_NORDIC_NRF21540_FEM_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF21540_FEM_SPI))

DT_COMPAT_NORDIC_NRF2220_FEM := nordic,nrf2220-fem

config DT_HAS_NORDIC_NRF2220_FEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF2220_FEM))

DT_COMPAT_NORDIC_NRF2220_FEM_TWI := nordic,nrf2220-fem-twi

config DT_HAS_NORDIC_NRF2220_FEM_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF2220_FEM_TWI))

DT_COMPAT_NORDIC_NRF2240_FEM := nordic,nrf2240-fem

config DT_HAS_NORDIC_NRF2240_FEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF2240_FEM))

DT_COMPAT_NORDIC_NRF2240_FEM_TWI := nordic,nrf2240-fem-twi

config DT_HAS_NORDIC_NRF2240_FEM_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF2240_FEM_TWI))

DT_COMPAT_NORDIC_NRF51_FLASH_CONTROLLER := nordic,nrf51-flash-controller

config DT_HAS_NORDIC_NRF51_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF51_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF52_FLASH_CONTROLLER := nordic,nrf52-flash-controller

config DT_HAS_NORDIC_NRF52_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF52_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF52X_REGULATOR_HV := nordic,nrf52x-regulator-hv

config DT_HAS_NORDIC_NRF52X_REGULATOR_HV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF52X_REGULATOR_HV))

DT_COMPAT_NORDIC_NRF53_FLASH_CONTROLLER := nordic,nrf53-flash-controller

config DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF53_HFXO := nordic,nrf53-hfxo

config DT_HAS_NORDIC_NRF53_HFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53_HFXO))

DT_COMPAT_NORDIC_NRF53_LFXO := nordic,nrf53-lfxo

config DT_HAS_NORDIC_NRF53_LFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53_LFXO))

DT_COMPAT_NORDIC_NRF53_OSCILLATORS := nordic,nrf53-oscillators

config DT_HAS_NORDIC_NRF53_OSCILLATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53_OSCILLATORS))

DT_COMPAT_NORDIC_NRF53X_REGULATOR_HV := nordic,nrf53x-regulator-hv

config DT_HAS_NORDIC_NRF53X_REGULATOR_HV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53X_REGULATOR_HV))

DT_COMPAT_NORDIC_NRF53X_REGULATORS := nordic,nrf53x-regulators

config DT_HAS_NORDIC_NRF53X_REGULATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53X_REGULATORS))

DT_COMPAT_NORDIC_NRF54H_HFXO := nordic,nrf54h-hfxo

config DT_HAS_NORDIC_NRF54H_HFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF54H_HFXO))

DT_COMPAT_NORDIC_NRF54H_LFXO := nordic,nrf54h-lfxo

config DT_HAS_NORDIC_NRF54H_LFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF54H_LFXO))

DT_COMPAT_NORDIC_NRF54L_REGULATORS := nordic,nrf54l-regulators

config DT_HAS_NORDIC_NRF54L_REGULATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF54L_REGULATORS))

DT_COMPAT_NORDIC_NRF5X_REGULATOR := nordic,nrf5x-regulator

config DT_HAS_NORDIC_NRF5X_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF5X_REGULATOR))

DT_COMPAT_NORDIC_NRF7000_COEX := nordic,nrf7000-coex

config DT_HAS_NORDIC_NRF7000_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7000_COEX))

DT_COMPAT_NORDIC_NRF7000_QSPI := nordic,nrf7000-qspi

config DT_HAS_NORDIC_NRF7000_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7000_QSPI))

DT_COMPAT_NORDIC_NRF7000_SPI := nordic,nrf7000-spi

config DT_HAS_NORDIC_NRF7000_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7000_SPI))

DT_COMPAT_NORDIC_NRF7001_COEX := nordic,nrf7001-coex

config DT_HAS_NORDIC_NRF7001_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7001_COEX))

DT_COMPAT_NORDIC_NRF7001_QSPI := nordic,nrf7001-qspi

config DT_HAS_NORDIC_NRF7001_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7001_QSPI))

DT_COMPAT_NORDIC_NRF7001_SPI := nordic,nrf7001-spi

config DT_HAS_NORDIC_NRF7001_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7001_SPI))

DT_COMPAT_NORDIC_NRF7002_COEX := nordic,nrf7002-coex

config DT_HAS_NORDIC_NRF7002_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7002_COEX))

DT_COMPAT_NORDIC_NRF7002_QSPI := nordic,nrf7002-qspi

config DT_HAS_NORDIC_NRF7002_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7002_QSPI))

DT_COMPAT_NORDIC_NRF7002_SPI := nordic,nrf7002-spi

config DT_HAS_NORDIC_NRF7002_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF7002_SPI))

DT_COMPAT_NORDIC_NRF91_FLASH_CONTROLLER := nordic,nrf91-flash-controller

config DT_HAS_NORDIC_NRF91_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF91_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF91_SLM := nordic,nrf91-slm

config DT_HAS_NORDIC_NRF91_SLM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF91_SLM))

DT_COMPAT_NORDIC_NRF91X_REGULATORS := nordic,nrf91x-regulators

config DT_HAS_NORDIC_NRF91X_REGULATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF91X_REGULATORS))

DT_COMPAT_NORDIC_NRFS_AUDIOPLL := nordic,nrfs-audiopll

config DT_HAS_NORDIC_NRFS_AUDIOPLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRFS_AUDIOPLL))

DT_COMPAT_NORDIC_OWNED_MEMORY := nordic,owned-memory

config DT_HAS_NORDIC_OWNED_MEMORY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_OWNED_MEMORY))

DT_COMPAT_NORDIC_OWNED_PARTITIONS := nordic,owned-partitions

config DT_HAS_NORDIC_OWNED_PARTITIONS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_OWNED_PARTITIONS))

DT_COMPAT_NORDIC_QSPI_NOR := nordic,qspi-nor

config DT_HAS_NORDIC_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_QSPI_NOR))

DT_COMPAT_NORDIC_RRAM_CONTROLLER := nordic,rram-controller

config DT_HAS_NORDIC_RRAM_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_RRAM_CONTROLLER))

DT_COMPAT_NORDIC_SENSOR_SIM := nordic,sensor-sim

config DT_HAS_NORDIC_SENSOR_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_SENSOR_SIM))

DT_COMPAT_NORDIC_SENSOR_STUB := nordic,sensor-stub

config DT_HAS_NORDIC_SENSOR_STUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_SENSOR_STUB))

DT_COMPAT_NORDIC_VPR := nordic,vpr

config DT_HAS_NORDIC_VPR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_VPR))

DT_COMPAT_NORDIC_WIFI71 := nordic,wifi71

config DT_HAS_NORDIC_WIFI71_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_WIFI71))

DT_COMPAT_NORDIC_THINGY53_EDGE_CONNECTOR := nordic-thingy53-edge-connector

config DT_HAS_NORDIC_THINGY53_EDGE_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_THINGY53_EDGE_CONNECTOR))

DT_COMPAT_NORITAKE_ITRON := noritake,itron

config DT_HAS_NORITAKE_ITRON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORITAKE_ITRON))

DT_COMPAT_NS16550 := ns16550

config DT_HAS_NS16550_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NS16550))

DT_COMPAT_NTC_THERMISTOR_GENERIC := ntc-thermistor-generic

config DT_HAS_NTC_THERMISTOR_GENERIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NTC_THERMISTOR_GENERIC))

DT_COMPAT_NUCLEI_BUMBLEBEE := nuclei,bumblebee

config DT_HAS_NUCLEI_BUMBLEBEE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_BUMBLEBEE))

DT_COMPAT_NUCLEI_ECLIC := nuclei,eclic

config DT_HAS_NUCLEI_ECLIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_ECLIC))

DT_COMPAT_NUCLEI_SYSTIMER := nuclei,systimer

config DT_HAS_NUCLEI_SYSTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_SYSTIMER))

DT_COMPAT_NUVOTON_ADC_CMP := nuvoton,adc-cmp

config DT_HAS_NUVOTON_ADC_CMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_ADC_CMP))

DT_COMPAT_NUVOTON_NCT38XX := nuvoton,nct38xx

config DT_HAS_NUVOTON_NCT38XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX))

DT_COMPAT_NUVOTON_NCT38XX_GPIO := nuvoton,nct38xx-gpio

config DT_HAS_NUVOTON_NCT38XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO))

DT_COMPAT_NUVOTON_NCT38XX_GPIO_ALERT := nuvoton,nct38xx-gpio-alert

config DT_HAS_NUVOTON_NCT38XX_GPIO_ALERT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO_ALERT))

DT_COMPAT_NUVOTON_NCT38XX_GPIO_PORT := nuvoton,nct38xx-gpio-port

config DT_HAS_NUVOTON_NCT38XX_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO_PORT))

DT_COMPAT_NUVOTON_NPCM_PCC := nuvoton,npcm-pcc

config DT_HAS_NUVOTON_NPCM_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCM_PCC))

DT_COMPAT_NUVOTON_NPCX_ADC := nuvoton,npcx-adc

config DT_HAS_NUVOTON_NPCX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ADC))

DT_COMPAT_NUVOTON_NPCX_BBRAM := nuvoton,npcx-bbram

config DT_HAS_NUVOTON_NPCX_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_BBRAM))

DT_COMPAT_NUVOTON_NPCX_BOOTER_VARIANT := nuvoton,npcx-booter-variant

config DT_HAS_NUVOTON_NPCX_BOOTER_VARIANT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_BOOTER_VARIANT))

DT_COMPAT_NUVOTON_NPCX_DRBG := nuvoton,npcx-drbg

config DT_HAS_NUVOTON_NPCX_DRBG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_DRBG))

DT_COMPAT_NUVOTON_NPCX_ESPI := nuvoton,npcx-espi

config DT_HAS_NUVOTON_NPCX_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ESPI))

DT_COMPAT_NUVOTON_NPCX_ESPI_TAF := nuvoton,npcx-espi-taf

config DT_HAS_NUVOTON_NPCX_ESPI_TAF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ESPI_TAF))

DT_COMPAT_NUVOTON_NPCX_ESPI_VW_CONF := nuvoton,npcx-espi-vw-conf

config DT_HAS_NUVOTON_NPCX_ESPI_VW_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ESPI_VW_CONF))

DT_COMPAT_NUVOTON_NPCX_FIU_NOR := nuvoton,npcx-fiu-nor

config DT_HAS_NUVOTON_NPCX_FIU_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_FIU_NOR))

DT_COMPAT_NUVOTON_NPCX_FIU_QSPI := nuvoton,npcx-fiu-qspi

config DT_HAS_NUVOTON_NPCX_FIU_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_FIU_QSPI))

DT_COMPAT_NUVOTON_NPCX_GPIO := nuvoton,npcx-gpio

config DT_HAS_NUVOTON_NPCX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_GPIO))

DT_COMPAT_NUVOTON_NPCX_HOST_SUB := nuvoton,npcx-host-sub

config DT_HAS_NUVOTON_NPCX_HOST_SUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_HOST_SUB))

DT_COMPAT_NUVOTON_NPCX_HOST_UART := nuvoton,npcx-host-uart

config DT_HAS_NUVOTON_NPCX_HOST_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_HOST_UART))

DT_COMPAT_NUVOTON_NPCX_I2C_CTRL := nuvoton,npcx-i2c-ctrl

config DT_HAS_NUVOTON_NPCX_I2C_CTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_I2C_CTRL))

DT_COMPAT_NUVOTON_NPCX_I2C_PORT := nuvoton,npcx-i2c-port

config DT_HAS_NUVOTON_NPCX_I2C_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_I2C_PORT))

DT_COMPAT_NUVOTON_NPCX_I3C := nuvoton,npcx-i3c

config DT_HAS_NUVOTON_NPCX_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_I3C))

DT_COMPAT_NUVOTON_NPCX_ITIM_TIMER := nuvoton,npcx-itim-timer

config DT_HAS_NUVOTON_NPCX_ITIM_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ITIM_TIMER))

DT_COMPAT_NUVOTON_NPCX_KBD := nuvoton,npcx-kbd

config DT_HAS_NUVOTON_NPCX_KBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_KBD))

DT_COMPAT_NUVOTON_NPCX_LEAKAGE_IO := nuvoton,npcx-leakage-io

config DT_HAS_NUVOTON_NPCX_LEAKAGE_IO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_LEAKAGE_IO))

DT_COMPAT_NUVOTON_NPCX_LVOLCTRL_CONF := nuvoton,npcx-lvolctrl-conf

config DT_HAS_NUVOTON_NPCX_LVOLCTRL_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_LVOLCTRL_CONF))

DT_COMPAT_NUVOTON_NPCX_MIWU := nuvoton,npcx-miwu

config DT_HAS_NUVOTON_NPCX_MIWU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU))

DT_COMPAT_NUVOTON_NPCX_MIWU_INT_MAP := nuvoton,npcx-miwu-int-map

config DT_HAS_NUVOTON_NPCX_MIWU_INT_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU_INT_MAP))

DT_COMPAT_NUVOTON_NPCX_MIWU_WUI_MAP := nuvoton,npcx-miwu-wui-map

config DT_HAS_NUVOTON_NPCX_MIWU_WUI_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU_WUI_MAP))

DT_COMPAT_NUVOTON_NPCX_PCC := nuvoton,npcx-pcc

config DT_HAS_NUVOTON_NPCX_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PCC))

DT_COMPAT_NUVOTON_NPCX_PECI := nuvoton,npcx-peci

config DT_HAS_NUVOTON_NPCX_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PECI))

DT_COMPAT_NUVOTON_NPCX_PINCTRL := nuvoton,npcx-pinctrl

config DT_HAS_NUVOTON_NPCX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL))

DT_COMPAT_NUVOTON_NPCX_PINCTRL_CONF := nuvoton,npcx-pinctrl-conf

config DT_HAS_NUVOTON_NPCX_PINCTRL_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL_CONF))

DT_COMPAT_NUVOTON_NPCX_PINCTRL_DEF := nuvoton,npcx-pinctrl-def

config DT_HAS_NUVOTON_NPCX_PINCTRL_DEF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL_DEF))

DT_COMPAT_NUVOTON_NPCX_POWER_PSL := nuvoton,npcx-power-psl

config DT_HAS_NUVOTON_NPCX_POWER_PSL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_POWER_PSL))

DT_COMPAT_NUVOTON_NPCX_PS2_CHANNEL := nuvoton,npcx-ps2-channel

config DT_HAS_NUVOTON_NPCX_PS2_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PS2_CHANNEL))

DT_COMPAT_NUVOTON_NPCX_PS2_CTRL := nuvoton,npcx-ps2-ctrl

config DT_HAS_NUVOTON_NPCX_PS2_CTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PS2_CTRL))

DT_COMPAT_NUVOTON_NPCX_PWM := nuvoton,npcx-pwm

config DT_HAS_NUVOTON_NPCX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PWM))

DT_COMPAT_NUVOTON_NPCX_RST := nuvoton,npcx-rst

config DT_HAS_NUVOTON_NPCX_RST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_RST))

DT_COMPAT_NUVOTON_NPCX_SCFG := nuvoton,npcx-scfg

config DT_HAS_NUVOTON_NPCX_SCFG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SCFG))

DT_COMPAT_NUVOTON_NPCX_SHA := nuvoton,npcx-sha

config DT_HAS_NUVOTON_NPCX_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SHA))

DT_COMPAT_NUVOTON_NPCX_SHI := nuvoton,npcx-shi

config DT_HAS_NUVOTON_NPCX_SHI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SHI))

DT_COMPAT_NUVOTON_NPCX_SHI_ENHANCED := nuvoton,npcx-shi-enhanced

config DT_HAS_NUVOTON_NPCX_SHI_ENHANCED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SHI_ENHANCED))

DT_COMPAT_NUVOTON_NPCX_SOC_ID := nuvoton,npcx-soc-id

config DT_HAS_NUVOTON_NPCX_SOC_ID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SOC_ID))

DT_COMPAT_NUVOTON_NPCX_SPIP := nuvoton,npcx-spip

config DT_HAS_NUVOTON_NPCX_SPIP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SPIP))

DT_COMPAT_NUVOTON_NPCX_TACH := nuvoton,npcx-tach

config DT_HAS_NUVOTON_NPCX_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_TACH))

DT_COMPAT_NUVOTON_NPCX_UART := nuvoton,npcx-uart

config DT_HAS_NUVOTON_NPCX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_UART))

DT_COMPAT_NUVOTON_NPCX_WATCHDOG := nuvoton,npcx-watchdog

config DT_HAS_NUVOTON_NPCX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_WATCHDOG))

DT_COMPAT_NUVOTON_NUMAKER_ADC := nuvoton,numaker-adc

config DT_HAS_NUVOTON_NUMAKER_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_ADC))

DT_COMPAT_NUVOTON_NUMAKER_CANFD := nuvoton,numaker-canfd

config DT_HAS_NUVOTON_NUMAKER_CANFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_CANFD))

DT_COMPAT_NUVOTON_NUMAKER_ETHERNET := nuvoton,numaker-ethernet

config DT_HAS_NUVOTON_NUMAKER_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_ETHERNET))

DT_COMPAT_NUVOTON_NUMAKER_FMC := nuvoton,numaker-fmc

config DT_HAS_NUVOTON_NUMAKER_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_FMC))

DT_COMPAT_NUVOTON_NUMAKER_GPIO := nuvoton,numaker-gpio

config DT_HAS_NUVOTON_NUMAKER_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_GPIO))

DT_COMPAT_NUVOTON_NUMAKER_I2C := nuvoton,numaker-i2c

config DT_HAS_NUVOTON_NUMAKER_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_I2C))

DT_COMPAT_NUVOTON_NUMAKER_PCC := nuvoton,numaker-pcc

config DT_HAS_NUVOTON_NUMAKER_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PCC))

DT_COMPAT_NUVOTON_NUMAKER_PINCTRL := nuvoton,numaker-pinctrl

config DT_HAS_NUVOTON_NUMAKER_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PINCTRL))

DT_COMPAT_NUVOTON_NUMAKER_PPC := nuvoton,numaker-ppc

config DT_HAS_NUVOTON_NUMAKER_PPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PPC))

DT_COMPAT_NUVOTON_NUMAKER_PWM := nuvoton,numaker-pwm

config DT_HAS_NUVOTON_NUMAKER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PWM))

DT_COMPAT_NUVOTON_NUMAKER_RMC := nuvoton,numaker-rmc

config DT_HAS_NUVOTON_NUMAKER_RMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_RMC))

DT_COMPAT_NUVOTON_NUMAKER_RST := nuvoton,numaker-rst

config DT_HAS_NUVOTON_NUMAKER_RST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_RST))

DT_COMPAT_NUVOTON_NUMAKER_RTC := nuvoton,numaker-rtc

config DT_HAS_NUVOTON_NUMAKER_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_RTC))

DT_COMPAT_NUVOTON_NUMAKER_SCC := nuvoton,numaker-scc

config DT_HAS_NUVOTON_NUMAKER_SCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_SCC))

DT_COMPAT_NUVOTON_NUMAKER_SPI := nuvoton,numaker-spi

config DT_HAS_NUVOTON_NUMAKER_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_SPI))

DT_COMPAT_NUVOTON_NUMAKER_TCPC := nuvoton,numaker-tcpc

config DT_HAS_NUVOTON_NUMAKER_TCPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_TCPC))

DT_COMPAT_NUVOTON_NUMAKER_UART := nuvoton,numaker-uart

config DT_HAS_NUVOTON_NUMAKER_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_UART))

DT_COMPAT_NUVOTON_NUMAKER_USBD := nuvoton,numaker-usbd

config DT_HAS_NUVOTON_NUMAKER_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_USBD))

DT_COMPAT_NUVOTON_NUMAKER_VBUS := nuvoton,numaker-vbus

config DT_HAS_NUVOTON_NUMAKER_VBUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_VBUS))

DT_COMPAT_NUVOTON_NUMAKER_WWDT := nuvoton,numaker-wwdt

config DT_HAS_NUVOTON_NUMAKER_WWDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_WWDT))

DT_COMPAT_NUVOTON_NUMICRO_GPIO := nuvoton,numicro-gpio

config DT_HAS_NUVOTON_NUMICRO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_GPIO))

DT_COMPAT_NUVOTON_NUMICRO_PINCTRL := nuvoton,numicro-pinctrl

config DT_HAS_NUVOTON_NUMICRO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_PINCTRL))

DT_COMPAT_NUVOTON_NUMICRO_UART := nuvoton,numicro-uart

config DT_HAS_NUVOTON_NUMICRO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_UART))

DT_COMPAT_NVME_CONTROLLER := nvme-controller

config DT_HAS_NVME_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NVME_CONTROLLER))

DT_COMPAT_NXP_ADC12 := nxp,adc12

config DT_HAS_NXP_ADC12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ADC12))

DT_COMPAT_NXP_AON_WAKEUP_PIN := nxp,aon-wakeup-pin

config DT_HAS_NXP_AON_WAKEUP_PIN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_AON_WAKEUP_PIN))

DT_COMPAT_NXP_BT_HCI_UART := nxp,bt-hci-uart

config DT_HAS_NXP_BT_HCI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_BT_HCI_UART))

DT_COMPAT_NXP_CAM_44PINS_CONNECTOR := nxp,cam-44pins-connector

config DT_HAS_NXP_CAM_44PINS_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_CAM_44PINS_CONNECTOR))

DT_COMPAT_NXP_CTIMER_PWM := nxp,ctimer-pwm

config DT_HAS_NXP_CTIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_CTIMER_PWM))

DT_COMPAT_NXP_DAI_ESAI := nxp,dai-esai

config DT_HAS_NXP_DAI_ESAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DAI_ESAI))

DT_COMPAT_NXP_DAI_SAI := nxp,dai-sai

config DT_HAS_NXP_DAI_SAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DAI_SAI))

DT_COMPAT_NXP_DCNANO_LCDIF := nxp,dcnano-lcdif

config DT_HAS_NXP_DCNANO_LCDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DCNANO_LCDIF))

DT_COMPAT_NXP_DMIC := nxp,dmic

config DT_HAS_NXP_DMIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DMIC))

DT_COMPAT_NXP_DSPI := nxp,dspi

config DT_HAS_NXP_DSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DSPI))

DT_COMPAT_NXP_EDMA := nxp,edma

config DT_HAS_NXP_EDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_EDMA))

DT_COMPAT_NXP_EHCI := nxp,ehci

config DT_HAS_NXP_EHCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_EHCI))

DT_COMPAT_NXP_ENET := nxp,enet

config DT_HAS_NXP_ENET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET))

DT_COMPAT_NXP_ENET_MAC := nxp,enet-mac

config DT_HAS_NXP_ENET_MAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_MAC))

DT_COMPAT_NXP_ENET_MDIO := nxp,enet-mdio

config DT_HAS_NXP_ENET_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_MDIO))

DT_COMPAT_NXP_ENET_PTP_CLOCK := nxp,enet-ptp-clock

config DT_HAS_NXP_ENET_PTP_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_PTP_CLOCK))

DT_COMPAT_NXP_ENET_QOS := nxp,enet-qos

config DT_HAS_NXP_ENET_QOS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_QOS))

DT_COMPAT_NXP_ENET_QOS_MAC := nxp,enet-qos-mac

config DT_HAS_NXP_ENET_QOS_MAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_QOS_MAC))

DT_COMPAT_NXP_ENET_QOS_MDIO := nxp,enet-qos-mdio

config DT_HAS_NXP_ENET_QOS_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET_QOS_MDIO))

DT_COMPAT_NXP_ENET1G := nxp,enet1g

config DT_HAS_NXP_ENET1G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_ENET1G))

DT_COMPAT_NXP_FLEXCAN := nxp,flexcan

config DT_HAS_NXP_FLEXCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXCAN))

DT_COMPAT_NXP_FLEXCAN_FD := nxp,flexcan-fd

config DT_HAS_NXP_FLEXCAN_FD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXCAN_FD))

DT_COMPAT_NXP_FLEXIO := nxp,flexio

config DT_HAS_NXP_FLEXIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXIO))

DT_COMPAT_NXP_FLEXIO_PWM := nxp,flexio-pwm

config DT_HAS_NXP_FLEXIO_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXIO_PWM))

DT_COMPAT_NXP_FLEXIO_SPI := nxp,flexio-spi

config DT_HAS_NXP_FLEXIO_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXIO_SPI))

DT_COMPAT_NXP_FLEXPWM := nxp,flexpwm

config DT_HAS_NXP_FLEXPWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXPWM))

DT_COMPAT_NXP_FLEXRAM := nxp,flexram

config DT_HAS_NXP_FLEXRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXRAM))

DT_COMPAT_NXP_FS26_WDOG := nxp,fs26-wdog

config DT_HAS_NXP_FS26_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FS26_WDOG))

DT_COMPAT_NXP_FTM := nxp,ftm

config DT_HAS_NXP_FTM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FTM))

DT_COMPAT_NXP_FTM_PWM := nxp,ftm-pwm

config DT_HAS_NXP_FTM_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FTM_PWM))

DT_COMPAT_NXP_FXAS21002 := nxp,fxas21002

config DT_HAS_NXP_FXAS21002_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FXAS21002))

DT_COMPAT_NXP_FXLS8974 := nxp,fxls8974

config DT_HAS_NXP_FXLS8974_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FXLS8974))

DT_COMPAT_NXP_FXOS8700 := nxp,fxos8700

config DT_HAS_NXP_FXOS8700_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FXOS8700))

DT_COMPAT_NXP_GAU_ADC := nxp,gau-adc

config DT_HAS_NXP_GAU_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_GAU_ADC))

DT_COMPAT_NXP_GAU_DAC := nxp,gau-dac

config DT_HAS_NXP_GAU_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_GAU_DAC))

DT_COMPAT_NXP_GPIO_CLUSTER := nxp,gpio-cluster

config DT_HAS_NXP_GPIO_CLUSTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_GPIO_CLUSTER))

DT_COMPAT_NXP_GPT_HW_TIMER := nxp,gpt-hw-timer

config DT_HAS_NXP_GPT_HW_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_GPT_HW_TIMER))

DT_COMPAT_NXP_HCI_BLE := nxp,hci-ble

config DT_HAS_NXP_HCI_BLE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_HCI_BLE))

DT_COMPAT_NXP_HDLC_RCP_IF := nxp,hdlc-rcp-if

config DT_HAS_NXP_HDLC_RCP_IF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_HDLC_RCP_IF))

DT_COMPAT_NXP_I2C_TSC_FPC := nxp,i2c-tsc-fpc

config DT_HAS_NXP_I2C_TSC_FPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_I2C_TSC_FPC))

DT_COMPAT_NXP_IAP_FMC11 := nxp,iap-fmc11

config DT_HAS_NXP_IAP_FMC11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC11))

DT_COMPAT_NXP_IAP_FMC54 := nxp,iap-fmc54

config DT_HAS_NXP_IAP_FMC54_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC54))

DT_COMPAT_NXP_IAP_FMC55 := nxp,iap-fmc55

config DT_HAS_NXP_IAP_FMC55_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC55))

DT_COMPAT_NXP_IAP_FMC553 := nxp,iap-fmc553

config DT_HAS_NXP_IAP_FMC553_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC553))

DT_COMPAT_NXP_IMX_ANATOP := nxp,imx-anatop

config DT_HAS_NXP_IMX_ANATOP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ANATOP))

DT_COMPAT_NXP_IMX_CAAM := nxp,imx-caam

config DT_HAS_NXP_IMX_CAAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CAAM))

DT_COMPAT_NXP_IMX_CCM := nxp,imx-ccm

config DT_HAS_NXP_IMX_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CCM))

DT_COMPAT_NXP_IMX_CCM_FNPLL := nxp,imx-ccm-fnpll

config DT_HAS_NXP_IMX_CCM_FNPLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CCM_FNPLL))

DT_COMPAT_NXP_IMX_CCM_REV2 := nxp,imx-ccm-rev2

config DT_HAS_NXP_IMX_CCM_REV2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CCM_REV2))

DT_COMPAT_NXP_IMX_CSI := nxp,imx-csi

config DT_HAS_NXP_IMX_CSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CSI))

DT_COMPAT_NXP_IMX_DTCM := nxp,imx-dtcm

config DT_HAS_NXP_IMX_DTCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_DTCM))

DT_COMPAT_NXP_IMX_ECSPI := nxp,imx-ecspi

config DT_HAS_NXP_IMX_ECSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ECSPI))

DT_COMPAT_NXP_IMX_ELCDIF := nxp,imx-elcdif

config DT_HAS_NXP_IMX_ELCDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ELCDIF))

DT_COMPAT_NXP_IMX_EPIT := nxp,imx-epit

config DT_HAS_NXP_IMX_EPIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_EPIT))

DT_COMPAT_NXP_IMX_FLEXSPI := nxp,imx-flexspi

config DT_HAS_NXP_IMX_FLEXSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI))

DT_COMPAT_NXP_IMX_FLEXSPI_APS6404L := nxp,imx-flexspi-aps6404l

config DT_HAS_NXP_IMX_FLEXSPI_APS6404L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_APS6404L))

DT_COMPAT_NXP_IMX_FLEXSPI_APS6408L := nxp,imx-flexspi-aps6408l

config DT_HAS_NXP_IMX_FLEXSPI_APS6408L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_APS6408L))

DT_COMPAT_NXP_IMX_FLEXSPI_HYPERFLASH := nxp,imx-flexspi-hyperflash

config DT_HAS_NXP_IMX_FLEXSPI_HYPERFLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_HYPERFLASH))

DT_COMPAT_NXP_IMX_FLEXSPI_IS66WVQ8M4 := nxp,imx-flexspi-is66wvq8m4

config DT_HAS_NXP_IMX_FLEXSPI_IS66WVQ8M4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_IS66WVQ8M4))

DT_COMPAT_NXP_IMX_FLEXSPI_MX25UM51345G := nxp,imx-flexspi-mx25um51345g

config DT_HAS_NXP_IMX_FLEXSPI_MX25UM51345G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_MX25UM51345G))

DT_COMPAT_NXP_IMX_FLEXSPI_NOR := nxp,imx-flexspi-nor

config DT_HAS_NXP_IMX_FLEXSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_NOR))

DT_COMPAT_NXP_IMX_FLEXSPI_S27KS0641 := nxp,imx-flexspi-s27ks0641

config DT_HAS_NXP_IMX_FLEXSPI_S27KS0641_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_S27KS0641))

DT_COMPAT_NXP_IMX_FLEXSPI_W956A8MBYA := nxp,imx-flexspi-w956a8mbya

config DT_HAS_NXP_IMX_FLEXSPI_W956A8MBYA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_W956A8MBYA))

DT_COMPAT_NXP_IMX_GPIO := nxp,imx-gpio

config DT_HAS_NXP_IMX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPIO))

DT_COMPAT_NXP_IMX_GPR := nxp,imx-gpr

config DT_HAS_NXP_IMX_GPR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPR))

DT_COMPAT_NXP_IMX_GPT := nxp,imx-gpt

config DT_HAS_NXP_IMX_GPT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPT))

DT_COMPAT_NXP_IMX_IOMUXC := nxp,imx-iomuxc

config DT_HAS_NXP_IMX_IOMUXC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_IOMUXC))

DT_COMPAT_NXP_IMX_IOMUXC_SCU := nxp,imx-iomuxc-scu

config DT_HAS_NXP_IMX_IOMUXC_SCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_IOMUXC_SCU))

DT_COMPAT_NXP_IMX_ITCM := nxp,imx-itcm

config DT_HAS_NXP_IMX_ITCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ITCM))

DT_COMPAT_NXP_IMX_IUART := nxp,imx-iuart

config DT_HAS_NXP_IMX_IUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_IUART))

DT_COMPAT_NXP_IMX_MIPI_DSI := nxp,imx-mipi-dsi

config DT_HAS_NXP_IMX_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_MIPI_DSI))

DT_COMPAT_NXP_IMX_MU := nxp,imx-mu

config DT_HAS_NXP_IMX_MU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_MU))

DT_COMPAT_NXP_IMX_NETC_EMDIO := nxp,imx-netc-emdio

config DT_HAS_NXP_IMX_NETC_EMDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_NETC_EMDIO))

DT_COMPAT_NXP_IMX_NETC_PSI := nxp,imx-netc-psi

config DT_HAS_NXP_IMX_NETC_PSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_NETC_PSI))

DT_COMPAT_NXP_IMX_PWM := nxp,imx-pwm

config DT_HAS_NXP_IMX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_PWM))

DT_COMPAT_NXP_IMX_QTMR := nxp,imx-qtmr

config DT_HAS_NXP_IMX_QTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_QTMR))

DT_COMPAT_NXP_IMX_RGPIO := nxp,imx-rgpio

config DT_HAS_NXP_IMX_RGPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_RGPIO))

DT_COMPAT_NXP_IMX_SEMC := nxp,imx-semc

config DT_HAS_NXP_IMX_SEMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_SEMC))

DT_COMPAT_NXP_IMX_SNVS_RTC := nxp,imx-snvs-rtc

config DT_HAS_NXP_IMX_SNVS_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_SNVS_RTC))

DT_COMPAT_NXP_IMX_TMR := nxp,imx-tmr

config DT_HAS_NXP_IMX_TMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_TMR))

DT_COMPAT_NXP_IMX_UART := nxp,imx-uart

config DT_HAS_NXP_IMX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_UART))

DT_COMPAT_NXP_IMX_USDHC := nxp,imx-usdhc

config DT_HAS_NXP_IMX_USDHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_USDHC))

DT_COMPAT_NXP_IMX_WDOG := nxp,imx-wdog

config DT_HAS_NXP_IMX_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_WDOG))

DT_COMPAT_NXP_IMX7D_PINCTRL := nxp,imx7d-pinctrl

config DT_HAS_NXP_IMX7D_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX7D_PINCTRL))

DT_COMPAT_NXP_IMX8_PINCTRL := nxp,imx8-pinctrl

config DT_HAS_NXP_IMX8_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8_PINCTRL))

DT_COMPAT_NXP_IMX8M_PINCTRL := nxp,imx8m-pinctrl

config DT_HAS_NXP_IMX8M_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8M_PINCTRL))

DT_COMPAT_NXP_IMX8MP_PINCTRL := nxp,imx8mp-pinctrl

config DT_HAS_NXP_IMX8MP_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8MP_PINCTRL))

DT_COMPAT_NXP_IMX8ULP_PINCTRL := nxp,imx8ulp-pinctrl

config DT_HAS_NXP_IMX8ULP_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8ULP_PINCTRL))

DT_COMPAT_NXP_IMX93_PINCTRL := nxp,imx93-pinctrl

config DT_HAS_NXP_IMX93_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX93_PINCTRL))

DT_COMPAT_NXP_IRQSTEER_INTC := nxp,irqsteer-intc

config DT_HAS_NXP_IRQSTEER_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IRQSTEER_INTC))

DT_COMPAT_NXP_IRQSTEER_MASTER := nxp,irqsteer-master

config DT_HAS_NXP_IRQSTEER_MASTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IRQSTEER_MASTER))

DT_COMPAT_NXP_IRTC := nxp,irtc

config DT_HAS_NXP_IRTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IRTC))

DT_COMPAT_NXP_KINETIS_ACMP := nxp,kinetis-acmp

config DT_HAS_NXP_KINETIS_ACMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ACMP))

DT_COMPAT_NXP_KINETIS_ADC16 := nxp,kinetis-adc16

config DT_HAS_NXP_KINETIS_ADC16_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ADC16))

DT_COMPAT_NXP_KINETIS_DAC := nxp,kinetis-dac

config DT_HAS_NXP_KINETIS_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_DAC))

DT_COMPAT_NXP_KINETIS_DAC32 := nxp,kinetis-dac32

config DT_HAS_NXP_KINETIS_DAC32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_DAC32))

DT_COMPAT_NXP_KINETIS_ETHERNET := nxp,kinetis-ethernet

config DT_HAS_NXP_KINETIS_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ETHERNET))

DT_COMPAT_NXP_KINETIS_FTFA := nxp,kinetis-ftfa

config DT_HAS_NXP_KINETIS_FTFA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFA))

DT_COMPAT_NXP_KINETIS_FTFE := nxp,kinetis-ftfe

config DT_HAS_NXP_KINETIS_FTFE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFE))

DT_COMPAT_NXP_KINETIS_FTFL := nxp,kinetis-ftfl

config DT_HAS_NXP_KINETIS_FTFL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFL))

DT_COMPAT_NXP_KINETIS_GPIO := nxp,kinetis-gpio

config DT_HAS_NXP_KINETIS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_GPIO))

DT_COMPAT_NXP_KINETIS_I2C := nxp,kinetis-i2c

config DT_HAS_NXP_KINETIS_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_I2C))

DT_COMPAT_NXP_KINETIS_KE1XF_SIM := nxp,kinetis-ke1xf-sim

config DT_HAS_NXP_KINETIS_KE1XF_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_KE1XF_SIM))

DT_COMPAT_NXP_KINETIS_LPSCI := nxp,kinetis-lpsci

config DT_HAS_NXP_KINETIS_LPSCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_LPSCI))

DT_COMPAT_NXP_KINETIS_LPTMR := nxp,kinetis-lptmr

config DT_HAS_NXP_KINETIS_LPTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_LPTMR))

DT_COMPAT_NXP_KINETIS_MCG := nxp,kinetis-mcg

config DT_HAS_NXP_KINETIS_MCG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_MCG))

DT_COMPAT_NXP_KINETIS_PCC := nxp,kinetis-pcc

config DT_HAS_NXP_KINETIS_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PCC))

DT_COMPAT_NXP_KINETIS_PTP := nxp,kinetis-ptp

config DT_HAS_NXP_KINETIS_PTP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PTP))

DT_COMPAT_NXP_KINETIS_PWT := nxp,kinetis-pwt

config DT_HAS_NXP_KINETIS_PWT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PWT))

DT_COMPAT_NXP_KINETIS_RNGA := nxp,kinetis-rnga

config DT_HAS_NXP_KINETIS_RNGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_RNGA))

DT_COMPAT_NXP_KINETIS_SCG := nxp,kinetis-scg

config DT_HAS_NXP_KINETIS_SCG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_SCG))

DT_COMPAT_NXP_KINETIS_SIM := nxp,kinetis-sim

config DT_HAS_NXP_KINETIS_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_SIM))

DT_COMPAT_NXP_KINETIS_TEMPERATURE := nxp,kinetis-temperature

config DT_HAS_NXP_KINETIS_TEMPERATURE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TEMPERATURE))

DT_COMPAT_NXP_KINETIS_TPM := nxp,kinetis-tpm

config DT_HAS_NXP_KINETIS_TPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TPM))

DT_COMPAT_NXP_KINETIS_TRNG := nxp,kinetis-trng

config DT_HAS_NXP_KINETIS_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TRNG))

DT_COMPAT_NXP_KINETIS_UART := nxp,kinetis-uart

config DT_HAS_NXP_KINETIS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_UART))

DT_COMPAT_NXP_KINETIS_USBD := nxp,kinetis-usbd

config DT_HAS_NXP_KINETIS_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_USBD))

DT_COMPAT_NXP_KINETIS_WDOG := nxp,kinetis-wdog

config DT_HAS_NXP_KINETIS_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_WDOG))

DT_COMPAT_NXP_KW41Z_IEEE802154 := nxp,kw41z-ieee802154

config DT_HAS_NXP_KW41Z_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KW41Z_IEEE802154))

DT_COMPAT_NXP_LCD_8080 := nxp,lcd-8080

config DT_HAS_NXP_LCD_8080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LCD_8080))

DT_COMPAT_NXP_LCDIC := nxp,lcdic

config DT_HAS_NXP_LCDIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LCDIC))

DT_COMPAT_NXP_LP_FLEXCOMM := nxp,lp-flexcomm

config DT_HAS_NXP_LP_FLEXCOMM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LP_FLEXCOMM))

DT_COMPAT_NXP_LPC_CTIMER := nxp,lpc-ctimer

config DT_HAS_NXP_LPC_CTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_CTIMER))

DT_COMPAT_NXP_LPC_DMA := nxp,lpc-dma

config DT_HAS_NXP_LPC_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_DMA))

DT_COMPAT_NXP_LPC_FLEXCOMM := nxp,lpc-flexcomm

config DT_HAS_NXP_LPC_FLEXCOMM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_FLEXCOMM))

DT_COMPAT_NXP_LPC_GPIO := nxp,lpc-gpio

config DT_HAS_NXP_LPC_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_GPIO))

DT_COMPAT_NXP_LPC_GPIO_PORT := nxp,lpc-gpio-port

config DT_HAS_NXP_LPC_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_GPIO_PORT))

DT_COMPAT_NXP_LPC_I2C := nxp,lpc-i2c

config DT_HAS_NXP_LPC_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_I2C))

DT_COMPAT_NXP_LPC_I2S := nxp,lpc-i2s

config DT_HAS_NXP_LPC_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_I2S))

DT_COMPAT_NXP_LPC_IOCON := nxp,lpc-iocon

config DT_HAS_NXP_LPC_IOCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON))

DT_COMPAT_NXP_LPC_IOCON_PINCTRL := nxp,lpc-iocon-pinctrl

config DT_HAS_NXP_LPC_IOCON_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON_PINCTRL))

DT_COMPAT_NXP_LPC_IOCON_PIO := nxp,lpc-iocon-pio

config DT_HAS_NXP_LPC_IOCON_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON_PIO))

DT_COMPAT_NXP_LPC_LPADC := nxp,lpc-lpadc

config DT_HAS_NXP_LPC_LPADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_LPADC))

DT_COMPAT_NXP_LPC_MAILBOX := nxp,lpc-mailbox

config DT_HAS_NXP_LPC_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_MAILBOX))

DT_COMPAT_NXP_LPC_MCAN := nxp,lpc-mcan

config DT_HAS_NXP_LPC_MCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_MCAN))

DT_COMPAT_NXP_LPC_RNG := nxp,lpc-rng

config DT_HAS_NXP_LPC_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_RNG))

DT_COMPAT_NXP_LPC_RTC := nxp,lpc-rtc

config DT_HAS_NXP_LPC_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_RTC))

DT_COMPAT_NXP_LPC_RTC_HIGHRES := nxp,lpc-rtc-highres

config DT_HAS_NXP_LPC_RTC_HIGHRES_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_RTC_HIGHRES))

DT_COMPAT_NXP_LPC_SDIF := nxp,lpc-sdif

config DT_HAS_NXP_LPC_SDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SDIF))

DT_COMPAT_NXP_LPC_SPI := nxp,lpc-spi

config DT_HAS_NXP_LPC_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SPI))

DT_COMPAT_NXP_LPC_SYSCON := nxp,lpc-syscon

config DT_HAS_NXP_LPC_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SYSCON))

DT_COMPAT_NXP_LPC_SYSCON_RESET := nxp,lpc-syscon-reset

config DT_HAS_NXP_LPC_SYSCON_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SYSCON_RESET))

DT_COMPAT_NXP_LPC_UID := nxp,lpc-uid

config DT_HAS_NXP_LPC_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_UID))

DT_COMPAT_NXP_LPC_USART := nxp,lpc-usart

config DT_HAS_NXP_LPC_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_USART))

DT_COMPAT_NXP_LPC_WWDT := nxp,lpc-wwdt

config DT_HAS_NXP_LPC_WWDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_WWDT))

DT_COMPAT_NXP_LPC11U6X_EEPROM := nxp,lpc11u6x-eeprom

config DT_HAS_NXP_LPC11U6X_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_EEPROM))

DT_COMPAT_NXP_LPC11U6X_GPIO := nxp,lpc11u6x-gpio

config DT_HAS_NXP_LPC11U6X_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_GPIO))

DT_COMPAT_NXP_LPC11U6X_I2C := nxp,lpc11u6x-i2c

config DT_HAS_NXP_LPC11U6X_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_I2C))

DT_COMPAT_NXP_LPC11U6X_PINCTRL := nxp,lpc11u6x-pinctrl

config DT_HAS_NXP_LPC11U6X_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_PINCTRL))

DT_COMPAT_NXP_LPC11U6X_SYSCON := nxp,lpc11u6x-syscon

config DT_HAS_NXP_LPC11U6X_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_SYSCON))

DT_COMPAT_NXP_LPC11U6X_UART := nxp,lpc11u6x-uart

config DT_HAS_NXP_LPC11U6X_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_UART))

DT_COMPAT_NXP_LPCIP3511 := nxp,lpcip3511

config DT_HAS_NXP_LPCIP3511_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPCIP3511))

DT_COMPAT_NXP_LPCMP := nxp,lpcmp

config DT_HAS_NXP_LPCMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPCMP))

DT_COMPAT_NXP_LPDAC := nxp,lpdac

config DT_HAS_NXP_LPDAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPDAC))

DT_COMPAT_NXP_LPI2C := nxp,lpi2c

config DT_HAS_NXP_LPI2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPI2C))

DT_COMPAT_NXP_LPSPI := nxp,lpspi

config DT_HAS_NXP_LPSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPSPI))

DT_COMPAT_NXP_LPTMR := nxp,lptmr

config DT_HAS_NXP_LPTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPTMR))

DT_COMPAT_NXP_LPUART := nxp,lpuart

config DT_HAS_NXP_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPUART))

DT_COMPAT_NXP_MBOX_IMX_MU := nxp,mbox-imx-mu

config DT_HAS_NXP_MBOX_IMX_MU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MBOX_IMX_MU))

DT_COMPAT_NXP_MBOX_MAILBOX := nxp,mbox-mailbox

config DT_HAS_NXP_MBOX_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MBOX_MAILBOX))

DT_COMPAT_NXP_MCI_IO_MUX := nxp,mci-io-mux

config DT_HAS_NXP_MCI_IO_MUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCI_IO_MUX))

DT_COMPAT_NXP_MCR20A := nxp,mcr20a

config DT_HAS_NXP_MCR20A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCR20A))

DT_COMPAT_NXP_MCUX_12B1MSPS_SAR := nxp,mcux-12b1msps-sar

config DT_HAS_NXP_MCUX_12B1MSPS_SAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_12B1MSPS_SAR))

DT_COMPAT_NXP_MCUX_DCP := nxp,mcux-dcp

config DT_HAS_NXP_MCUX_DCP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_DCP))

DT_COMPAT_NXP_MCUX_EDMA := nxp,mcux-edma

config DT_HAS_NXP_MCUX_EDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_EDMA))

DT_COMPAT_NXP_MCUX_I2S := nxp,mcux-i2s

config DT_HAS_NXP_MCUX_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_I2S))

DT_COMPAT_NXP_MCUX_I3C := nxp,mcux-i3c

config DT_HAS_NXP_MCUX_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_I3C))

DT_COMPAT_NXP_MCUX_QDEC := nxp,mcux-qdec

config DT_HAS_NXP_MCUX_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_QDEC))

DT_COMPAT_NXP_MCUX_RT_PINCTRL := nxp,mcux-rt-pinctrl

config DT_HAS_NXP_MCUX_RT_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_RT_PINCTRL))

DT_COMPAT_NXP_MCUX_RT11XX_PINCTRL := nxp,mcux-rt11xx-pinctrl

config DT_HAS_NXP_MCUX_RT11XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_RT11XX_PINCTRL))

DT_COMPAT_NXP_MCUX_XBAR := nxp,mcux-xbar

config DT_HAS_NXP_MCUX_XBAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_XBAR))

DT_COMPAT_NXP_MCXC_OSC := nxp,mcxc-osc

config DT_HAS_NXP_MCXC_OSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCXC_OSC))

DT_COMPAT_NXP_MIPI_CSI2RX := nxp,mipi-csi2rx

config DT_HAS_NXP_MIPI_CSI2RX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MIPI_CSI2RX))

DT_COMPAT_NXP_MIPI_DBI_FLEXIO_LCDIF := nxp,mipi-dbi-flexio-lcdif

config DT_HAS_NXP_MIPI_DBI_FLEXIO_LCDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MIPI_DBI_FLEXIO_LCDIF))

DT_COMPAT_NXP_MIPI_DSI_2L := nxp,mipi-dsi-2l

config DT_HAS_NXP_MIPI_DSI_2L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MIPI_DSI_2L))

DT_COMPAT_NXP_MRT := nxp,mrt

config DT_HAS_NXP_MRT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MRT))

DT_COMPAT_NXP_MRT_CHANNEL := nxp,mrt-channel

config DT_HAS_NXP_MRT_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MRT_CHANNEL))

DT_COMPAT_NXP_MSF1 := nxp,msf1

config DT_HAS_NXP_MSF1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MSF1))

DT_COMPAT_NXP_NBU := nxp,nbu

config DT_HAS_NXP_NBU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_NBU))

DT_COMPAT_NXP_NX20P3483 := nxp,nx20p3483

config DT_HAS_NXP_NX20P3483_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_NX20P3483))

DT_COMPAT_NXP_OS_TIMER := nxp,os-timer

config DT_HAS_NXP_OS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_OS_TIMER))

DT_COMPAT_NXP_P3T1755 := nxp,p3t1755

config DT_HAS_NXP_P3T1755_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_P3T1755))

DT_COMPAT_NXP_PARALLEL_LCD_CONNECTOR := nxp,parallel-lcd-connector

config DT_HAS_NXP_PARALLEL_LCD_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PARALLEL_LCD_CONNECTOR))

DT_COMPAT_NXP_PCA9420 := nxp,pca9420

config DT_HAS_NXP_PCA9420_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9420))

DT_COMPAT_NXP_PCA9538 := nxp,pca9538

config DT_HAS_NXP_PCA9538_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9538))

DT_COMPAT_NXP_PCA9539 := nxp,pca9539

config DT_HAS_NXP_PCA9539_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9539))

DT_COMPAT_NXP_PCA9554 := nxp,pca9554

config DT_HAS_NXP_PCA9554_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9554))

DT_COMPAT_NXP_PCA9555 := nxp,pca9555

config DT_HAS_NXP_PCA9555_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9555))

DT_COMPAT_NXP_PCA95XX := nxp,pca95xx

config DT_HAS_NXP_PCA95XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA95XX))

DT_COMPAT_NXP_PCA9633 := nxp,pca9633

config DT_HAS_NXP_PCA9633_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9633))

DT_COMPAT_NXP_PCA9685_PWM := nxp,pca9685-pwm

config DT_HAS_NXP_PCA9685_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9685_PWM))

DT_COMPAT_NXP_PCAL6408A := nxp,pcal6408a

config DT_HAS_NXP_PCAL6408A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6408A))

DT_COMPAT_NXP_PCAL6416A := nxp,pcal6416a

config DT_HAS_NXP_PCAL6416A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6416A))

DT_COMPAT_NXP_PCAL6524 := nxp,pcal6524

config DT_HAS_NXP_PCAL6524_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6524))

DT_COMPAT_NXP_PCAL6534 := nxp,pcal6534

config DT_HAS_NXP_PCAL6534_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6534))

DT_COMPAT_NXP_PCF8523 := nxp,pcf8523

config DT_HAS_NXP_PCF8523_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF8523))

DT_COMPAT_NXP_PCF8563 := nxp,pcf8563

config DT_HAS_NXP_PCF8563_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF8563))

DT_COMPAT_NXP_PCF857X := nxp,pcf857x

config DT_HAS_NXP_PCF857X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF857X))

DT_COMPAT_NXP_PDCFG_POWER := nxp,pdcfg-power

config DT_HAS_NXP_PDCFG_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PDCFG_POWER))

DT_COMPAT_NXP_PINT := nxp,pint

config DT_HAS_NXP_PINT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PINT))

DT_COMPAT_NXP_PIT := nxp,pit

config DT_HAS_NXP_PIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PIT))

DT_COMPAT_NXP_PIT_CHANNEL := nxp,pit-channel

config DT_HAS_NXP_PIT_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PIT_CHANNEL))

DT_COMPAT_NXP_PORT_PINCTRL := nxp,port-pinctrl

config DT_HAS_NXP_PORT_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PORT_PINCTRL))

DT_COMPAT_NXP_PORT_PINMUX := nxp,port-pinmux

config DT_HAS_NXP_PORT_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PORT_PINMUX))

DT_COMPAT_NXP_PXP := nxp,pxp

config DT_HAS_NXP_PXP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PXP))

DT_COMPAT_NXP_QDEC_S32 := nxp,qdec-s32

config DT_HAS_NXP_QDEC_S32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_QDEC_S32))

DT_COMPAT_NXP_QTMR_PWM := nxp,qtmr-pwm

config DT_HAS_NXP_QTMR_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_QTMR_PWM))

DT_COMPAT_NXP_RDC := nxp,rdc

config DT_HAS_NXP_RDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RDC))

DT_COMPAT_NXP_RSTCTL := nxp,rstctl

config DT_HAS_NXP_RSTCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RSTCTL))

DT_COMPAT_NXP_RT_IOCON_PINCTRL := nxp,rt-iocon-pinctrl

config DT_HAS_NXP_RT_IOCON_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RT_IOCON_PINCTRL))

DT_COMPAT_NXP_RTC := nxp,rtc

config DT_HAS_NXP_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RTC))

DT_COMPAT_NXP_RW_PMU := nxp,rw-pmu

config DT_HAS_NXP_RW_PMU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RW_PMU))

DT_COMPAT_NXP_RW_SOC_CTRL := nxp,rw-soc-ctrl

config DT_HAS_NXP_RW_SOC_CTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RW_SOC_CTRL))

DT_COMPAT_NXP_S32_ADC_SAR := nxp,s32-adc-sar

config DT_HAS_NXP_S32_ADC_SAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_ADC_SAR))

DT_COMPAT_NXP_S32_CANXL := nxp,s32-canxl

config DT_HAS_NXP_S32_CANXL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_CANXL))

DT_COMPAT_NXP_S32_CLOCK := nxp,s32-clock

config DT_HAS_NXP_S32_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_CLOCK))

DT_COMPAT_NXP_S32_EMIOS := nxp,s32-emios

config DT_HAS_NXP_S32_EMIOS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_EMIOS))

DT_COMPAT_NXP_S32_EMIOS_PWM := nxp,s32-emios-pwm

config DT_HAS_NXP_S32_EMIOS_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_EMIOS_PWM))

DT_COMPAT_NXP_S32_GMAC := nxp,s32-gmac

config DT_HAS_NXP_S32_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_GMAC))

DT_COMPAT_NXP_S32_GMAC_MDIO := nxp,s32-gmac-mdio

config DT_HAS_NXP_S32_GMAC_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_GMAC_MDIO))

DT_COMPAT_NXP_S32_GPIO := nxp,s32-gpio

config DT_HAS_NXP_S32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_GPIO))

DT_COMPAT_NXP_S32_LCU := nxp,s32-lcu

config DT_HAS_NXP_S32_LCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_LCU))

DT_COMPAT_NXP_S32_LINFLEXD := nxp,s32-linflexd

config DT_HAS_NXP_S32_LINFLEXD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_LINFLEXD))

DT_COMPAT_NXP_S32_MC_ME := nxp,s32-mc-me

config DT_HAS_NXP_S32_MC_ME_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_MC_ME))

DT_COMPAT_NXP_S32_MC_RGM := nxp,s32-mc-rgm

config DT_HAS_NXP_S32_MC_RGM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_MC_RGM))

DT_COMPAT_NXP_S32_MRU := nxp,s32-mru

config DT_HAS_NXP_S32_MRU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_MRU))

DT_COMPAT_NXP_S32_NETC_EMDIO := nxp,s32-netc-emdio

config DT_HAS_NXP_S32_NETC_EMDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_EMDIO))

DT_COMPAT_NXP_S32_NETC_PSI := nxp,s32-netc-psi

config DT_HAS_NXP_S32_NETC_PSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_PSI))

DT_COMPAT_NXP_S32_NETC_VSI := nxp,s32-netc-vsi

config DT_HAS_NXP_S32_NETC_VSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_VSI))

DT_COMPAT_NXP_S32_QSPI := nxp,s32-qspi

config DT_HAS_NXP_S32_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI))

DT_COMPAT_NXP_S32_QSPI_DEVICE := nxp,s32-qspi-device

config DT_HAS_NXP_S32_QSPI_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI_DEVICE))

DT_COMPAT_NXP_S32_QSPI_NOR := nxp,s32-qspi-nor

config DT_HAS_NXP_S32_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI_NOR))

DT_COMPAT_NXP_S32_SIUL2_EIRQ := nxp,s32-siul2-eirq

config DT_HAS_NXP_S32_SIUL2_EIRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SIUL2_EIRQ))

DT_COMPAT_NXP_S32_SPI := nxp,s32-spi

config DT_HAS_NXP_S32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SPI))

DT_COMPAT_NXP_S32_SWT := nxp,s32-swt

config DT_HAS_NXP_S32_SWT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SWT))

DT_COMPAT_NXP_S32_SYS_TIMER := nxp,s32-sys-timer

config DT_HAS_NXP_S32_SYS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SYS_TIMER))

DT_COMPAT_NXP_S32_TRGMUX := nxp,s32-trgmux

config DT_HAS_NXP_S32_TRGMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_TRGMUX))

DT_COMPAT_NXP_S32_WKPU := nxp,s32-wkpu

config DT_HAS_NXP_S32_WKPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_WKPU))

DT_COMPAT_NXP_S32K3_PINCTRL := nxp,s32k3-pinctrl

config DT_HAS_NXP_S32K3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32K3_PINCTRL))

DT_COMPAT_NXP_S32K3_PMC := nxp,s32k3-pmc

config DT_HAS_NXP_S32K3_PMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32K3_PMC))

DT_COMPAT_NXP_S32ZE_PINCTRL := nxp,s32ze-pinctrl

config DT_HAS_NXP_S32ZE_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32ZE_PINCTRL))

DT_COMPAT_NXP_SC18IM704 := nxp,sc18im704

config DT_HAS_NXP_SC18IM704_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704))

DT_COMPAT_NXP_SC18IM704_GPIO := nxp,sc18im704-gpio

config DT_HAS_NXP_SC18IM704_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704_GPIO))

DT_COMPAT_NXP_SC18IM704_I2C := nxp,sc18im704-i2c

config DT_HAS_NXP_SC18IM704_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704_I2C))

DT_COMPAT_NXP_SCG_K4 := nxp,scg-k4

config DT_HAS_NXP_SCG_K4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SCG_K4))

DT_COMPAT_NXP_SCTIMER_PWM := nxp,sctimer-pwm

config DT_HAS_NXP_SCTIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SCTIMER_PWM))

DT_COMPAT_NXP_SCU_PD := nxp,scu-pd

config DT_HAS_NXP_SCU_PD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SCU_PD))

DT_COMPAT_NXP_SDMA := nxp,sdma

config DT_HAS_NXP_SDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SDMA))

DT_COMPAT_NXP_SMARTDMA := nxp,smartdma

config DT_HAS_NXP_SMARTDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SMARTDMA))

DT_COMPAT_NXP_SOF_HOST_DMA := nxp,sof-host-dma

config DT_HAS_NXP_SOF_HOST_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SOF_HOST_DMA))

DT_COMPAT_NXP_SYSMPU := nxp,sysmpu

config DT_HAS_NXP_SYSMPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SYSMPU))

DT_COMPAT_NXP_TEMPMON := nxp,tempmon

config DT_HAS_NXP_TEMPMON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_TEMPMON))

DT_COMPAT_NXP_TJA1103 := nxp,tja1103

config DT_HAS_NXP_TJA1103_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_TJA1103))

DT_COMPAT_NXP_TPM_TIMER := nxp,tpm-timer

config DT_HAS_NXP_TPM_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_TPM_TIMER))

DT_COMPAT_NXP_USBPHY := nxp,usbphy

config DT_HAS_NXP_USBPHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_USBPHY))

DT_COMPAT_NXP_VF610_ADC := nxp,vf610-adc

config DT_HAS_NXP_VF610_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_VF610_ADC))

DT_COMPAT_NXP_VIDEO_SMARTDMA := nxp,video-smartdma

config DT_HAS_NXP_VIDEO_SMARTDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_VIDEO_SMARTDMA))

DT_COMPAT_NXP_VREF := nxp,vref

config DT_HAS_NXP_VREF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_VREF))

DT_COMPAT_NXP_WDOG32 := nxp,wdog32

config DT_HAS_NXP_WDOG32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_WDOG32))

DT_COMPAT_NXP_WIFI := nxp,wifi

config DT_HAS_NXP_WIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_WIFI))

DT_COMPAT_ONNN_NCP5623 := onnn,ncp5623

config DT_HAS_ONNN_NCP5623_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ONNN_NCP5623))

DT_COMPAT_ONNN_NCT75 := onnn,nct75

config DT_HAS_ONNN_NCT75_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ONNN_NCT75))

DT_COMPAT_OPENCORES_SPI_SIMPLE := opencores,spi-simple

config DT_HAS_OPENCORES_SPI_SIMPLE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENCORES_SPI_SIMPLE))

DT_COMPAT_OPENISA_RI5CY := openisa,ri5cy

config DT_HAS_OPENISA_RI5CY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RI5CY))

DT_COMPAT_OPENISA_RV32M1_EVENT_UNIT := openisa,rv32m1-event-unit

config DT_HAS_OPENISA_RV32M1_EVENT_UNIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_EVENT_UNIT))

DT_COMPAT_OPENISA_RV32M1_FTFE := openisa,rv32m1-ftfe

config DT_HAS_OPENISA_RV32M1_FTFE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_FTFE))

DT_COMPAT_OPENISA_RV32M1_GENFSK := openisa,rv32m1-genfsk

config DT_HAS_OPENISA_RV32M1_GENFSK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_GENFSK))

DT_COMPAT_OPENISA_RV32M1_GPIO := openisa,rv32m1-gpio

config DT_HAS_OPENISA_RV32M1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_GPIO))

DT_COMPAT_OPENISA_RV32M1_INTMUX := openisa,rv32m1-intmux

config DT_HAS_OPENISA_RV32M1_INTMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_INTMUX))

DT_COMPAT_OPENISA_RV32M1_INTMUX_CH := openisa,rv32m1-intmux-ch

config DT_HAS_OPENISA_RV32M1_INTMUX_CH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_INTMUX_CH))

DT_COMPAT_OPENISA_RV32M1_LPI2C := openisa,rv32m1-lpi2c

config DT_HAS_OPENISA_RV32M1_LPI2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPI2C))

DT_COMPAT_OPENISA_RV32M1_LPSPI := openisa,rv32m1-lpspi

config DT_HAS_OPENISA_RV32M1_LPSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPSPI))

DT_COMPAT_OPENISA_RV32M1_LPTMR := openisa,rv32m1-lptmr

config DT_HAS_OPENISA_RV32M1_LPTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPTMR))

DT_COMPAT_OPENISA_RV32M1_LPUART := openisa,rv32m1-lpuart

config DT_HAS_OPENISA_RV32M1_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPUART))

DT_COMPAT_OPENISA_RV32M1_PCC := openisa,rv32m1-pcc

config DT_HAS_OPENISA_RV32M1_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PCC))

DT_COMPAT_OPENISA_RV32M1_PINCTRL := openisa,rv32m1-pinctrl

config DT_HAS_OPENISA_RV32M1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PINCTRL))

DT_COMPAT_OPENISA_RV32M1_PINMUX := openisa,rv32m1-pinmux

config DT_HAS_OPENISA_RV32M1_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PINMUX))

DT_COMPAT_OPENISA_RV32M1_TPM := openisa,rv32m1-tpm

config DT_HAS_OPENISA_RV32M1_TPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_TPM))

DT_COMPAT_OPENISA_RV32M1_TRNG := openisa,rv32m1-trng

config DT_HAS_OPENISA_RV32M1_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_TRNG))

DT_COMPAT_OPENISA_ZERO_RI5CY := openisa,zero-ri5cy

config DT_HAS_OPENISA_ZERO_RI5CY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_ZERO_RI5CY))

DT_COMPAT_OPENTHREAD_CONFIG := openthread,config

config DT_HAS_OPENTHREAD_CONFIG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENTHREAD_CONFIG))

DT_COMPAT_ORISETECH_OTM8009A := orisetech,otm8009a

config DT_HAS_ORISETECH_OTM8009A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ORISETECH_OTM8009A))

DT_COMPAT_OVTI_OV2640 := ovti,ov2640

config DT_HAS_OVTI_OV2640_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV2640))

DT_COMPAT_OVTI_OV5640 := ovti,ov5640

config DT_HAS_OVTI_OV5640_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV5640))

DT_COMPAT_OVTI_OV7670 := ovti,ov7670

config DT_HAS_OVTI_OV7670_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV7670))

DT_COMPAT_OVTI_OV7725 := ovti,ov7725

config DT_HAS_OVTI_OV7725_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV7725))

DT_COMPAT_PANASONIC_AMG88XX := panasonic,amg88xx

config DT_HAS_PANASONIC_AMG88XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PANASONIC_AMG88XX))

DT_COMPAT_PANASONIC_REDUCED_ARDUINO_HEADER := panasonic,reduced-arduino-header

config DT_HAS_PANASONIC_REDUCED_ARDUINO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PANASONIC_REDUCED_ARDUINO_HEADER))

DT_COMPAT_PARADE_PS8XXX := parade,ps8xxx

config DT_HAS_PARADE_PS8XXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PARADE_PS8XXX))

DT_COMPAT_PARTICLE_GEN3_HEADER := particle-gen3-header

config DT_HAS_PARTICLE_GEN3_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PARTICLE_GEN3_HEADER))

DT_COMPAT_PCI_HOST_ECAM_GENERIC := pci-host-ecam-generic

config DT_HAS_PCI_HOST_ECAM_GENERIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PCI_HOST_ECAM_GENERIC))

DT_COMPAT_PCIE_CONTROLLER := pcie-controller

config DT_HAS_PCIE_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PCIE_CONTROLLER))

DT_COMPAT_PHOSENSE_XBR818 := phosense,xbr818

config DT_HAS_PHOSENSE_XBR818_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PHOSENSE_XBR818))

DT_COMPAT_PIXART_PAT912X := pixart,pat912x

config DT_HAS_PIXART_PAT912X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PAT912X))

DT_COMPAT_PIXART_PAW3212 := pixart,paw3212

config DT_HAS_PIXART_PAW3212_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PAW3212))

DT_COMPAT_PIXART_PAW32XX := pixart,paw32xx

config DT_HAS_PIXART_PAW32XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PAW32XX))

DT_COMPAT_PIXART_PMW3360 := pixart,pmw3360

config DT_HAS_PIXART_PMW3360_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PMW3360))

DT_COMPAT_PIXART_PMW3610 := pixart,pmw3610

config DT_HAS_PIXART_PMW3610_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PMW3610))

DT_COMPAT_PLANTOWER_PMS7003 := plantower,pms7003

config DT_HAS_PLANTOWER_PMS7003_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PLANTOWER_PMS7003))

DT_COMPAT_POWER_DOMAIN := power-domain

config DT_HAS_POWER_DOMAIN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_POWER_DOMAIN))

DT_COMPAT_POWER_DOMAIN_GPIO := power-domain-gpio

config DT_HAS_POWER_DOMAIN_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_POWER_DOMAIN_GPIO))

DT_COMPAT_POWER_DOMAIN_GPIO_MONITOR := power-domain-gpio-monitor

config DT_HAS_POWER_DOMAIN_GPIO_MONITOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_POWER_DOMAIN_GPIO_MONITOR))

DT_COMPAT_PTC_PT6314 := ptc,pt6314

config DT_HAS_PTC_PT6314_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PTC_PT6314))

DT_COMPAT_PWM_CLOCK := pwm-clock

config DT_HAS_PWM_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PWM_CLOCK))

DT_COMPAT_PWM_LEDS := pwm-leds

config DT_HAS_PWM_LEDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PWM_LEDS))

DT_COMPAT_QCA_AR8031 := qca,ar8031

config DT_HAS_QCA_AR8031_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QCA_AR8031))

DT_COMPAT_QEMU_IVSHMEM := qemu,ivshmem

config DT_HAS_QEMU_IVSHMEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QEMU_IVSHMEM))

DT_COMPAT_QEMU_NIOS2_ZEPHYR := qemu,nios2-zephyr

config DT_HAS_QEMU_NIOS2_ZEPHYR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QEMU_NIOS2_ZEPHYR))

DT_COMPAT_QEMU_RISCV_VIRT := qemu,riscv-virt

config DT_HAS_QEMU_RISCV_VIRT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QEMU_RISCV_VIRT))

DT_COMPAT_QUECTEL_BG95 := quectel,bg95

config DT_HAS_QUECTEL_BG95_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_BG95))

DT_COMPAT_QUECTEL_BG9X := quectel,bg9x

config DT_HAS_QUECTEL_BG9X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_BG9X))

DT_COMPAT_QUECTEL_EG25_G := quectel,eg25-g

config DT_HAS_QUECTEL_EG25_G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_EG25_G))

DT_COMPAT_QUECTEL_LC26G := quectel,lc26g

config DT_HAS_QUECTEL_LC26G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_LC26G))

DT_COMPAT_QUECTEL_LC76G := quectel,lc76g

config DT_HAS_QUECTEL_LC76G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_LC76G))

DT_COMPAT_QUECTEL_LC86G := quectel,lc86g

config DT_HAS_QUECTEL_LC86G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_LC86G))

DT_COMPAT_QUICKLOGIC_EOS_S3_GPIO := quicklogic,eos-s3-gpio

config DT_HAS_QUICKLOGIC_EOS_S3_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_EOS_S3_GPIO))

DT_COMPAT_QUICKLOGIC_EOS_S3_PINCTRL := quicklogic,eos-s3-pinctrl

config DT_HAS_QUICKLOGIC_EOS_S3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_EOS_S3_PINCTRL))

DT_COMPAT_QUICKLOGIC_USBSERIALPORT_S3B := quicklogic,usbserialport-s3b

config DT_HAS_QUICKLOGIC_USBSERIALPORT_S3B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_USBSERIALPORT_S3B))

DT_COMPAT_RASPBERRYPI_CORE_SUPPLY_REGULATOR := raspberrypi,core-supply-regulator

config DT_HAS_RASPBERRYPI_CORE_SUPPLY_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_CORE_SUPPLY_REGULATOR))

DT_COMPAT_RASPBERRYPI_PICO_ADC := raspberrypi,pico-adc

config DT_HAS_RASPBERRYPI_PICO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_ADC))

DT_COMPAT_RASPBERRYPI_PICO_CLOCK := raspberrypi,pico-clock

config DT_HAS_RASPBERRYPI_PICO_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_CLOCK))

DT_COMPAT_RASPBERRYPI_PICO_CLOCK_CONTROLLER := raspberrypi,pico-clock-controller

config DT_HAS_RASPBERRYPI_PICO_CLOCK_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_CLOCK_CONTROLLER))

DT_COMPAT_RASPBERRYPI_PICO_DMA := raspberrypi,pico-dma

config DT_HAS_RASPBERRYPI_PICO_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_DMA))

DT_COMPAT_RASPBERRYPI_PICO_FLASH_CONTROLLER := raspberrypi,pico-flash-controller

config DT_HAS_RASPBERRYPI_PICO_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_FLASH_CONTROLLER))

DT_COMPAT_RASPBERRYPI_PICO_GPIO := raspberrypi,pico-gpio

config DT_HAS_RASPBERRYPI_PICO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_GPIO))

DT_COMPAT_RASPBERRYPI_PICO_HEADER := raspberrypi,pico-header

config DT_HAS_RASPBERRYPI_PICO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_HEADER))

DT_COMPAT_RASPBERRYPI_PICO_I2C := raspberrypi,pico-i2c

config DT_HAS_RASPBERRYPI_PICO_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_I2C))

DT_COMPAT_RASPBERRYPI_PICO_PINCTRL := raspberrypi,pico-pinctrl

config DT_HAS_RASPBERRYPI_PICO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PINCTRL))

DT_COMPAT_RASPBERRYPI_PICO_PIO := raspberrypi,pico-pio

config DT_HAS_RASPBERRYPI_PICO_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PIO))

DT_COMPAT_RASPBERRYPI_PICO_PIO_DEVICE := raspberrypi,pico-pio-device

config DT_HAS_RASPBERRYPI_PICO_PIO_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PIO_DEVICE))

DT_COMPAT_RASPBERRYPI_PICO_PLL := raspberrypi,pico-pll

config DT_HAS_RASPBERRYPI_PICO_PLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PLL))

DT_COMPAT_RASPBERRYPI_PICO_PWM := raspberrypi,pico-pwm

config DT_HAS_RASPBERRYPI_PICO_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PWM))

DT_COMPAT_RASPBERRYPI_PICO_RESET := raspberrypi,pico-reset

config DT_HAS_RASPBERRYPI_PICO_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_RESET))

DT_COMPAT_RASPBERRYPI_PICO_ROSC := raspberrypi,pico-rosc

config DT_HAS_RASPBERRYPI_PICO_ROSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_ROSC))

DT_COMPAT_RASPBERRYPI_PICO_RTC := raspberrypi,pico-rtc

config DT_HAS_RASPBERRYPI_PICO_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_RTC))

DT_COMPAT_RASPBERRYPI_PICO_SPI := raspberrypi,pico-spi

config DT_HAS_RASPBERRYPI_PICO_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_SPI))

DT_COMPAT_RASPBERRYPI_PICO_SPI_PIO := raspberrypi,pico-spi-pio

config DT_HAS_RASPBERRYPI_PICO_SPI_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_SPI_PIO))

DT_COMPAT_RASPBERRYPI_PICO_TEMP := raspberrypi,pico-temp

config DT_HAS_RASPBERRYPI_PICO_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_TEMP))

DT_COMPAT_RASPBERRYPI_PICO_TIMER := raspberrypi,pico-timer

config DT_HAS_RASPBERRYPI_PICO_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_TIMER))

DT_COMPAT_RASPBERRYPI_PICO_UART := raspberrypi,pico-uart

config DT_HAS_RASPBERRYPI_PICO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_UART))

DT_COMPAT_RASPBERRYPI_PICO_UART_PIO := raspberrypi,pico-uart-pio

config DT_HAS_RASPBERRYPI_PICO_UART_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_UART_PIO))

DT_COMPAT_RASPBERRYPI_PICO_USBD := raspberrypi,pico-usbd

config DT_HAS_RASPBERRYPI_PICO_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_USBD))

DT_COMPAT_RASPBERRYPI_PICO_WATCHDOG := raspberrypi,pico-watchdog

config DT_HAS_RASPBERRYPI_PICO_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_WATCHDOG))

DT_COMPAT_RASPBERRYPI_PICO_XOSC := raspberrypi,pico-xosc

config DT_HAS_RASPBERRYPI_PICO_XOSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_XOSC))

DT_COMPAT_RASPBERRYPI_RP1_GPIO := raspberrypi,rp1-gpio

config DT_HAS_RASPBERRYPI_RP1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_RP1_GPIO))

DT_COMPAT_RASPBERRYPI_40PINS_HEADER := raspberrypi-40pins-header

config DT_HAS_RASPBERRYPI_40PINS_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_40PINS_HEADER))

DT_COMPAT_RAYDIUM_RM67162 := raydium,rm67162

config DT_HAS_RAYDIUM_RM67162_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RAYDIUM_RM67162))

DT_COMPAT_RAYDIUM_RM68200 := raydium,rm68200

config DT_HAS_RAYDIUM_RM68200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RAYDIUM_RM68200))

DT_COMPAT_REALTEK_RTL8211F := realtek,rtl8211f

config DT_HAS_REALTEK_RTL8211F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTL8211F))

DT_COMPAT_REALTEK_RTS5912_GPIO := realtek,rts5912-gpio

config DT_HAS_REALTEK_RTS5912_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_GPIO))

DT_COMPAT_REALTEK_RTS5912_PINCTRL := realtek,rts5912-pinctrl

config DT_HAS_REALTEK_RTS5912_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_PINCTRL))

DT_COMPAT_REALTEK_RTS5912_RTMR := realtek,rts5912-rtmr

config DT_HAS_REALTEK_RTS5912_RTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_RTMR))

DT_COMPAT_REALTEK_RTS5912_SCCON := realtek,rts5912-sccon

config DT_HAS_REALTEK_RTS5912_SCCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_SCCON))

DT_COMPAT_REALTEK_RTS5912_SLWTIMER := realtek,rts5912-slwtimer

config DT_HAS_REALTEK_RTS5912_SLWTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_SLWTIMER))

DT_COMPAT_REALTEK_RTS5912_UART := realtek,rts5912-uart

config DT_HAS_REALTEK_RTS5912_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REALTEK_RTS5912_UART))

DT_COMPAT_REGULATOR_FIXED := regulator-fixed

config DT_HAS_REGULATOR_FIXED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REGULATOR_FIXED))

DT_COMPAT_REGULATOR_GPIO := regulator-gpio

config DT_HAS_REGULATOR_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REGULATOR_GPIO))

DT_COMPAT_RENESAS_BT_HCI_DA1453X := renesas,bt-hci-da1453x

config DT_HAS_RENESAS_BT_HCI_DA1453X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_BT_HCI_DA1453X))

DT_COMPAT_RENESAS_BT_HCI_DA1469X := renesas,bt-hci-da1469x

config DT_HAS_RENESAS_BT_HCI_DA1469X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_BT_HCI_DA1469X))

DT_COMPAT_RENESAS_HS300X := renesas,hs300x

config DT_HAS_RENESAS_HS300X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_HS300X))

DT_COMPAT_RENESAS_HS400X := renesas,hs400x

config DT_HAS_RENESAS_HS400X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_HS400X))

DT_COMPAT_RENESAS_PWM_RCAR := renesas,pwm-rcar

config DT_HAS_RENESAS_PWM_RCAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_PWM_RCAR))

DT_COMPAT_RENESAS_R8A7795_CPG_MSSR := renesas,r8a7795-cpg-mssr

config DT_HAS_RENESAS_R8A7795_CPG_MSSR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_R8A7795_CPG_MSSR))

DT_COMPAT_RENESAS_R8A779F0_CPG_MSSR := renesas,r8a779f0-cpg-mssr

config DT_HAS_RENESAS_R8A779F0_CPG_MSSR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_R8A779F0_CPG_MSSR))

DT_COMPAT_RENESAS_RA_ADC := renesas,ra-adc

config DT_HAS_RENESAS_RA_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_ADC))

DT_COMPAT_RENESAS_RA_AGT := renesas,ra-agt

config DT_HAS_RENESAS_RA_AGT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_AGT))

DT_COMPAT_RENESAS_RA_AGT_COUNTER := renesas,ra-agt-counter

config DT_HAS_RENESAS_RA_AGT_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_AGT_COUNTER))

DT_COMPAT_RENESAS_RA_CANFD := renesas,ra-canfd

config DT_HAS_RENESAS_RA_CANFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CANFD))

DT_COMPAT_RENESAS_RA_CANFD_GLOBAL := renesas,ra-canfd-global

config DT_HAS_RENESAS_RA_CANFD_GLOBAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CANFD_GLOBAL))

DT_COMPAT_RENESAS_RA_CGC_BUSCLK := renesas,ra-cgc-busclk

config DT_HAS_RENESAS_RA_CGC_BUSCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_BUSCLK))

DT_COMPAT_RENESAS_RA_CGC_EXTERNAL_CLOCK := renesas,ra-cgc-external-clock

config DT_HAS_RENESAS_RA_CGC_EXTERNAL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_EXTERNAL_CLOCK))

DT_COMPAT_RENESAS_RA_CGC_PCLK := renesas,ra-cgc-pclk

config DT_HAS_RENESAS_RA_CGC_PCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_PCLK))

DT_COMPAT_RENESAS_RA_CGC_PCLK_BLOCK := renesas,ra-cgc-pclk-block

config DT_HAS_RENESAS_RA_CGC_PCLK_BLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_PCLK_BLOCK))

DT_COMPAT_RENESAS_RA_CGC_PLL := renesas,ra-cgc-pll

config DT_HAS_RENESAS_RA_CGC_PLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_PLL))

DT_COMPAT_RENESAS_RA_CGC_PLL_OUT := renesas,ra-cgc-pll-out

config DT_HAS_RENESAS_RA_CGC_PLL_OUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_PLL_OUT))

DT_COMPAT_RENESAS_RA_CGC_SUBCLK := renesas,ra-cgc-subclk

config DT_HAS_RENESAS_RA_CGC_SUBCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_CGC_SUBCLK))

DT_COMPAT_RENESAS_RA_ETHERNET := renesas,ra-ethernet

config DT_HAS_RENESAS_RA_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_ETHERNET))

DT_COMPAT_RENESAS_RA_EXTERNAL_INTERRUPT := renesas,ra-external-interrupt

config DT_HAS_RENESAS_RA_EXTERNAL_INTERRUPT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_EXTERNAL_INTERRUPT))

DT_COMPAT_RENESAS_RA_FLASH_HP_CONTROLLER := renesas,ra-flash-hp-controller

config DT_HAS_RENESAS_RA_FLASH_HP_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_FLASH_HP_CONTROLLER))

DT_COMPAT_RENESAS_RA_GLCDC := renesas,ra-glcdc

config DT_HAS_RENESAS_RA_GLCDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_GLCDC))

DT_COMPAT_RENESAS_RA_GPIO := renesas,ra-gpio

config DT_HAS_RENESAS_RA_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_GPIO))

DT_COMPAT_RENESAS_RA_GPIO_IOPORT := renesas,ra-gpio-ioport

config DT_HAS_RENESAS_RA_GPIO_IOPORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_GPIO_IOPORT))

DT_COMPAT_RENESAS_RA_GPIO_MIPI_HEADER := renesas,ra-gpio-mipi-header

config DT_HAS_RENESAS_RA_GPIO_MIPI_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_GPIO_MIPI_HEADER))

DT_COMPAT_RENESAS_RA_IIC := renesas,ra-iic

config DT_HAS_RENESAS_RA_IIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_IIC))

DT_COMPAT_RENESAS_RA_INTERRUPT_CONTROLLER_UNIT := renesas,ra-interrupt-controller-unit

config DT_HAS_RENESAS_RA_INTERRUPT_CONTROLLER_UNIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_INTERRUPT_CONTROLLER_UNIT))

DT_COMPAT_RENESAS_RA_MDIO := renesas,ra-mdio

config DT_HAS_RENESAS_RA_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_MDIO))

DT_COMPAT_RENESAS_RA_MIPI_DSI := renesas,ra-mipi-dsi

config DT_HAS_RENESAS_RA_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_MIPI_DSI))

DT_COMPAT_RENESAS_RA_NV_FLASH := renesas,ra-nv-flash

config DT_HAS_RENESAS_RA_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_NV_FLASH))

DT_COMPAT_RENESAS_RA_PINCTRL_PFS := renesas,ra-pinctrl-pfs

config DT_HAS_RENESAS_RA_PINCTRL_PFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_PINCTRL_PFS))

DT_COMPAT_RENESAS_RA_PWM := renesas,ra-pwm

config DT_HAS_RENESAS_RA_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_PWM))

DT_COMPAT_RENESAS_RA_RSIP_E51A_TRNG := renesas,ra-rsip-e51a-trng

config DT_HAS_RENESAS_RA_RSIP_E51A_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_RSIP_E51A_TRNG))

DT_COMPAT_RENESAS_RA_SCE5_RNG := renesas,ra-sce5-rng

config DT_HAS_RENESAS_RA_SCE5_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SCE5_RNG))

DT_COMPAT_RENESAS_RA_SCE7_RNG := renesas,ra-sce7-rng

config DT_HAS_RENESAS_RA_SCE7_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SCE7_RNG))

DT_COMPAT_RENESAS_RA_SCI := renesas,ra-sci

config DT_HAS_RENESAS_RA_SCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SCI))

DT_COMPAT_RENESAS_RA_SCI_UART := renesas,ra-sci-uart

config DT_HAS_RENESAS_RA_SCI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SCI_UART))

DT_COMPAT_RENESAS_RA_SDHC := renesas,ra-sdhc

config DT_HAS_RENESAS_RA_SDHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SDHC))

DT_COMPAT_RENESAS_RA_SDRAM := renesas,ra-sdram

config DT_HAS_RENESAS_RA_SDRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SDRAM))

DT_COMPAT_RENESAS_RA_SPI := renesas,ra-spi

config DT_HAS_RENESAS_RA_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_SPI))

DT_COMPAT_RENESAS_RA_TRNG := renesas,ra-trng

config DT_HAS_RENESAS_RA_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_TRNG))

DT_COMPAT_RENESAS_RA_UART_SCI := renesas,ra-uart-sci

config DT_HAS_RENESAS_RA_UART_SCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_UART_SCI))

DT_COMPAT_RENESAS_RA_UDC := renesas,ra-udc

config DT_HAS_RENESAS_RA_UDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_UDC))

DT_COMPAT_RENESAS_RA_USB := renesas,ra-usb

config DT_HAS_RENESAS_RA_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_USB))

DT_COMPAT_RENESAS_RA_USBPHYC := renesas,ra-usbphyc

config DT_HAS_RENESAS_RA_USBPHYC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA_USBPHYC))

DT_COMPAT_RENESAS_RA8_SPI_B := renesas,ra8-spi-b

config DT_HAS_RENESAS_RA8_SPI_B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA8_SPI_B))

DT_COMPAT_RENESAS_RA8_UART_SCI_B := renesas,ra8-uart-sci-b

config DT_HAS_RENESAS_RA8_UART_SCI_B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RA8_UART_SCI_B))

DT_COMPAT_RENESAS_RCAR_CAN := renesas,rcar-can

config DT_HAS_RENESAS_RCAR_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_CAN))

DT_COMPAT_RENESAS_RCAR_CMT := renesas,rcar-cmt

config DT_HAS_RENESAS_RCAR_CMT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_CMT))

DT_COMPAT_RENESAS_RCAR_GPIO := renesas,rcar-gpio

config DT_HAS_RENESAS_RCAR_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_GPIO))

DT_COMPAT_RENESAS_RCAR_HSCIF := renesas,rcar-hscif

config DT_HAS_RENESAS_RCAR_HSCIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_HSCIF))

DT_COMPAT_RENESAS_RCAR_I2C := renesas,rcar-i2c

config DT_HAS_RENESAS_RCAR_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_I2C))

DT_COMPAT_RENESAS_RCAR_MMC := renesas,rcar-mmc

config DT_HAS_RENESAS_RCAR_MMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_MMC))

DT_COMPAT_RENESAS_RCAR_PFC := renesas,rcar-pfc

config DT_HAS_RENESAS_RCAR_PFC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_PFC))

DT_COMPAT_RENESAS_RCAR_SCIF := renesas,rcar-scif

config DT_HAS_RENESAS_RCAR_SCIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_SCIF))

DT_COMPAT_RENESAS_RZ_GPIO := renesas,rz-gpio

config DT_HAS_RENESAS_RZ_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZ_GPIO))

DT_COMPAT_RENESAS_RZ_GPIO_INT := renesas,rz-gpio-int

config DT_HAS_RENESAS_RZ_GPIO_INT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZ_GPIO_INT))

DT_COMPAT_RENESAS_RZ_SCIF_UART := renesas,rz-scif-uart

config DT_HAS_RENESAS_RZ_SCIF_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZ_SCIF_UART))

DT_COMPAT_RENESAS_RZG_PINCTRL := renesas,rzg-pinctrl

config DT_HAS_RENESAS_RZG_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZG_PINCTRL))

DT_COMPAT_RENESAS_RZT2M_GPIO := renesas,rzt2m-gpio

config DT_HAS_RENESAS_RZT2M_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZT2M_GPIO))

DT_COMPAT_RENESAS_RZT2M_GPIO_COMMON := renesas,rzt2m-gpio-common

config DT_HAS_RENESAS_RZT2M_GPIO_COMMON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZT2M_GPIO_COMMON))

DT_COMPAT_RENESAS_RZT2M_PINCTRL := renesas,rzt2m-pinctrl

config DT_HAS_RENESAS_RZT2M_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZT2M_PINCTRL))

DT_COMPAT_RENESAS_RZT2M_UART := renesas,rzt2m-uart

config DT_HAS_RENESAS_RZT2M_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RZT2M_UART))

DT_COMPAT_RENESAS_SLG47105 := renesas,slg47105

config DT_HAS_RENESAS_SLG47105_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SLG47105))

DT_COMPAT_RENESAS_SLG47115 := renesas,slg47115

config DT_HAS_RENESAS_SLG47115_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SLG47115))

DT_COMPAT_RENESAS_SLG471X5 := renesas,slg471x5

config DT_HAS_RENESAS_SLG471X5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SLG471X5))

DT_COMPAT_RENESAS_SMARTBOND_ADC := renesas,smartbond-adc

config DT_HAS_RENESAS_SMARTBOND_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_ADC))

DT_COMPAT_RENESAS_SMARTBOND_CRYPTO := renesas,smartbond-crypto

config DT_HAS_RENESAS_SMARTBOND_CRYPTO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_CRYPTO))

DT_COMPAT_RENESAS_SMARTBOND_DISPLAY := renesas,smartbond-display

config DT_HAS_RENESAS_SMARTBOND_DISPLAY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_DISPLAY))

DT_COMPAT_RENESAS_SMARTBOND_DMA := renesas,smartbond-dma

config DT_HAS_RENESAS_SMARTBOND_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_DMA))

DT_COMPAT_RENESAS_SMARTBOND_FLASH_CONTROLLER := renesas,smartbond-flash-controller

config DT_HAS_RENESAS_SMARTBOND_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_FLASH_CONTROLLER))

DT_COMPAT_RENESAS_SMARTBOND_GPIO := renesas,smartbond-gpio

config DT_HAS_RENESAS_SMARTBOND_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_GPIO))

DT_COMPAT_RENESAS_SMARTBOND_I2C := renesas,smartbond-i2c

config DT_HAS_RENESAS_SMARTBOND_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_I2C))

DT_COMPAT_RENESAS_SMARTBOND_LP_CLK := renesas,smartbond-lp-clk

config DT_HAS_RENESAS_SMARTBOND_LP_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_LP_CLK))

DT_COMPAT_RENESAS_SMARTBOND_LP_OSC := renesas,smartbond-lp-osc

config DT_HAS_RENESAS_SMARTBOND_LP_OSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_LP_OSC))

DT_COMPAT_RENESAS_SMARTBOND_MIPI_DBI := renesas,smartbond-mipi-dbi

config DT_HAS_RENESAS_SMARTBOND_MIPI_DBI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_MIPI_DBI))

DT_COMPAT_RENESAS_SMARTBOND_NOR_PSRAM := renesas,smartbond-nor-psram

config DT_HAS_RENESAS_SMARTBOND_NOR_PSRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_NOR_PSRAM))

DT_COMPAT_RENESAS_SMARTBOND_PINCTRL := renesas,smartbond-pinctrl

config DT_HAS_RENESAS_SMARTBOND_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_PINCTRL))

DT_COMPAT_RENESAS_SMARTBOND_REGULATOR := renesas,smartbond-regulator

config DT_HAS_RENESAS_SMARTBOND_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_REGULATOR))

DT_COMPAT_RENESAS_SMARTBOND_RTC := renesas,smartbond-rtc

config DT_HAS_RENESAS_SMARTBOND_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_RTC))

DT_COMPAT_RENESAS_SMARTBOND_SDADC := renesas,smartbond-sdadc

config DT_HAS_RENESAS_SMARTBOND_SDADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SDADC))

DT_COMPAT_RENESAS_SMARTBOND_SPI := renesas,smartbond-spi

config DT_HAS_RENESAS_SMARTBOND_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SPI))

DT_COMPAT_RENESAS_SMARTBOND_SYS_CLK := renesas,smartbond-sys-clk

config DT_HAS_RENESAS_SMARTBOND_SYS_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SYS_CLK))

DT_COMPAT_RENESAS_SMARTBOND_TIMER := renesas,smartbond-timer

config DT_HAS_RENESAS_SMARTBOND_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_TIMER))

DT_COMPAT_RENESAS_SMARTBOND_TRNG := renesas,smartbond-trng

config DT_HAS_RENESAS_SMARTBOND_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_TRNG))

DT_COMPAT_RENESAS_SMARTBOND_UART := renesas,smartbond-uart

config DT_HAS_RENESAS_SMARTBOND_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_UART))

DT_COMPAT_RENESAS_SMARTBOND_USBD := renesas,smartbond-usbd

config DT_HAS_RENESAS_SMARTBOND_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_USBD))

DT_COMPAT_RENESAS_SMARTBOND_WATCHDOG := renesas,smartbond-watchdog

config DT_HAS_RENESAS_SMARTBOND_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_WATCHDOG))

DT_COMPAT_REYAX_RYLRXXX := reyax,rylrxxx

config DT_HAS_REYAX_RYLRXXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REYAX_RYLRXXX))

DT_COMPAT_RICHTEK_RT1718S := richtek,rt1718s

config DT_HAS_RICHTEK_RT1718S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RICHTEK_RT1718S))

DT_COMPAT_RICHTEK_RT1718S_GPIO_PORT := richtek,rt1718s-gpio-port

config DT_HAS_RICHTEK_RT1718S_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RICHTEK_RT1718S_GPIO_PORT))

DT_COMPAT_RISCV_CPU_INTC := riscv,cpu-intc

config DT_HAS_RISCV_CPU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RISCV_CPU_INTC))

DT_COMPAT_ROCKTECH_RK043FN02H_CT := rocktech,rk043fn02h-ct

config DT_HAS_ROCKTECH_RK043FN02H_CT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROCKTECH_RK043FN02H_CT))

DT_COMPAT_ROHM_BD8LB600FS := rohm,bd8lb600fs

config DT_HAS_ROHM_BD8LB600FS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BD8LB600FS))

DT_COMPAT_ROHM_BD8LB600FS_DIAGNOSTICS := rohm,bd8lb600fs-diagnostics

config DT_HAS_ROHM_BD8LB600FS_DIAGNOSTICS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BD8LB600FS_DIAGNOSTICS))

DT_COMPAT_ROHM_BD8LB600FS_GPIO := rohm,bd8lb600fs-gpio

config DT_HAS_ROHM_BD8LB600FS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BD8LB600FS_GPIO))

DT_COMPAT_ROHM_BH1749 := rohm,bh1749

config DT_HAS_ROHM_BH1749_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BH1749))

DT_COMPAT_ROHM_BH1750 := rohm,bh1750

config DT_HAS_ROHM_BH1750_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BH1750))

DT_COMPAT_SAMPLE_CONTROLLER := sample_controller

config DT_HAS_SAMPLE_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SAMPLE_CONTROLLER))

DT_COMPAT_SBS_DEFAULT_SBS_GAUGE := sbs,default-sbs-gauge

config DT_HAS_SBS_DEFAULT_SBS_GAUGE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_DEFAULT_SBS_GAUGE))

DT_COMPAT_SBS_SBS_CHARGER := sbs,sbs-charger

config DT_HAS_SBS_SBS_CHARGER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_SBS_CHARGER))

DT_COMPAT_SBS_SBS_GAUGE := sbs,sbs-gauge

config DT_HAS_SBS_SBS_GAUGE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_SBS_GAUGE))

DT_COMPAT_SBS_SBS_GAUGE_NEW_API := sbs,sbs-gauge-new-api

config DT_HAS_SBS_SBS_GAUGE_NEW_API_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_SBS_GAUGE_NEW_API))

DT_COMPAT_SCIOSENSE_ENS160 := sciosense,ens160

config DT_HAS_SCIOSENSE_ENS160_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SCIOSENSE_ENS160))

DT_COMPAT_SDC_RADIO_COEX_ONE_WIRE := sdc-radio-coex-one-wire

config DT_HAS_SDC_RADIO_COEX_ONE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SDC_RADIO_COEX_ONE_WIRE))

DT_COMPAT_SEEED_GROVE_LCD_RGB := seeed,grove-lcd-rgb

config DT_HAS_SEEED_GROVE_LCD_RGB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_LCD_RGB))

DT_COMPAT_SEEED_GROVE_LIGHT := seeed,grove-light

config DT_HAS_SEEED_GROVE_LIGHT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_LIGHT))

DT_COMPAT_SEEED_GROVE_TEMPERATURE := seeed,grove-temperature

config DT_HAS_SEEED_GROVE_TEMPERATURE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_TEMPERATURE))

DT_COMPAT_SEEED_HM330X := seeed,hm330x

config DT_HAS_SEEED_HM330X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_HM330X))

DT_COMPAT_SEEED_XIAO_GPIO := seeed,xiao-gpio

config DT_HAS_SEEED_XIAO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_XIAO_GPIO))

DT_COMPAT_SEGGER_ETH_RTT := segger,eth-rtt

config DT_HAS_SEGGER_ETH_RTT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEGGER_ETH_RTT))

DT_COMPAT_SEGGER_RTT_UART := segger,rtt-uart

config DT_HAS_SEGGER_RTT_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEGGER_RTT_UART))

DT_COMPAT_SEMTECH_SX1261 := semtech,sx1261

config DT_HAS_SEMTECH_SX1261_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1261))

DT_COMPAT_SEMTECH_SX1262 := semtech,sx1262

config DT_HAS_SEMTECH_SX1262_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1262))

DT_COMPAT_SEMTECH_SX1272 := semtech,sx1272

config DT_HAS_SEMTECH_SX1272_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1272))

DT_COMPAT_SEMTECH_SX1276 := semtech,sx1276

config DT_HAS_SEMTECH_SX1276_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1276))

DT_COMPAT_SEMTECH_SX1509B := semtech,sx1509b

config DT_HAS_SEMTECH_SX1509B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1509B))

DT_COMPAT_SEMTECH_SX9500 := semtech,sx9500

config DT_HAS_SEMTECH_SX9500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX9500))

DT_COMPAT_SENSIRION_SCD40 := sensirion,scd40

config DT_HAS_SENSIRION_SCD40_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SCD40))

DT_COMPAT_SENSIRION_SCD41 := sensirion,scd41

config DT_HAS_SENSIRION_SCD41_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SCD41))

DT_COMPAT_SENSIRION_SGP40 := sensirion,sgp40

config DT_HAS_SENSIRION_SGP40_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SGP40))

DT_COMPAT_SENSIRION_SHT21 := sensirion,sht21

config DT_HAS_SENSIRION_SHT21_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHT21))

DT_COMPAT_SENSIRION_SHT3XD := sensirion,sht3xd

config DT_HAS_SENSIRION_SHT3XD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHT3XD))

DT_COMPAT_SENSIRION_SHT4X := sensirion,sht4x

config DT_HAS_SENSIRION_SHT4X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHT4X))

DT_COMPAT_SENSIRION_SHTCX := sensirion,shtcx

config DT_HAS_SENSIRION_SHTCX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHTCX))

DT_COMPAT_SENSIRION_STS4X := sensirion,sts4x

config DT_HAS_SENSIRION_STS4X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_STS4X))

DT_COMPAT_SENSRY_SY1XX := sensry,sy1xx

config DT_HAS_SENSRY_SY1XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSRY_SY1XX))

DT_COMPAT_SENSRY_SY1XX_EVENT_UNIT := sensry,sy1xx-event-unit

config DT_HAS_SENSRY_SY1XX_EVENT_UNIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSRY_SY1XX_EVENT_UNIT))

DT_COMPAT_SENSRY_SY1XX_PINCTRL := sensry,sy1xx-pinctrl

config DT_HAS_SENSRY_SY1XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSRY_SY1XX_PINCTRL))

DT_COMPAT_SENSRY_SY1XX_SYS_TIMER := sensry,sy1xx-sys-timer

config DT_HAS_SENSRY_SY1XX_SYS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSRY_SY1XX_SYS_TIMER))

DT_COMPAT_SENSRY_SY1XX_UART := sensry,sy1xx-uart

config DT_HAS_SENSRY_SY1XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSRY_SY1XX_UART))

DT_COMPAT_SHARED_IRQ := shared-irq

config DT_HAS_SHARED_IRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SHARED_IRQ))

DT_COMPAT_SHARP_LS0XX := sharp,ls0xx

config DT_HAS_SHARP_LS0XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SHARP_LS0XX))

DT_COMPAT_SIEMENS_IVSHMEM_ETH := siemens,ivshmem-eth

config DT_HAS_SIEMENS_IVSHMEM_ETH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIEMENS_IVSHMEM_ETH))

DT_COMPAT_SIFIVE_CLINT0 := sifive,clint0

config DT_HAS_SIFIVE_CLINT0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_CLINT0))

DT_COMPAT_SIFIVE_DTIM0 := sifive,dtim0

config DT_HAS_SIFIVE_DTIM0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_DTIM0))

DT_COMPAT_SIFIVE_E24 := sifive,e24

config DT_HAS_SIFIVE_E24_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E24))

DT_COMPAT_SIFIVE_E31 := sifive,e31

config DT_HAS_SIFIVE_E31_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E31))

DT_COMPAT_SIFIVE_E51 := sifive,e51

config DT_HAS_SIFIVE_E51_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E51))

DT_COMPAT_SIFIVE_FU740_C000_DDR := sifive,fu740-c000-ddr

config DT_HAS_SIFIVE_FU740_C000_DDR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_FU740_C000_DDR))

DT_COMPAT_SIFIVE_GPIO0 := sifive,gpio0

config DT_HAS_SIFIVE_GPIO0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_GPIO0))

DT_COMPAT_SIFIVE_I2C0 := sifive,i2c0

config DT_HAS_SIFIVE_I2C0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_I2C0))

DT_COMPAT_SIFIVE_PINCTRL := sifive,pinctrl

config DT_HAS_SIFIVE_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PINCTRL))

DT_COMPAT_SIFIVE_PLIC_1_0_0 := sifive,plic-1.0.0

config DT_HAS_SIFIVE_PLIC_1_0_0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PLIC_1_0_0))

DT_COMPAT_SIFIVE_PWM0 := sifive,pwm0

config DT_HAS_SIFIVE_PWM0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PWM0))

DT_COMPAT_SIFIVE_S7 := sifive,s7

config DT_HAS_SIFIVE_S7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_S7))

DT_COMPAT_SIFIVE_SPI0 := sifive,spi0

config DT_HAS_SIFIVE_SPI0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_SPI0))

DT_COMPAT_SIFIVE_U54 := sifive,u54

config DT_HAS_SIFIVE_U54_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_U54))

DT_COMPAT_SIFIVE_UART0 := sifive,uart0

config DT_HAS_SIFIVE_UART0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_UART0))

DT_COMPAT_SIFIVE_WDT := sifive,wdt

config DT_HAS_SIFIVE_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_WDT))

DT_COMPAT_SILABS_BT_HCI_EFR32 := silabs,bt-hci-efr32

config DT_HAS_SILABS_BT_HCI_EFR32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_BT_HCI_EFR32))

DT_COMPAT_SILABS_DBUS_PINCTRL := silabs,dbus-pinctrl

config DT_HAS_SILABS_DBUS_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_DBUS_PINCTRL))

DT_COMPAT_SILABS_GECKO_ADC := silabs,gecko-adc

config DT_HAS_SILABS_GECKO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_ADC))

DT_COMPAT_SILABS_GECKO_BURTC := silabs,gecko-burtc

config DT_HAS_SILABS_GECKO_BURTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_BURTC))

DT_COMPAT_SILABS_GECKO_ETHERNET := silabs,gecko-ethernet

config DT_HAS_SILABS_GECKO_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_ETHERNET))

DT_COMPAT_SILABS_GECKO_FLASH_CONTROLLER := silabs,gecko-flash-controller

config DT_HAS_SILABS_GECKO_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_FLASH_CONTROLLER))

DT_COMPAT_SILABS_GECKO_GPIO := silabs,gecko-gpio

config DT_HAS_SILABS_GECKO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_GPIO))

DT_COMPAT_SILABS_GECKO_GPIO_PORT := silabs,gecko-gpio-port

config DT_HAS_SILABS_GECKO_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_GPIO_PORT))

DT_COMPAT_SILABS_GECKO_I2C := silabs,gecko-i2c

config DT_HAS_SILABS_GECKO_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_I2C))

DT_COMPAT_SILABS_GECKO_IADC := silabs,gecko-iadc

config DT_HAS_SILABS_GECKO_IADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_IADC))

DT_COMPAT_SILABS_GECKO_LEUART := silabs,gecko-leuart

config DT_HAS_SILABS_GECKO_LEUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_LEUART))

DT_COMPAT_SILABS_GECKO_PINCTRL := silabs,gecko-pinctrl

config DT_HAS_SILABS_GECKO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_PINCTRL))

DT_COMPAT_SILABS_GECKO_PWM := silabs,gecko-pwm

config DT_HAS_SILABS_GECKO_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_PWM))

DT_COMPAT_SILABS_GECKO_RTCC := silabs,gecko-rtcc

config DT_HAS_SILABS_GECKO_RTCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_RTCC))

DT_COMPAT_SILABS_GECKO_SEMAILBOX := silabs,gecko-semailbox

config DT_HAS_SILABS_GECKO_SEMAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_SEMAILBOX))

DT_COMPAT_SILABS_GECKO_SPI_EUSART := silabs,gecko-spi-eusart

config DT_HAS_SILABS_GECKO_SPI_EUSART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_SPI_EUSART))

DT_COMPAT_SILABS_GECKO_SPI_USART := silabs,gecko-spi-usart

config DT_HAS_SILABS_GECKO_SPI_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_SPI_USART))

DT_COMPAT_SILABS_GECKO_STIMER := silabs,gecko-stimer

config DT_HAS_SILABS_GECKO_STIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_STIMER))

DT_COMPAT_SILABS_GECKO_TIMER := silabs,gecko-timer

config DT_HAS_SILABS_GECKO_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_TIMER))

DT_COMPAT_SILABS_GECKO_TRNG := silabs,gecko-trng

config DT_HAS_SILABS_GECKO_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_TRNG))

DT_COMPAT_SILABS_GECKO_UART := silabs,gecko-uart

config DT_HAS_SILABS_GECKO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_UART))

DT_COMPAT_SILABS_GECKO_USART := silabs,gecko-usart

config DT_HAS_SILABS_GECKO_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_USART))

DT_COMPAT_SILABS_GECKO_WDOG := silabs,gecko-wdog

config DT_HAS_SILABS_GECKO_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_WDOG))

DT_COMPAT_SILABS_HFXO := silabs,hfxo

config DT_HAS_SILABS_HFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_HFXO))

DT_COMPAT_SILABS_LDMA := silabs,ldma

config DT_HAS_SILABS_LDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_LDMA))

DT_COMPAT_SILABS_SERIES_CLOCK := silabs,series-clock

config DT_HAS_SILABS_SERIES_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES_CLOCK))

DT_COMPAT_SILABS_SERIES2_DCDC := silabs,series2-dcdc

config DT_HAS_SILABS_SERIES2_DCDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES2_DCDC))

DT_COMPAT_SILABS_SERIES2_HFRCODPLL := silabs,series2-hfrcodpll

config DT_HAS_SILABS_SERIES2_HFRCODPLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES2_HFRCODPLL))

DT_COMPAT_SILABS_SERIES2_HFRCOEM23 := silabs,series2-hfrcoem23

config DT_HAS_SILABS_SERIES2_HFRCOEM23_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES2_HFRCOEM23))

DT_COMPAT_SILABS_SERIES2_LFRCO := silabs,series2-lfrco

config DT_HAS_SILABS_SERIES2_LFRCO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES2_LFRCO))

DT_COMPAT_SILABS_SERIES2_LFXO := silabs,series2-lfxo

config DT_HAS_SILABS_SERIES2_LFXO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SERIES2_LFXO))

DT_COMPAT_SILABS_SI32_AES := silabs,si32-aes

config DT_HAS_SILABS_SI32_AES_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_AES))

DT_COMPAT_SILABS_SI32_AHB := silabs,si32-ahb

config DT_HAS_SILABS_SI32_AHB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_AHB))

DT_COMPAT_SILABS_SI32_APB := silabs,si32-apb

config DT_HAS_SILABS_SI32_APB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_APB))

DT_COMPAT_SILABS_SI32_DMA := silabs,si32-dma

config DT_HAS_SILABS_SI32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_DMA))

DT_COMPAT_SILABS_SI32_FLASH_CONTROLLER := silabs,si32-flash-controller

config DT_HAS_SILABS_SI32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_FLASH_CONTROLLER))

DT_COMPAT_SILABS_SI32_GPIO := silabs,si32-gpio

config DT_HAS_SILABS_SI32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_GPIO))

DT_COMPAT_SILABS_SI32_PINCTRL := silabs,si32-pinctrl

config DT_HAS_SILABS_SI32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_PINCTRL))

DT_COMPAT_SILABS_SI32_PLL := silabs,si32-pll

config DT_HAS_SILABS_SI32_PLL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_PLL))

DT_COMPAT_SILABS_SI32_USART := silabs,si32-usart

config DT_HAS_SILABS_SI32_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI32_USART))

DT_COMPAT_SILABS_SI7006 := silabs,si7006

config DT_HAS_SILABS_SI7006_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7006))

DT_COMPAT_SILABS_SI7055 := silabs,si7055

config DT_HAS_SILABS_SI7055_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7055))

DT_COMPAT_SILABS_SI7060 := silabs,si7060

config DT_HAS_SILABS_SI7060_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7060))

DT_COMPAT_SILABS_SI7210 := silabs,si7210

config DT_HAS_SILABS_SI7210_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7210))

DT_COMPAT_SIMCOM_SIM7080 := simcom,sim7080

config DT_HAS_SIMCOM_SIM7080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIMCOM_SIM7080))

DT_COMPAT_SINOWEALTH_SH1106 := sinowealth,sh1106

config DT_HAS_SINOWEALTH_SH1106_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SINOWEALTH_SH1106))

DT_COMPAT_SITRONIX_CF1133 := sitronix,cf1133

config DT_HAS_SITRONIX_CF1133_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_CF1133))

DT_COMPAT_SITRONIX_ST7735R := sitronix,st7735r

config DT_HAS_SITRONIX_ST7735R_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_ST7735R))

DT_COMPAT_SITRONIX_ST7789V := sitronix,st7789v

config DT_HAS_SITRONIX_ST7789V_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_ST7789V))

DT_COMPAT_SITRONIX_ST7796S := sitronix,st7796s

config DT_HAS_SITRONIX_ST7796S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_ST7796S))

DT_COMPAT_SKYWORKS_SKY13317 := skyworks,sky13317

config DT_HAS_SKYWORKS_SKY13317_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY13317))

DT_COMPAT_SKYWORKS_SKY13351 := skyworks,sky13351

config DT_HAS_SKYWORKS_SKY13351_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY13351))

DT_COMPAT_SKYWORKS_SKY66112_11 := skyworks,sky66112-11

config DT_HAS_SKYWORKS_SKY66112_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66112_11))

DT_COMPAT_SKYWORKS_SKY66114_11 := skyworks,sky66114-11

config DT_HAS_SKYWORKS_SKY66114_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66114_11))

DT_COMPAT_SKYWORKS_SKY66403_11 := skyworks,sky66403-11

config DT_HAS_SKYWORKS_SKY66403_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66403_11))

DT_COMPAT_SKYWORKS_SKY66407_11 := skyworks,sky66407-11

config DT_HAS_SKYWORKS_SKY66407_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66407_11))

DT_COMPAT_SMSC_LAN91C111 := smsc,lan91c111

config DT_HAS_SMSC_LAN91C111_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN91C111))

DT_COMPAT_SMSC_LAN91C111_MDIO := smsc,lan91c111-mdio

config DT_HAS_SMSC_LAN91C111_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN91C111_MDIO))

DT_COMPAT_SMSC_LAN9220 := smsc,lan9220

config DT_HAS_SMSC_LAN9220_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN9220))

DT_COMPAT_SNPS_ARC_IOT_SYSCONF := snps,arc-iot-sysconf

config DT_HAS_SNPS_ARC_IOT_SYSCONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARC_IOT_SYSCONF))

DT_COMPAT_SNPS_ARC_TIMER := snps,arc-timer

config DT_HAS_SNPS_ARC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARC_TIMER))

DT_COMPAT_SNPS_ARCEM := snps,arcem

config DT_HAS_SNPS_ARCEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCEM))

DT_COMPAT_SNPS_ARCHS_ICI := snps,archs-ici

config DT_HAS_SNPS_ARCHS_ICI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCHS_ICI))

DT_COMPAT_SNPS_ARCHS_IDU_INTC := snps,archs-idu-intc

config DT_HAS_SNPS_ARCHS_IDU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCHS_IDU_INTC))

DT_COMPAT_SNPS_ARCV2_INTC := snps,arcv2-intc

config DT_HAS_SNPS_ARCV2_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCV2_INTC))

DT_COMPAT_SNPS_CREG_GPIO := snps,creg-gpio

config DT_HAS_SNPS_CREG_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_CREG_GPIO))

DT_COMPAT_SNPS_DESIGNWARE_DMA := snps,designware-dma

config DT_HAS_SNPS_DESIGNWARE_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_DMA))

DT_COMPAT_SNPS_DESIGNWARE_DMA_AXI := snps,designware-dma-axi

config DT_HAS_SNPS_DESIGNWARE_DMA_AXI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_DMA_AXI))

DT_COMPAT_SNPS_DESIGNWARE_ETHERNET := snps,designware-ethernet

config DT_HAS_SNPS_DESIGNWARE_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_ETHERNET))

DT_COMPAT_SNPS_DESIGNWARE_GPIO := snps,designware-gpio

config DT_HAS_SNPS_DESIGNWARE_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_GPIO))

DT_COMPAT_SNPS_DESIGNWARE_I2C := snps,designware-i2c

config DT_HAS_SNPS_DESIGNWARE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_I2C))

DT_COMPAT_SNPS_DESIGNWARE_INTC := snps,designware-intc

config DT_HAS_SNPS_DESIGNWARE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_INTC))

DT_COMPAT_SNPS_DESIGNWARE_SPI := snps,designware-spi

config DT_HAS_SNPS_DESIGNWARE_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_SPI))

DT_COMPAT_SNPS_DESIGNWARE_SSI := snps,designware-ssi

config DT_HAS_SNPS_DESIGNWARE_SSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_SSI))

DT_COMPAT_SNPS_DESIGNWARE_USB := snps,designware-usb

config DT_HAS_SNPS_DESIGNWARE_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_USB))

DT_COMPAT_SNPS_DESIGNWARE_WATCHDOG := snps,designware-watchdog

config DT_HAS_SNPS_DESIGNWARE_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_WATCHDOG))

DT_COMPAT_SNPS_DW_TIMERS := snps,dw-timers

config DT_HAS_SNPS_DW_TIMERS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DW_TIMERS))

DT_COMPAT_SNPS_DWC2 := snps,dwc2

config DT_HAS_SNPS_DWC2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DWC2))

DT_COMPAT_SNPS_DWCXGMAC := snps,dwcxgmac

config DT_HAS_SNPS_DWCXGMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DWCXGMAC))

DT_COMPAT_SNPS_DWCXGMAC_MDIO := snps,dwcxgmac-mdio

config DT_HAS_SNPS_DWCXGMAC_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DWCXGMAC_MDIO))

DT_COMPAT_SNPS_EMSDP_PINCTRL := snps,emsdp-pinctrl

config DT_HAS_SNPS_EMSDP_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_EMSDP_PINCTRL))

DT_COMPAT_SNPS_ETHERNET_CYCLONEV := snps,ethernet-cyclonev

config DT_HAS_SNPS_ETHERNET_CYCLONEV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ETHERNET_CYCLONEV))

DT_COMPAT_SNPS_HOSTLINK_UART := snps,hostlink-uart

config DT_HAS_SNPS_HOSTLINK_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_HOSTLINK_UART))

DT_COMPAT_SNPS_NSIM_UART := snps,nsim-uart

config DT_HAS_SNPS_NSIM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_NSIM_UART))

DT_COMPAT_SOC_NV_FLASH := soc-nv-flash

config DT_HAS_SOC_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOC_NV_FLASH))

DT_COMPAT_SOLOMON_SSD1306FB := solomon,ssd1306fb

config DT_HAS_SOLOMON_SSD1306FB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1306FB))

DT_COMPAT_SOLOMON_SSD1322 := solomon,ssd1322

config DT_HAS_SOLOMON_SSD1322_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1322))

DT_COMPAT_SOLOMON_SSD1327FB := solomon,ssd1327fb

config DT_HAS_SOLOMON_SSD1327FB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1327FB))

DT_COMPAT_SOLOMON_SSD1608 := solomon,ssd1608

config DT_HAS_SOLOMON_SSD1608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1608))

DT_COMPAT_SOLOMON_SSD1673 := solomon,ssd1673

config DT_HAS_SOLOMON_SSD1673_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1673))

DT_COMPAT_SOLOMON_SSD1675A := solomon,ssd1675a

config DT_HAS_SOLOMON_SSD1675A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1675A))

DT_COMPAT_SOLOMON_SSD1680 := solomon,ssd1680

config DT_HAS_SOLOMON_SSD1680_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1680))

DT_COMPAT_SOLOMON_SSD1681 := solomon,ssd1681

config DT_HAS_SOLOMON_SSD1681_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1681))

DT_COMPAT_SPARKFUN_MICROMOD_GPIO := sparkfun,micromod-gpio

config DT_HAS_SPARKFUN_MICROMOD_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SPARKFUN_MICROMOD_GPIO))

DT_COMPAT_SPARKFUN_PRO_MICRO_GPIO := sparkfun,pro-micro-gpio

config DT_HAS_SPARKFUN_PRO_MICRO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SPARKFUN_PRO_MICRO_GPIO))

DT_COMPAT_SPARKFUN_SERLCD := sparkfun,serlcd

config DT_HAS_SPARKFUN_SERLCD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SPARKFUN_SERLCD))

DT_COMPAT_SQN_GM02S := sqn,gm02s

config DT_HAS_SQN_GM02S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SQN_GM02S))

DT_COMPAT_SQN_HWSPINLOCK := sqn,hwspinlock

config DT_HAS_SQN_HWSPINLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SQN_HWSPINLOCK))

DT_COMPAT_ST_DSI_LCD_QSH_030 := st,dsi-lcd-qsh-030

config DT_HAS_ST_DSI_LCD_QSH_030_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_DSI_LCD_QSH_030))

DT_COMPAT_ST_HCI_SPI_V1 := st,hci-spi-v1

config DT_HAS_ST_HCI_SPI_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HCI_SPI_V1))

DT_COMPAT_ST_HCI_SPI_V2 := st,hci-spi-v2

config DT_HAS_ST_HCI_SPI_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HCI_SPI_V2))

DT_COMPAT_ST_HCI_STM32WB0 := st,hci-stm32wb0

config DT_HAS_ST_HCI_STM32WB0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HCI_STM32WB0))

DT_COMPAT_ST_HCI_STM32WBA := st,hci-stm32wba

config DT_HAS_ST_HCI_STM32WBA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HCI_STM32WBA))

DT_COMPAT_ST_HTS221 := st,hts221

config DT_HAS_ST_HTS221_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HTS221))

DT_COMPAT_ST_I3G4250D := st,i3g4250d

config DT_HAS_ST_I3G4250D_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_I3G4250D))

DT_COMPAT_ST_IIS2DH := st,iis2dh

config DT_HAS_ST_IIS2DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2DH))

DT_COMPAT_ST_IIS2DLPC := st,iis2dlpc

config DT_HAS_ST_IIS2DLPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2DLPC))

DT_COMPAT_ST_IIS2ICLX := st,iis2iclx

config DT_HAS_ST_IIS2ICLX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2ICLX))

DT_COMPAT_ST_IIS2MDC := st,iis2mdc

config DT_HAS_ST_IIS2MDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2MDC))

DT_COMPAT_ST_IIS328DQ := st,iis328dq

config DT_HAS_ST_IIS328DQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS328DQ))

DT_COMPAT_ST_IIS3DHHC := st,iis3dhhc

config DT_HAS_ST_IIS3DHHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS3DHHC))

DT_COMPAT_ST_ILPS22QS := st,ilps22qs

config DT_HAS_ST_ILPS22QS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_ILPS22QS))

DT_COMPAT_ST_ISM330DHCX := st,ism330dhcx

config DT_HAS_ST_ISM330DHCX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_ISM330DHCX))

DT_COMPAT_ST_LIS2DE12 := st,lis2de12

config DT_HAS_ST_LIS2DE12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DE12))

DT_COMPAT_ST_LIS2DH := st,lis2dh

config DT_HAS_ST_LIS2DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DH))

DT_COMPAT_ST_LIS2DH12 := st,lis2dh12

config DT_HAS_ST_LIS2DH12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DH12))

DT_COMPAT_ST_LIS2DS12 := st,lis2ds12

config DT_HAS_ST_LIS2DS12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DS12))

DT_COMPAT_ST_LIS2DU12 := st,lis2du12

config DT_HAS_ST_LIS2DU12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DU12))

DT_COMPAT_ST_LIS2DUX12 := st,lis2dux12

config DT_HAS_ST_LIS2DUX12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DUX12))

DT_COMPAT_ST_LIS2DUXS12 := st,lis2duxs12

config DT_HAS_ST_LIS2DUXS12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DUXS12))

DT_COMPAT_ST_LIS2DW12 := st,lis2dw12

config DT_HAS_ST_LIS2DW12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DW12))

DT_COMPAT_ST_LIS2MDL := st,lis2mdl

config DT_HAS_ST_LIS2MDL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2MDL))

DT_COMPAT_ST_LIS3DH := st,lis3dh

config DT_HAS_ST_LIS3DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS3DH))

DT_COMPAT_ST_LIS3MDL_MAGN := st,lis3mdl-magn

config DT_HAS_ST_LIS3MDL_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS3MDL_MAGN))

DT_COMPAT_ST_LPS22DF := st,lps22df

config DT_HAS_ST_LPS22DF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS22DF))

DT_COMPAT_ST_LPS22HB_PRESS := st,lps22hb-press

config DT_HAS_ST_LPS22HB_PRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS22HB_PRESS))

DT_COMPAT_ST_LPS22HH := st,lps22hh

config DT_HAS_ST_LPS22HH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS22HH))

DT_COMPAT_ST_LPS25HB_PRESS := st,lps25hb-press

config DT_HAS_ST_LPS25HB_PRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS25HB_PRESS))

DT_COMPAT_ST_LPS28DFW := st,lps28dfw

config DT_HAS_ST_LPS28DFW_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS28DFW))

DT_COMPAT_ST_LSM303AGR_ACCEL := st,lsm303agr-accel

config DT_HAS_ST_LSM303AGR_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303AGR_ACCEL))

DT_COMPAT_ST_LSM303DLHC_ACCEL := st,lsm303dlhc-accel

config DT_HAS_ST_LSM303DLHC_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303DLHC_ACCEL))

DT_COMPAT_ST_LSM303DLHC_MAGN := st,lsm303dlhc-magn

config DT_HAS_ST_LSM303DLHC_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303DLHC_MAGN))

DT_COMPAT_ST_LSM6DS0 := st,lsm6ds0

config DT_HAS_ST_LSM6DS0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DS0))

DT_COMPAT_ST_LSM6DSL := st,lsm6dsl

config DT_HAS_ST_LSM6DSL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSL))

DT_COMPAT_ST_LSM6DSO := st,lsm6dso

config DT_HAS_ST_LSM6DSO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO))

DT_COMPAT_ST_LSM6DSO16IS := st,lsm6dso16is

config DT_HAS_ST_LSM6DSO16IS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO16IS))

DT_COMPAT_ST_LSM6DSO32 := st,lsm6dso32

config DT_HAS_ST_LSM6DSO32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO32))

DT_COMPAT_ST_LSM6DSV16X := st,lsm6dsv16x

config DT_HAS_ST_LSM6DSV16X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSV16X))

DT_COMPAT_ST_LSM9DS0_GYRO := st,lsm9ds0-gyro

config DT_HAS_ST_LSM9DS0_GYRO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM9DS0_GYRO))

DT_COMPAT_ST_LSM9DS0_MFD := st,lsm9ds0-mfd

config DT_HAS_ST_LSM9DS0_MFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM9DS0_MFD))

DT_COMPAT_ST_LSM9DS1 := st,lsm9ds1

config DT_HAS_ST_LSM9DS1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM9DS1))

DT_COMPAT_ST_MBOX_STM32_HSEM := st,mbox-stm32-hsem

config DT_HAS_ST_MBOX_STM32_HSEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_MBOX_STM32_HSEM))

DT_COMPAT_ST_MPXXDTYY := st,mpxxdtyy

config DT_HAS_ST_MPXXDTYY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_MPXXDTYY))

DT_COMPAT_ST_ST25R3911B := st,st25r3911b

config DT_HAS_ST_ST25R3911B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_ST25R3911B))

DT_COMPAT_ST_STM32_ADC := st,stm32-adc

config DT_HAS_ST_STM32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_ADC))

DT_COMPAT_ST_STM32_AES := st,stm32-aes

config DT_HAS_ST_STM32_AES_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_AES))

DT_COMPAT_ST_STM32_BACKUP_SRAM := st,stm32-backup-sram

config DT_HAS_ST_STM32_BACKUP_SRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BACKUP_SRAM))

DT_COMPAT_ST_STM32_BBRAM := st,stm32-bbram

config DT_HAS_ST_STM32_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BBRAM))

DT_COMPAT_ST_STM32_BDMA := st,stm32-bdma

config DT_HAS_ST_STM32_BDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BDMA))

DT_COMPAT_ST_STM32_BXCAN := st,stm32-bxcan

config DT_HAS_ST_STM32_BXCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BXCAN))

DT_COMPAT_ST_STM32_CCM := st,stm32-ccm

config DT_HAS_ST_STM32_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CCM))

DT_COMPAT_ST_STM32_CLOCK_MCO := st,stm32-clock-mco

config DT_HAS_ST_STM32_CLOCK_MCO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CLOCK_MCO))

DT_COMPAT_ST_STM32_CLOCK_MUX := st,stm32-clock-mux

config DT_HAS_ST_STM32_CLOCK_MUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CLOCK_MUX))

DT_COMPAT_ST_STM32_COUNTER := st,stm32-counter

config DT_HAS_ST_STM32_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_COUNTER))

DT_COMPAT_ST_STM32_CRYP := st,stm32-cryp

config DT_HAS_ST_STM32_CRYP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CRYP))

DT_COMPAT_ST_STM32_DAC := st,stm32-dac

config DT_HAS_ST_STM32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DAC))

DT_COMPAT_ST_STM32_DCMI := st,stm32-dcmi

config DT_HAS_ST_STM32_DCMI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DCMI))

DT_COMPAT_ST_STM32_DIGI_TEMP := st,stm32-digi-temp

config DT_HAS_ST_STM32_DIGI_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DIGI_TEMP))

DT_COMPAT_ST_STM32_DMA := st,stm32-dma

config DT_HAS_ST_STM32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA))

DT_COMPAT_ST_STM32_DMA_V1 := st,stm32-dma-v1

config DT_HAS_ST_STM32_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V1))

DT_COMPAT_ST_STM32_DMA_V2 := st,stm32-dma-v2

config DT_HAS_ST_STM32_DMA_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V2))

DT_COMPAT_ST_STM32_DMA_V2BIS := st,stm32-dma-v2bis

config DT_HAS_ST_STM32_DMA_V2BIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V2BIS))

DT_COMPAT_ST_STM32_DMAMUX := st,stm32-dmamux

config DT_HAS_ST_STM32_DMAMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMAMUX))

DT_COMPAT_ST_STM32_EEPROM := st,stm32-eeprom

config DT_HAS_ST_STM32_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_EEPROM))

DT_COMPAT_ST_STM32_ETHERNET := st,stm32-ethernet

config DT_HAS_ST_STM32_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_ETHERNET))

DT_COMPAT_ST_STM32_EXTI := st,stm32-exti

config DT_HAS_ST_STM32_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_EXTI))

DT_COMPAT_ST_STM32_FDCAN := st,stm32-fdcan

config DT_HAS_ST_STM32_FDCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FDCAN))

DT_COMPAT_ST_STM32_FLASH_CONTROLLER := st,stm32-flash-controller

config DT_HAS_ST_STM32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32_FMC := st,stm32-fmc

config DT_HAS_ST_STM32_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC))

DT_COMPAT_ST_STM32_FMC_MIPI_DBI := st,stm32-fmc-mipi-dbi

config DT_HAS_ST_STM32_FMC_MIPI_DBI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC_MIPI_DBI))

DT_COMPAT_ST_STM32_FMC_NOR_PSRAM := st,stm32-fmc-nor-psram

config DT_HAS_ST_STM32_FMC_NOR_PSRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC_NOR_PSRAM))

DT_COMPAT_ST_STM32_FMC_SDRAM := st,stm32-fmc-sdram

config DT_HAS_ST_STM32_FMC_SDRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC_SDRAM))

DT_COMPAT_ST_STM32_GPIO := st,stm32-gpio

config DT_HAS_ST_STM32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_GPIO))

DT_COMPAT_ST_STM32_HSE_CLOCK := st,stm32-hse-clock

config DT_HAS_ST_STM32_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_HSE_CLOCK))

DT_COMPAT_ST_STM32_HSEM_MAILBOX := st,stm32-hsem-mailbox

config DT_HAS_ST_STM32_HSEM_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_HSEM_MAILBOX))

DT_COMPAT_ST_STM32_HSI48_CLOCK := st,stm32-hsi48-clock

config DT_HAS_ST_STM32_HSI48_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_HSI48_CLOCK))

DT_COMPAT_ST_STM32_I2C_V1 := st,stm32-i2c-v1

config DT_HAS_ST_STM32_I2C_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2C_V1))

DT_COMPAT_ST_STM32_I2C_V2 := st,stm32-i2c-v2

config DT_HAS_ST_STM32_I2C_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2C_V2))

DT_COMPAT_ST_STM32_I2S := st,stm32-i2s

config DT_HAS_ST_STM32_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2S))

DT_COMPAT_ST_STM32_I3C := st,stm32-i3c

config DT_HAS_ST_STM32_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I3C))

DT_COMPAT_ST_STM32_IPCC_MAILBOX := st,stm32-ipcc-mailbox

config DT_HAS_ST_STM32_IPCC_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_IPCC_MAILBOX))

DT_COMPAT_ST_STM32_LPTIM := st,stm32-lptim

config DT_HAS_ST_STM32_LPTIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LPTIM))

DT_COMPAT_ST_STM32_LPUART := st,stm32-lpuart

config DT_HAS_ST_STM32_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LPUART))

DT_COMPAT_ST_STM32_LSE_CLOCK := st,stm32-lse-clock

config DT_HAS_ST_STM32_LSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LSE_CLOCK))

DT_COMPAT_ST_STM32_LTDC := st,stm32-ltdc

config DT_HAS_ST_STM32_LTDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LTDC))

DT_COMPAT_ST_STM32_MDIO := st,stm32-mdio

config DT_HAS_ST_STM32_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_MDIO))

DT_COMPAT_ST_STM32_MIPI_DSI := st,stm32-mipi-dsi

config DT_HAS_ST_STM32_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_MIPI_DSI))

DT_COMPAT_ST_STM32_MSI_CLOCK := st,stm32-msi-clock

config DT_HAS_ST_STM32_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_MSI_CLOCK))

DT_COMPAT_ST_STM32_NV_FLASH := st,stm32-nv-flash

config DT_HAS_ST_STM32_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_NV_FLASH))

DT_COMPAT_ST_STM32_OSPI := st,stm32-ospi

config DT_HAS_ST_STM32_OSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OSPI))

DT_COMPAT_ST_STM32_OSPI_NOR := st,stm32-ospi-nor

config DT_HAS_ST_STM32_OSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OSPI_NOR))

DT_COMPAT_ST_STM32_OTGFS := st,stm32-otgfs

config DT_HAS_ST_STM32_OTGFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OTGFS))

DT_COMPAT_ST_STM32_OTGHS := st,stm32-otghs

config DT_HAS_ST_STM32_OTGHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OTGHS))

DT_COMPAT_ST_STM32_PINCTRL := st,stm32-pinctrl

config DT_HAS_ST_STM32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_PINCTRL))

DT_COMPAT_ST_STM32_PWM := st,stm32-pwm

config DT_HAS_ST_STM32_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_PWM))

DT_COMPAT_ST_STM32_PWR := st,stm32-pwr

config DT_HAS_ST_STM32_PWR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_PWR))

DT_COMPAT_ST_STM32_QDEC := st,stm32-qdec

config DT_HAS_ST_STM32_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QDEC))

DT_COMPAT_ST_STM32_QSPI := st,stm32-qspi

config DT_HAS_ST_STM32_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QSPI))

DT_COMPAT_ST_STM32_QSPI_NOR := st,stm32-qspi-nor

config DT_HAS_ST_STM32_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QSPI_NOR))

DT_COMPAT_ST_STM32_RCC := st,stm32-rcc

config DT_HAS_ST_STM32_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RCC))

DT_COMPAT_ST_STM32_RCC_RCTL := st,stm32-rcc-rctl

config DT_HAS_ST_STM32_RCC_RCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RCC_RCTL))

DT_COMPAT_ST_STM32_RNG := st,stm32-rng

config DT_HAS_ST_STM32_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RNG))

DT_COMPAT_ST_STM32_RTC := st,stm32-rtc

config DT_HAS_ST_STM32_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RTC))

DT_COMPAT_ST_STM32_SDMMC := st,stm32-sdmmc

config DT_HAS_ST_STM32_SDMMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SDMMC))

DT_COMPAT_ST_STM32_SMBUS := st,stm32-smbus

config DT_HAS_ST_STM32_SMBUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SMBUS))

DT_COMPAT_ST_STM32_SPI := st,stm32-spi

config DT_HAS_ST_STM32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI))

DT_COMPAT_ST_STM32_SPI_FIFO := st,stm32-spi-fifo

config DT_HAS_ST_STM32_SPI_FIFO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_FIFO))

DT_COMPAT_ST_STM32_SPI_HOST_CMD := st,stm32-spi-host-cmd

config DT_HAS_ST_STM32_SPI_HOST_CMD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_HOST_CMD))

DT_COMPAT_ST_STM32_SPI_SUBGHZ := st,stm32-spi-subghz

config DT_HAS_ST_STM32_SPI_SUBGHZ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_SUBGHZ))

DT_COMPAT_ST_STM32_TEMP := st,stm32-temp

config DT_HAS_ST_STM32_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TEMP))

DT_COMPAT_ST_STM32_TEMP_CAL := st,stm32-temp-cal

config DT_HAS_ST_STM32_TEMP_CAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TEMP_CAL))

DT_COMPAT_ST_STM32_TIMERS := st,stm32-timers

config DT_HAS_ST_STM32_TIMERS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TIMERS))

DT_COMPAT_ST_STM32_UART := st,stm32-uart

config DT_HAS_ST_STM32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_UART))

DT_COMPAT_ST_STM32_UCPD := st,stm32-ucpd

config DT_HAS_ST_STM32_UCPD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_UCPD))

DT_COMPAT_ST_STM32_USART := st,stm32-usart

config DT_HAS_ST_STM32_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USART))

DT_COMPAT_ST_STM32_USB := st,stm32-usb

config DT_HAS_ST_STM32_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USB))

DT_COMPAT_ST_STM32_USBPHYC := st,stm32-usbphyc

config DT_HAS_ST_STM32_USBPHYC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USBPHYC))

DT_COMPAT_ST_STM32_VBAT := st,stm32-vbat

config DT_HAS_ST_STM32_VBAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_VBAT))

DT_COMPAT_ST_STM32_VREF := st,stm32-vref

config DT_HAS_ST_STM32_VREF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_VREF))

DT_COMPAT_ST_STM32_WATCHDOG := st,stm32-watchdog

config DT_HAS_ST_STM32_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_WATCHDOG))

DT_COMPAT_ST_STM32_WINDOW_WATCHDOG := st,stm32-window-watchdog

config DT_HAS_ST_STM32_WINDOW_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_WINDOW_WATCHDOG))

DT_COMPAT_ST_STM32_XSPI := st,stm32-xspi

config DT_HAS_ST_STM32_XSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_XSPI))

DT_COMPAT_ST_STM32_XSPI_NOR := st,stm32-xspi-nor

config DT_HAS_ST_STM32_XSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_XSPI_NOR))

DT_COMPAT_ST_STM32C0_HSI_CLOCK := st,stm32c0-hsi-clock

config DT_HAS_ST_STM32C0_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32C0_HSI_CLOCK))

DT_COMPAT_ST_STM32C0_TEMP_CAL := st,stm32c0-temp-cal

config DT_HAS_ST_STM32C0_TEMP_CAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32C0_TEMP_CAL))

DT_COMPAT_ST_STM32F0_PLL_CLOCK := st,stm32f0-pll-clock

config DT_HAS_ST_STM32F0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F0_PLL_CLOCK))

DT_COMPAT_ST_STM32F0_RCC := st,stm32f0-rcc

config DT_HAS_ST_STM32F0_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F0_RCC))

DT_COMPAT_ST_STM32F1_ADC := st,stm32f1-adc

config DT_HAS_ST_STM32F1_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_ADC))

DT_COMPAT_ST_STM32F1_CLOCK_MCO := st,stm32f1-clock-mco

config DT_HAS_ST_STM32F1_CLOCK_MCO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_CLOCK_MCO))

DT_COMPAT_ST_STM32F1_FLASH_CONTROLLER := st,stm32f1-flash-controller

config DT_HAS_ST_STM32F1_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F1_PINCTRL := st,stm32f1-pinctrl

config DT_HAS_ST_STM32F1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_PINCTRL))

DT_COMPAT_ST_STM32F1_PLL_CLOCK := st,stm32f1-pll-clock

config DT_HAS_ST_STM32F1_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_PLL_CLOCK))

DT_COMPAT_ST_STM32F1_RCC := st,stm32f1-rcc

config DT_HAS_ST_STM32F1_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_RCC))

DT_COMPAT_ST_STM32F100_PLL_CLOCK := st,stm32f100-pll-clock

config DT_HAS_ST_STM32F100_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F100_PLL_CLOCK))

DT_COMPAT_ST_STM32F105_PLL_CLOCK := st,stm32f105-pll-clock

config DT_HAS_ST_STM32F105_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F105_PLL_CLOCK))

DT_COMPAT_ST_STM32F105_PLL2_CLOCK := st,stm32f105-pll2-clock

config DT_HAS_ST_STM32F105_PLL2_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F105_PLL2_CLOCK))

DT_COMPAT_ST_STM32F2_FLASH_CONTROLLER := st,stm32f2-flash-controller

config DT_HAS_ST_STM32F2_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F2_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F2_PLL_CLOCK := st,stm32f2-pll-clock

config DT_HAS_ST_STM32F2_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F2_PLL_CLOCK))

DT_COMPAT_ST_STM32F3_RCC := st,stm32f3-rcc

config DT_HAS_ST_STM32F3_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F3_RCC))

DT_COMPAT_ST_STM32F4_ADC := st,stm32f4-adc

config DT_HAS_ST_STM32F4_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_ADC))

DT_COMPAT_ST_STM32F4_FLASH_CONTROLLER := st,stm32f4-flash-controller

config DT_HAS_ST_STM32F4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F4_FSOTG := st,stm32f4-fsotg

config DT_HAS_ST_STM32F4_FSOTG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_FSOTG))

DT_COMPAT_ST_STM32F4_NV_FLASH := st,stm32f4-nv-flash

config DT_HAS_ST_STM32F4_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_NV_FLASH))

DT_COMPAT_ST_STM32F4_PLL_CLOCK := st,stm32f4-pll-clock

config DT_HAS_ST_STM32F4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_PLL_CLOCK))

DT_COMPAT_ST_STM32F4_PLLI2S_CLOCK := st,stm32f4-plli2s-clock

config DT_HAS_ST_STM32F4_PLLI2S_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_PLLI2S_CLOCK))

DT_COMPAT_ST_STM32F411_PLLI2S_CLOCK := st,stm32f411-plli2s-clock

config DT_HAS_ST_STM32F411_PLLI2S_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F411_PLLI2S_CLOCK))

DT_COMPAT_ST_STM32F7_FLASH_CONTROLLER := st,stm32f7-flash-controller

config DT_HAS_ST_STM32F7_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F7_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F7_PLL_CLOCK := st,stm32f7-pll-clock

config DT_HAS_ST_STM32F7_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F7_PLL_CLOCK))

DT_COMPAT_ST_STM32G0_EXTI := st,stm32g0-exti

config DT_HAS_ST_STM32G0_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_EXTI))

DT_COMPAT_ST_STM32G0_FLASH_CONTROLLER := st,stm32g0-flash-controller

config DT_HAS_ST_STM32G0_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32G0_HSI_CLOCK := st,stm32g0-hsi-clock

config DT_HAS_ST_STM32G0_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_HSI_CLOCK))

DT_COMPAT_ST_STM32G0_PLL_CLOCK := st,stm32g0-pll-clock

config DT_HAS_ST_STM32G0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_PLL_CLOCK))

DT_COMPAT_ST_STM32G4_FLASH_CONTROLLER := st,stm32g4-flash-controller

config DT_HAS_ST_STM32G4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32G4_PLL_CLOCK := st,stm32g4-pll-clock

config DT_HAS_ST_STM32G4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G4_PLL_CLOCK))

DT_COMPAT_ST_STM32H7_ETHERNET := st,stm32h7-ethernet

config DT_HAS_ST_STM32H7_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_ETHERNET))

DT_COMPAT_ST_STM32H7_FDCAN := st,stm32h7-fdcan

config DT_HAS_ST_STM32H7_FDCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FDCAN))

DT_COMPAT_ST_STM32H7_FLASH_CONTROLLER := st,stm32h7-flash-controller

config DT_HAS_ST_STM32H7_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32H7_FMC := st,stm32h7-fmc

config DT_HAS_ST_STM32H7_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FMC))

DT_COMPAT_ST_STM32H7_HSI_CLOCK := st,stm32h7-hsi-clock

config DT_HAS_ST_STM32H7_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_HSI_CLOCK))

DT_COMPAT_ST_STM32H7_I2S := st,stm32h7-i2s

config DT_HAS_ST_STM32H7_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_I2S))

DT_COMPAT_ST_STM32H7_PLL_CLOCK := st,stm32h7-pll-clock

config DT_HAS_ST_STM32H7_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_PLL_CLOCK))

DT_COMPAT_ST_STM32H7_RCC := st,stm32h7-rcc

config DT_HAS_ST_STM32H7_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_RCC))

DT_COMPAT_ST_STM32H7_SPI := st,stm32h7-spi

config DT_HAS_ST_STM32H7_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_SPI))

DT_COMPAT_ST_STM32H7RS_EXTI := st,stm32h7rs-exti

config DT_HAS_ST_STM32H7RS_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7RS_EXTI))

DT_COMPAT_ST_STM32H7RS_PLL_CLOCK := st,stm32h7rs-pll-clock

config DT_HAS_ST_STM32H7RS_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7RS_PLL_CLOCK))

DT_COMPAT_ST_STM32H7RS_RCC := st,stm32h7rs-rcc

config DT_HAS_ST_STM32H7RS_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7RS_RCC))

DT_COMPAT_ST_STM32L0_MSI_CLOCK := st,stm32l0-msi-clock

config DT_HAS_ST_STM32L0_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L0_MSI_CLOCK))

DT_COMPAT_ST_STM32L0_NV_FLASH := st,stm32l0-nv-flash

config DT_HAS_ST_STM32L0_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L0_NV_FLASH))

DT_COMPAT_ST_STM32L0_PLL_CLOCK := st,stm32l0-pll-clock

config DT_HAS_ST_STM32L0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L0_PLL_CLOCK))

DT_COMPAT_ST_STM32L4_AES := st,stm32l4-aes

config DT_HAS_ST_STM32L4_AES_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L4_AES))

DT_COMPAT_ST_STM32L4_FLASH_CONTROLLER := st,stm32l4-flash-controller

config DT_HAS_ST_STM32L4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32L4_PLL_CLOCK := st,stm32l4-pll-clock

config DT_HAS_ST_STM32L4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L4_PLL_CLOCK))

DT_COMPAT_ST_STM32L5_FLASH_CONTROLLER := st,stm32l5-flash-controller

config DT_HAS_ST_STM32L5_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L5_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32MP1_RCC := st,stm32mp1-rcc

config DT_HAS_ST_STM32MP1_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32MP1_RCC))

DT_COMPAT_ST_STM32U0_PLL_CLOCK := st,stm32u0-pll-clock

config DT_HAS_ST_STM32U0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U0_PLL_CLOCK))

DT_COMPAT_ST_STM32U5_DMA := st,stm32u5-dma

config DT_HAS_ST_STM32U5_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_DMA))

DT_COMPAT_ST_STM32U5_MSI_CLOCK := st,stm32u5-msi-clock

config DT_HAS_ST_STM32U5_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_MSI_CLOCK))

DT_COMPAT_ST_STM32U5_OTGHS_PHY := st,stm32u5-otghs-phy

config DT_HAS_ST_STM32U5_OTGHS_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_OTGHS_PHY))

DT_COMPAT_ST_STM32U5_PLL_CLOCK := st,stm32u5-pll-clock

config DT_HAS_ST_STM32U5_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_PLL_CLOCK))

DT_COMPAT_ST_STM32U5_RCC := st,stm32u5-rcc

config DT_HAS_ST_STM32U5_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_RCC))

DT_COMPAT_ST_STM32WB_FLASH_CONTROLLER := st,stm32wb-flash-controller

config DT_HAS_ST_STM32WB_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32WB_PLL_CLOCK := st,stm32wb-pll-clock

config DT_HAS_ST_STM32WB_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_PLL_CLOCK))

DT_COMPAT_ST_STM32WB_RCC := st,stm32wb-rcc

config DT_HAS_ST_STM32WB_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_RCC))

DT_COMPAT_ST_STM32WB_RF := st,stm32wb-rf

config DT_HAS_ST_STM32WB_RF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_RF))

DT_COMPAT_ST_STM32WB0_ADC := st,stm32wb0-adc

config DT_HAS_ST_STM32WB0_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_ADC))

DT_COMPAT_ST_STM32WB0_FLASH_CONTROLLER := st,stm32wb0-flash-controller

config DT_HAS_ST_STM32WB0_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32WB0_GPIO_INTC := st,stm32wb0-gpio-intc

config DT_HAS_ST_STM32WB0_GPIO_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_GPIO_INTC))

DT_COMPAT_ST_STM32WB0_LSI_CLOCK := st,stm32wb0-lsi-clock

config DT_HAS_ST_STM32WB0_LSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_LSI_CLOCK))

DT_COMPAT_ST_STM32WB0_PWR := st,stm32wb0-pwr

config DT_HAS_ST_STM32WB0_PWR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_PWR))

DT_COMPAT_ST_STM32WB0_RCC := st,stm32wb0-rcc

config DT_HAS_ST_STM32WB0_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB0_RCC))

DT_COMPAT_ST_STM32WBA_FLASH_CONTROLLER := st,stm32wba-flash-controller

config DT_HAS_ST_STM32WBA_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32WBA_HSE_CLOCK := st,stm32wba-hse-clock

config DT_HAS_ST_STM32WBA_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_HSE_CLOCK))

DT_COMPAT_ST_STM32WBA_PLL_CLOCK := st,stm32wba-pll-clock

config DT_HAS_ST_STM32WBA_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_PLL_CLOCK))

DT_COMPAT_ST_STM32WBA_RCC := st,stm32wba-rcc

config DT_HAS_ST_STM32WBA_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_RCC))

DT_COMPAT_ST_STM32WL_HSE_CLOCK := st,stm32wl-hse-clock

config DT_HAS_ST_STM32WL_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_HSE_CLOCK))

DT_COMPAT_ST_STM32WL_RCC := st,stm32wl-rcc

config DT_HAS_ST_STM32WL_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_RCC))

DT_COMPAT_ST_STM32WL_SUBGHZ_RADIO := st,stm32wl-subghz-radio

config DT_HAS_ST_STM32WL_SUBGHZ_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_SUBGHZ_RADIO))

DT_COMPAT_ST_STMPE1600 := st,stmpe1600

config DT_HAS_ST_STMPE1600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STMPE1600))

DT_COMPAT_ST_STMPE811 := st,stmpe811

config DT_HAS_ST_STMPE811_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STMPE811))

DT_COMPAT_ST_STTS22H := st,stts22h

config DT_HAS_ST_STTS22H_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STTS22H))

DT_COMPAT_ST_STTS751 := st,stts751

config DT_HAS_ST_STTS751_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STTS751))

DT_COMPAT_ST_VL53L0X := st,vl53l0x

config DT_HAS_ST_VL53L0X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_VL53L0X))

DT_COMPAT_ST_VL53L1X := st,vl53l1x

config DT_HAS_ST_VL53L1X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_VL53L1X))

DT_COMPAT_ST_MORPHO_HEADER := st-morpho-header

config DT_HAS_ST_MORPHO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_MORPHO_HEADER))

DT_COMPAT_STARFIVE_JH7100_CLINT := starfive,jh7100-clint

config DT_HAS_STARFIVE_JH7100_CLINT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_STARFIVE_JH7100_CLINT))

DT_COMPAT_STEMMA_QT_CONNECTOR := stemma-qt-connector

config DT_HAS_STEMMA_QT_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_STEMMA_QT_CONNECTOR))

DT_COMPAT_SWERV_PIC := swerv,pic

config DT_HAS_SWERV_PIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SWERV_PIC))

DT_COMPAT_SWIR_HL7800 := swir,hl7800

config DT_HAS_SWIR_HL7800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SWIR_HL7800))

DT_COMPAT_SWJ_CONNECTOR := swj-connector

config DT_HAS_SWJ_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SWJ_CONNECTOR))

DT_COMPAT_SYSCON := syscon

config DT_HAS_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SYSCON))

DT_COMPAT_TDK_NTCG163JF103FT1 := tdk,ntcg163jf103ft1

config DT_HAS_TDK_NTCG163JF103FT1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TDK_NTCG163JF103FT1))

DT_COMPAT_TELINK_B91 := telink,b91

config DT_HAS_TELINK_B91_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91))

DT_COMPAT_TELINK_B91_ADC := telink,b91-adc

config DT_HAS_TELINK_B91_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_ADC))

DT_COMPAT_TELINK_B91_FLASH_CONTROLLER := telink,b91-flash-controller

config DT_HAS_TELINK_B91_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_FLASH_CONTROLLER))

DT_COMPAT_TELINK_B91_GPIO := telink,b91-gpio

config DT_HAS_TELINK_B91_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_GPIO))

DT_COMPAT_TELINK_B91_I2C := telink,b91-i2c

config DT_HAS_TELINK_B91_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_I2C))

DT_COMPAT_TELINK_B91_PINCTRL := telink,b91-pinctrl

config DT_HAS_TELINK_B91_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_PINCTRL))

DT_COMPAT_TELINK_B91_POWER := telink,b91-power

config DT_HAS_TELINK_B91_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_POWER))

DT_COMPAT_TELINK_B91_PWM := telink,b91-pwm

config DT_HAS_TELINK_B91_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_PWM))

DT_COMPAT_TELINK_B91_SPI := telink,b91-spi

config DT_HAS_TELINK_B91_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_SPI))

DT_COMPAT_TELINK_B91_TRNG := telink,b91-trng

config DT_HAS_TELINK_B91_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_TRNG))

DT_COMPAT_TELINK_B91_UART := telink,b91-uart

config DT_HAS_TELINK_B91_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_UART))

DT_COMPAT_TELINK_B91_ZB := telink,b91-zb

config DT_HAS_TELINK_B91_ZB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_ZB))

DT_COMPAT_TELINK_MACHINE_TIMER := telink,machine-timer

config DT_HAS_TELINK_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_MACHINE_TIMER))

DT_COMPAT_TELIT_ME310G1 := telit,me310g1

config DT_HAS_TELIT_ME310G1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELIT_ME310G1))

DT_COMPAT_TELIT_ME910G1 := telit,me910g1

config DT_HAS_TELIT_ME910G1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELIT_ME910G1))

DT_COMPAT_TEST_GPIO_ENABLE_DISABLE_INTERRUPT := test-gpio-enable-disable-interrupt

config DT_HAS_TEST_GPIO_ENABLE_DISABLE_INTERRUPT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TEST_GPIO_ENABLE_DISABLE_INTERRUPT))

DT_COMPAT_TI_ADS1013 := ti,ads1013

config DT_HAS_TI_ADS1013_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1013))

DT_COMPAT_TI_ADS1014 := ti,ads1014

config DT_HAS_TI_ADS1014_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1014))

DT_COMPAT_TI_ADS1015 := ti,ads1015

config DT_HAS_TI_ADS1015_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1015))

DT_COMPAT_TI_ADS1112 := ti,ads1112

config DT_HAS_TI_ADS1112_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1112))

DT_COMPAT_TI_ADS1113 := ti,ads1113

config DT_HAS_TI_ADS1113_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1113))

DT_COMPAT_TI_ADS1114 := ti,ads1114

config DT_HAS_TI_ADS1114_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1114))

DT_COMPAT_TI_ADS1115 := ti,ads1115

config DT_HAS_TI_ADS1115_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1115))

DT_COMPAT_TI_ADS1119 := ti,ads1119

config DT_HAS_TI_ADS1119_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1119))

DT_COMPAT_TI_ADS114S08 := ti,ads114s08

config DT_HAS_TI_ADS114S08_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS114S08))

DT_COMPAT_TI_ADS114S0X_GPIO := ti,ads114s0x-gpio

config DT_HAS_TI_ADS114S0X_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS114S0X_GPIO))

DT_COMPAT_TI_ADS131M02 := ti,ads131m02

config DT_HAS_TI_ADS131M02_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS131M02))

DT_COMPAT_TI_ADS7052 := ti,ads7052

config DT_HAS_TI_ADS7052_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS7052))

DT_COMPAT_TI_AM654_TIMER := ti,am654-timer

config DT_HAS_TI_AM654_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_AM654_TIMER))

DT_COMPAT_TI_BOOSTERPACK_HEADER := ti,boosterpack-header

config DT_HAS_TI_BOOSTERPACK_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BOOSTERPACK_HEADER))

DT_COMPAT_TI_BQ24190 := ti,bq24190

config DT_HAS_TI_BQ24190_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BQ24190))

DT_COMPAT_TI_BQ25180 := ti,bq25180

config DT_HAS_TI_BQ25180_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BQ25180))

DT_COMPAT_TI_BQ274XX := ti,bq274xx

config DT_HAS_TI_BQ274XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BQ274XX))

DT_COMPAT_TI_BQ27Z746 := ti,bq27z746

config DT_HAS_TI_BQ27Z746_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BQ27Z746))

DT_COMPAT_TI_CC1200 := ti,cc1200

config DT_HAS_TI_CC1200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC1200))

DT_COMPAT_TI_CC13XX_CC26XX_ADC := ti,cc13xx-cc26xx-adc

config DT_HAS_TI_CC13XX_CC26XX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_ADC))

DT_COMPAT_TI_CC13XX_CC26XX_FLASH_CONTROLLER := ti,cc13xx-cc26xx-flash-controller

config DT_HAS_TI_CC13XX_CC26XX_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_FLASH_CONTROLLER))

DT_COMPAT_TI_CC13XX_CC26XX_GPIO := ti,cc13xx-cc26xx-gpio

config DT_HAS_TI_CC13XX_CC26XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_GPIO))

DT_COMPAT_TI_CC13XX_CC26XX_I2C := ti,cc13xx-cc26xx-i2c

config DT_HAS_TI_CC13XX_CC26XX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_I2C))

DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154 := ti,cc13xx-cc26xx-ieee802154

config DT_HAS_TI_CC13XX_CC26XX_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154))

DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ := ti,cc13xx-cc26xx-ieee802154-subghz

config DT_HAS_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ))

DT_COMPAT_TI_CC13XX_CC26XX_PINCTRL := ti,cc13xx-cc26xx-pinctrl

config DT_HAS_TI_CC13XX_CC26XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_PINCTRL))

DT_COMPAT_TI_CC13XX_CC26XX_RADIO := ti,cc13xx-cc26xx-radio

config DT_HAS_TI_CC13XX_CC26XX_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_RADIO))

DT_COMPAT_TI_CC13XX_CC26XX_RTC_TIMER := ti,cc13xx-cc26xx-rtc-timer

config DT_HAS_TI_CC13XX_CC26XX_RTC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_RTC_TIMER))

DT_COMPAT_TI_CC13XX_CC26XX_SPI := ti,cc13xx-cc26xx-spi

config DT_HAS_TI_CC13XX_CC26XX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_SPI))

DT_COMPAT_TI_CC13XX_CC26XX_TIMER := ti,cc13xx-cc26xx-timer

config DT_HAS_TI_CC13XX_CC26XX_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TIMER))

DT_COMPAT_TI_CC13XX_CC26XX_TIMER_PWM := ti,cc13xx-cc26xx-timer-pwm

config DT_HAS_TI_CC13XX_CC26XX_TIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TIMER_PWM))

DT_COMPAT_TI_CC13XX_CC26XX_TRNG := ti,cc13xx-cc26xx-trng

config DT_HAS_TI_CC13XX_CC26XX_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TRNG))

DT_COMPAT_TI_CC13XX_CC26XX_UART := ti,cc13xx-cc26xx-uart

config DT_HAS_TI_CC13XX_CC26XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_UART))

DT_COMPAT_TI_CC13XX_CC26XX_WATCHDOG := ti,cc13xx-cc26xx-watchdog

config DT_HAS_TI_CC13XX_CC26XX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_WATCHDOG))

DT_COMPAT_TI_CC2520 := ti,cc2520

config DT_HAS_TI_CC2520_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC2520))

DT_COMPAT_TI_CC32XX_ADC := ti,cc32xx-adc

config DT_HAS_TI_CC32XX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_ADC))

DT_COMPAT_TI_CC32XX_GPIO := ti,cc32xx-gpio

config DT_HAS_TI_CC32XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_GPIO))

DT_COMPAT_TI_CC32XX_I2C := ti,cc32xx-i2c

config DT_HAS_TI_CC32XX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_I2C))

DT_COMPAT_TI_CC32XX_PINCTRL := ti,cc32xx-pinctrl

config DT_HAS_TI_CC32XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_PINCTRL))

DT_COMPAT_TI_CC32XX_UART := ti,cc32xx-uart

config DT_HAS_TI_CC32XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_UART))

DT_COMPAT_TI_CC32XX_WATCHDOG := ti,cc32xx-watchdog

config DT_HAS_TI_CC32XX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_WATCHDOG))

DT_COMPAT_TI_DAC43608 := ti,dac43608

config DT_HAS_TI_DAC43608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC43608))

DT_COMPAT_TI_DAC53608 := ti,dac53608

config DT_HAS_TI_DAC53608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC53608))

DT_COMPAT_TI_DAC60508 := ti,dac60508

config DT_HAS_TI_DAC60508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC60508))

DT_COMPAT_TI_DAC70508 := ti,dac70508

config DT_HAS_TI_DAC70508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC70508))

DT_COMPAT_TI_DAC80508 := ti,dac80508

config DT_HAS_TI_DAC80508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC80508))

DT_COMPAT_TI_DACX0501 := ti,dacx0501

config DT_HAS_TI_DACX0501_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DACX0501))

DT_COMPAT_TI_DAVINCI_GPIO := ti,davinci-gpio

config DT_HAS_TI_DAVINCI_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAVINCI_GPIO))

DT_COMPAT_TI_DAVINCI_GPIO_NEXUS := ti,davinci-gpio-nexus

config DT_HAS_TI_DAVINCI_GPIO_NEXUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAVINCI_GPIO_NEXUS))

DT_COMPAT_TI_DP83825 := ti,dp83825

config DT_HAS_TI_DP83825_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DP83825))

DT_COMPAT_TI_DRV2605 := ti,drv2605

config DT_HAS_TI_DRV2605_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DRV2605))

DT_COMPAT_TI_DRV8424 := ti,drv8424

config DT_HAS_TI_DRV8424_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DRV8424))

DT_COMPAT_TI_FDC2X1X := ti,fdc2x1x

config DT_HAS_TI_FDC2X1X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_FDC2X1X))

DT_COMPAT_TI_HDC := ti,hdc

config DT_HAS_TI_HDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC))

DT_COMPAT_TI_HDC2010 := ti,hdc2010

config DT_HAS_TI_HDC2010_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2010))

DT_COMPAT_TI_HDC2021 := ti,hdc2021

config DT_HAS_TI_HDC2021_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2021))

DT_COMPAT_TI_HDC2022 := ti,hdc2022

config DT_HAS_TI_HDC2022_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2022))

DT_COMPAT_TI_HDC2080 := ti,hdc2080

config DT_HAS_TI_HDC2080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2080))

DT_COMPAT_TI_INA219 := ti,ina219

config DT_HAS_TI_INA219_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA219))

DT_COMPAT_TI_INA226 := ti,ina226

config DT_HAS_TI_INA226_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA226))

DT_COMPAT_TI_INA230 := ti,ina230

config DT_HAS_TI_INA230_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA230))

DT_COMPAT_TI_INA236 := ti,ina236

config DT_HAS_TI_INA236_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA236))

DT_COMPAT_TI_INA237 := ti,ina237

config DT_HAS_TI_INA237_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA237))

DT_COMPAT_TI_INA3221 := ti,ina3221

config DT_HAS_TI_INA3221_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA3221))

DT_COMPAT_TI_K3_PINCTRL := ti,k3-pinctrl

config DT_HAS_TI_K3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_K3_PINCTRL))

DT_COMPAT_TI_LMP90077 := ti,lmp90077

config DT_HAS_TI_LMP90077_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90077))

DT_COMPAT_TI_LMP90078 := ti,lmp90078

config DT_HAS_TI_LMP90078_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90078))

DT_COMPAT_TI_LMP90079 := ti,lmp90079

config DT_HAS_TI_LMP90079_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90079))

DT_COMPAT_TI_LMP90080 := ti,lmp90080

config DT_HAS_TI_LMP90080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90080))

DT_COMPAT_TI_LMP90097 := ti,lmp90097

config DT_HAS_TI_LMP90097_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90097))

DT_COMPAT_TI_LMP90098 := ti,lmp90098

config DT_HAS_TI_LMP90098_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90098))

DT_COMPAT_TI_LMP90099 := ti,lmp90099

config DT_HAS_TI_LMP90099_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90099))

DT_COMPAT_TI_LMP90100 := ti,lmp90100

config DT_HAS_TI_LMP90100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90100))

DT_COMPAT_TI_LMP90XXX_GPIO := ti,lmp90xxx-gpio

config DT_HAS_TI_LMP90XXX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90XXX_GPIO))

DT_COMPAT_TI_LP3943 := ti,lp3943

config DT_HAS_TI_LP3943_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP3943))

DT_COMPAT_TI_LP5009 := ti,lp5009

config DT_HAS_TI_LP5009_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5009))

DT_COMPAT_TI_LP5012 := ti,lp5012

config DT_HAS_TI_LP5012_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5012))

DT_COMPAT_TI_LP5018 := ti,lp5018

config DT_HAS_TI_LP5018_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5018))

DT_COMPAT_TI_LP5024 := ti,lp5024

config DT_HAS_TI_LP5024_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5024))

DT_COMPAT_TI_LP5030 := ti,lp5030

config DT_HAS_TI_LP5030_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5030))

DT_COMPAT_TI_LP5036 := ti,lp5036

config DT_HAS_TI_LP5036_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5036))

DT_COMPAT_TI_LP5562 := ti,lp5562

config DT_HAS_TI_LP5562_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5562))

DT_COMPAT_TI_LP5569 := ti,lp5569

config DT_HAS_TI_LP5569_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5569))

DT_COMPAT_TI_MSP432P4XX_UART := ti,msp432p4xx-uart

config DT_HAS_TI_MSP432P4XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_MSP432P4XX_UART))

DT_COMPAT_TI_OPT3001 := ti,opt3001

config DT_HAS_TI_OPT3001_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_OPT3001))

DT_COMPAT_TI_SN74HC595 := ti,sn74hc595

config DT_HAS_TI_SN74HC595_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_SN74HC595))

DT_COMPAT_TI_STELLARIS_ETHERNET := ti,stellaris-ethernet

config DT_HAS_TI_STELLARIS_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_ETHERNET))

DT_COMPAT_TI_STELLARIS_FLASH_CONTROLLER := ti,stellaris-flash-controller

config DT_HAS_TI_STELLARIS_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_FLASH_CONTROLLER))

DT_COMPAT_TI_STELLARIS_GPIO := ti,stellaris-gpio

config DT_HAS_TI_STELLARIS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_GPIO))

DT_COMPAT_TI_STELLARIS_UART := ti,stellaris-uart

config DT_HAS_TI_STELLARIS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_UART))

DT_COMPAT_TI_TAS6422DAC := ti,tas6422dac

config DT_HAS_TI_TAS6422DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TAS6422DAC))

DT_COMPAT_TI_TCA6424A := ti,tca6424a

config DT_HAS_TI_TCA6424A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA6424A))

DT_COMPAT_TI_TCA9538 := ti,tca9538

config DT_HAS_TI_TCA9538_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9538))

DT_COMPAT_TI_TCA9544A := ti,tca9544a

config DT_HAS_TI_TCA9544A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9544A))

DT_COMPAT_TI_TCA9546A := ti,tca9546a

config DT_HAS_TI_TCA9546A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9546A))

DT_COMPAT_TI_TCA9548A := ti,tca9548a

config DT_HAS_TI_TCA9548A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9548A))

DT_COMPAT_TI_TCAN4X5X := ti,tcan4x5x

config DT_HAS_TI_TCAN4X5X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCAN4X5X))

DT_COMPAT_TI_TLA2021 := ti,tla2021

config DT_HAS_TI_TLA2021_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLA2021))

DT_COMPAT_TI_TLA2022 := ti,tla2022

config DT_HAS_TI_TLA2022_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLA2022))

DT_COMPAT_TI_TLA2024 := ti,tla2024

config DT_HAS_TI_TLA2024_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLA2024))

DT_COMPAT_TI_TLC59108 := ti,tlc59108

config DT_HAS_TI_TLC59108_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLC59108))

DT_COMPAT_TI_TLC5971 := ti,tlc5971

config DT_HAS_TI_TLC5971_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLC5971))

DT_COMPAT_TI_TLC59731 := ti,tlc59731

config DT_HAS_TI_TLC59731_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLC59731))

DT_COMPAT_TI_TLV320DAC := ti,tlv320dac

config DT_HAS_TI_TLV320DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLV320DAC))

DT_COMPAT_TI_TMAG3001 := ti,tmag3001

config DT_HAS_TI_TMAG3001_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMAG3001))

DT_COMPAT_TI_TMAG5170 := ti,tmag5170

config DT_HAS_TI_TMAG5170_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMAG5170))

DT_COMPAT_TI_TMAG5273 := ti,tmag5273

config DT_HAS_TI_TMAG5273_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMAG5273))

DT_COMPAT_TI_TMP007 := ti,tmp007

config DT_HAS_TI_TMP007_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP007))

DT_COMPAT_TI_TMP1075 := ti,tmp1075

config DT_HAS_TI_TMP1075_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP1075))

DT_COMPAT_TI_TMP108 := ti,tmp108

config DT_HAS_TI_TMP108_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP108))

DT_COMPAT_TI_TMP112 := ti,tmp112

config DT_HAS_TI_TMP112_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP112))

DT_COMPAT_TI_TMP114 := ti,tmp114

config DT_HAS_TI_TMP114_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP114))

DT_COMPAT_TI_TMP116 := ti,tmp116

config DT_HAS_TI_TMP116_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP116))

DT_COMPAT_TI_TMP116_EEPROM := ti,tmp116-eeprom

config DT_HAS_TI_TMP116_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP116_EEPROM))

DT_COMPAT_TI_TMP435 := ti,tmp435

config DT_HAS_TI_TMP435_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP435))

DT_COMPAT_TI_TPS382X := ti,tps382x

config DT_HAS_TI_TPS382X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TPS382X))

DT_COMPAT_TI_VIM := ti,vim

config DT_HAS_TI_VIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_VIM))

DT_COMPAT_U_BLOX_LARA_R6 := u-blox,lara-r6

config DT_HAS_U_BLOX_LARA_R6_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_U_BLOX_LARA_R6))

DT_COMPAT_U_BLOX_M8 := u-blox,m8

config DT_HAS_U_BLOX_M8_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_U_BLOX_M8))

DT_COMPAT_U_BLOX_SARA_R4 := u-blox,sara-r4

config DT_HAS_U_BLOX_SARA_R4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_U_BLOX_SARA_R4))

DT_COMPAT_U_BLOX_SARA_R5 := u-blox,sara-r5

config DT_HAS_U_BLOX_SARA_R5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_U_BLOX_SARA_R5))

DT_COMPAT_UART_HDLC_RCP_IF := uart,hdlc-rcp-if

config DT_HAS_UART_HDLC_RCP_IF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_UART_HDLC_RCP_IF))

DT_COMPAT_ULTRACHIP_UC8175 := ultrachip,uc8175

config DT_HAS_ULTRACHIP_UC8175_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ULTRACHIP_UC8175))

DT_COMPAT_ULTRACHIP_UC8176 := ultrachip,uc8176

config DT_HAS_ULTRACHIP_UC8176_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ULTRACHIP_UC8176))

DT_COMPAT_ULTRACHIP_UC8179 := ultrachip,uc8179

config DT_HAS_ULTRACHIP_UC8179_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ULTRACHIP_UC8179))

DT_COMPAT_USB_AUDIO := usb-audio

config DT_HAS_USB_AUDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO))

DT_COMPAT_USB_AUDIO_FEATURE_VOLUME := usb-audio-feature-volume

config DT_HAS_USB_AUDIO_FEATURE_VOLUME_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_FEATURE_VOLUME))

DT_COMPAT_USB_AUDIO_HP := usb-audio-hp

config DT_HAS_USB_AUDIO_HP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_HP))

DT_COMPAT_USB_AUDIO_HS := usb-audio-hs

config DT_HAS_USB_AUDIO_HS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_HS))

DT_COMPAT_USB_AUDIO_MIC := usb-audio-mic

config DT_HAS_USB_AUDIO_MIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_MIC))

DT_COMPAT_USB_C_CONNECTOR := usb-c-connector

config DT_HAS_USB_C_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_C_CONNECTOR))

DT_COMPAT_USB_NOP_XCEIV := usb-nop-xceiv

config DT_HAS_USB_NOP_XCEIV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_NOP_XCEIV))

DT_COMPAT_USB_ULPI_PHY := usb-ulpi-phy

config DT_HAS_USB_ULPI_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_ULPI_PHY))

DT_COMPAT_VISHAY_VCNL36825T := vishay,vcnl36825t

config DT_HAS_VISHAY_VCNL36825T_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VCNL36825T))

DT_COMPAT_VISHAY_VCNL4040 := vishay,vcnl4040

config DT_HAS_VISHAY_VCNL4040_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VCNL4040))

DT_COMPAT_VISHAY_VEML6031 := vishay,veml6031

config DT_HAS_VISHAY_VEML6031_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VEML6031))

DT_COMPAT_VISHAY_VEML7700 := vishay,veml7700

config DT_HAS_VISHAY_VEML7700_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VEML7700))

DT_COMPAT_VND_ADC := vnd,adc

config DT_HAS_VND_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ADC))

DT_COMPAT_VND_ADC_TEMP_SENSOR := vnd,adc-temp-sensor

config DT_HAS_VND_ADC_TEMP_SENSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ADC_TEMP_SENSOR))

DT_COMPAT_VND_ARRAY_HOLDER := vnd,array-holder

config DT_HAS_VND_ARRAY_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ARRAY_HOLDER))

DT_COMPAT_VND_BUSY_SIM := vnd,busy-sim

config DT_HAS_VND_BUSY_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_BUSY_SIM))

DT_COMPAT_VND_CAN_CONTROLLER := vnd,can-controller

config DT_HAS_VND_CAN_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CAN_CONTROLLER))

DT_COMPAT_VND_CAN_TRANSCEIVER := vnd,can-transceiver

config DT_HAS_VND_CAN_TRANSCEIVER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CAN_TRANSCEIVER))

DT_COMPAT_VND_CHILD_BINDINGS := vnd,child-bindings

config DT_HAS_VND_CHILD_BINDINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CHILD_BINDINGS))

DT_COMPAT_VND_CLOCK := vnd,clock

config DT_HAS_VND_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CLOCK))

DT_COMPAT_VND_CPU_INTC := vnd,cpu-intc

config DT_HAS_VND_CPU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CPU_INTC))

DT_COMPAT_VND_DAC := vnd,dac

config DT_HAS_VND_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DAC))

DT_COMPAT_VND_DEVICE_WITH_PROPS := vnd,device-with-props

config DT_HAS_VND_DEVICE_WITH_PROPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DEVICE_WITH_PROPS))

DT_COMPAT_VND_DISABLED_COMPAT := vnd,disabled-compat

config DT_HAS_VND_DISABLED_COMPAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DISABLED_COMPAT))

DT_COMPAT_VND_DMA := vnd,dma

config DT_HAS_VND_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DMA))

DT_COMPAT_VND_EMUL_TESTER := vnd,emul-tester

config DT_HAS_VND_EMUL_TESTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_EMUL_TESTER))

DT_COMPAT_VND_ENUM_HOLDER := vnd,enum-holder

config DT_HAS_VND_ENUM_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_HOLDER))

DT_COMPAT_VND_ENUM_HOLDER_INST := vnd,enum-holder-inst

config DT_HAS_VND_ENUM_HOLDER_INST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_HOLDER_INST))

DT_COMPAT_VND_ENUM_INT_ARRAY_HOLDER := vnd,enum-int-array-holder

config DT_HAS_VND_ENUM_INT_ARRAY_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_INT_ARRAY_HOLDER))

DT_COMPAT_VND_ENUM_INT_REQUIRED_FALSE_HOLDER := vnd,enum-int-required-false-holder

config DT_HAS_VND_ENUM_INT_REQUIRED_FALSE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_INT_REQUIRED_FALSE_HOLDER))

DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER := vnd,enum-required-false-holder

config DT_HAS_VND_ENUM_REQUIRED_FALSE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER))

DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER_INST := vnd,enum-required-false-holder-inst

config DT_HAS_VND_ENUM_REQUIRED_FALSE_HOLDER_INST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER_INST))

DT_COMPAT_VND_ENUM_STRING_ARRAY_HOLDER := vnd,enum-string-array-holder

config DT_HAS_VND_ENUM_STRING_ARRAY_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_STRING_ARRAY_HOLDER))

DT_COMPAT_VND_ETHERNET := vnd,ethernet

config DT_HAS_VND_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ETHERNET))

DT_COMPAT_VND_GPIO := vnd,gpio

config DT_HAS_VND_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO))

DT_COMPAT_VND_GPIO_DEVICE := vnd,gpio-device

config DT_HAS_VND_GPIO_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_DEVICE))

DT_COMPAT_VND_GPIO_EXPANDER := vnd,gpio-expander

config DT_HAS_VND_GPIO_EXPANDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_EXPANDER))

DT_COMPAT_VND_GPIO_INTC_DEVICE := vnd,gpio-intc-device

config DT_HAS_VND_GPIO_INTC_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_INTC_DEVICE))

DT_COMPAT_VND_GPIO_ONE_CELL := vnd,gpio-one-cell

config DT_HAS_VND_GPIO_ONE_CELL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_ONE_CELL))

DT_COMPAT_VND_GREAT_GRANDCHILD_BINDINGS := vnd,great-grandchild-bindings

config DT_HAS_VND_GREAT_GRANDCHILD_BINDINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GREAT_GRANDCHILD_BINDINGS))

DT_COMPAT_VND_I2C := vnd,i2c

config DT_HAS_VND_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C))

DT_COMPAT_VND_I2C_DEVICE := vnd,i2c-device

config DT_HAS_VND_I2C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C_DEVICE))

DT_COMPAT_VND_I2C_MUX := vnd,i2c-mux

config DT_HAS_VND_I2C_MUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C_MUX))

DT_COMPAT_VND_I2S := vnd,i2s

config DT_HAS_VND_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2S))

DT_COMPAT_VND_I3C := vnd,i3c

config DT_HAS_VND_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C))

DT_COMPAT_VND_I3C_DEVICE := vnd,i3c-device

config DT_HAS_VND_I3C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C_DEVICE))

DT_COMPAT_VND_I3C_I2C_DEVICE := vnd,i3c-i2c-device

config DT_HAS_VND_I3C_I2C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C_I2C_DEVICE))

DT_COMPAT_VND_IEEE802154 := vnd,ieee802154

config DT_HAS_VND_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_IEEE802154))

DT_COMPAT_VND_INPUT_DEVICE := vnd,input-device

config DT_HAS_VND_INPUT_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INPUT_DEVICE))

DT_COMPAT_VND_INTC := vnd,intc

config DT_HAS_VND_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INTC))

DT_COMPAT_VND_INTERRUPT_HOLDER := vnd,interrupt-holder

config DT_HAS_VND_INTERRUPT_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INTERRUPT_HOLDER))

DT_COMPAT_VND_INTERRUPT_HOLDER_EXTENDED := vnd,interrupt-holder-extended

config DT_HAS_VND_INTERRUPT_HOLDER_EXTENDED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INTERRUPT_HOLDER_EXTENDED))

DT_COMPAT_VND_MBOX := vnd,mbox

config DT_HAS_VND_MBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MBOX))

DT_COMPAT_VND_MBOX_CONSUMER := vnd,mbox-consumer

config DT_HAS_VND_MBOX_CONSUMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MBOX_CONSUMER))

DT_COMPAT_VND_MBOX_ZERO_CELL := vnd,mbox-zero-cell

config DT_HAS_VND_MBOX_ZERO_CELL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MBOX_ZERO_CELL))

DT_COMPAT_VND_MEMORY_ATTR := vnd,memory-attr

config DT_HAS_VND_MEMORY_ATTR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MEMORY_ATTR))

DT_COMPAT_VND_MIPI_DSI := vnd,mipi-dsi

config DT_HAS_VND_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MIPI_DSI))

DT_COMPAT_VND_NON_DEPRECATED_LABEL := vnd,non-deprecated-label

config DT_HAS_VND_NON_DEPRECATED_LABEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_NON_DEPRECATED_LABEL))

DT_COMPAT_VND_PCIE := vnd,pcie

config DT_HAS_VND_PCIE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PCIE))

DT_COMPAT_VND_PHANDLE_HOLDER := vnd,phandle-holder

config DT_HAS_VND_PHANDLE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PHANDLE_HOLDER))

DT_COMPAT_VND_PINCTRL := vnd,pinctrl

config DT_HAS_VND_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL))

DT_COMPAT_VND_PINCTRL_DEVICE := vnd,pinctrl-device

config DT_HAS_VND_PINCTRL_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL_DEVICE))

DT_COMPAT_VND_PINCTRL_TEST := vnd,pinctrl-test

config DT_HAS_VND_PINCTRL_TEST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL_TEST))

DT_COMPAT_VND_PWM := vnd,pwm

config DT_HAS_VND_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PWM))

DT_COMPAT_VND_REG_HOLDER := vnd,reg-holder

config DT_HAS_VND_REG_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_REG_HOLDER))

DT_COMPAT_VND_REG_HOLDER_64 := vnd,reg-holder-64

config DT_HAS_VND_REG_HOLDER_64_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_REG_HOLDER_64))

DT_COMPAT_VND_RESERVED_COMPAT := vnd,reserved-compat

config DT_HAS_VND_RESERVED_COMPAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_RESERVED_COMPAT))

DT_COMPAT_VND_RESET := vnd,reset

config DT_HAS_VND_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_RESET))

DT_COMPAT_VND_SERIAL := vnd,serial

config DT_HAS_VND_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SERIAL))

DT_COMPAT_VND_SPI := vnd,spi

config DT_HAS_VND_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI))

DT_COMPAT_VND_SPI_DEVICE := vnd,spi-device

config DT_HAS_VND_SPI_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI_DEVICE))

DT_COMPAT_VND_SPI_DEVICE_2 := vnd,spi-device-2

config DT_HAS_VND_SPI_DEVICE_2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI_DEVICE_2))

DT_COMPAT_VND_STRING := vnd,string

config DT_HAS_VND_STRING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING))

DT_COMPAT_VND_STRING_ARRAY := vnd,string-array

config DT_HAS_VND_STRING_ARRAY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_ARRAY))

DT_COMPAT_VND_STRING_ARRAY_TOKEN := vnd,string-array-token

config DT_HAS_VND_STRING_ARRAY_TOKEN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_ARRAY_TOKEN))

DT_COMPAT_VND_STRING_ARRAY_UNQUOTED := vnd,string-array-unquoted

config DT_HAS_VND_STRING_ARRAY_UNQUOTED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_ARRAY_UNQUOTED))

DT_COMPAT_VND_STRING_TOKEN := vnd,string-token

config DT_HAS_VND_STRING_TOKEN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_TOKEN))

DT_COMPAT_VND_STRING_UNQUOTED := vnd,string-unquoted

config DT_HAS_VND_STRING_UNQUOTED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_UNQUOTED))

DT_COMPAT_VND_VIDEO_MULTI_PORT := vnd,video-multi-port

config DT_HAS_VND_VIDEO_MULTI_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_VIDEO_MULTI_PORT))

DT_COMPAT_VND_VIDEO_SINGLE_PORT := vnd,video-single-port

config DT_HAS_VND_VIDEO_SINGLE_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_VIDEO_SINGLE_PORT))

DT_COMPAT_VND_W1 := vnd,w1

config DT_HAS_VND_W1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_W1))

DT_COMPAT_VOLTAGE_DIVIDER := voltage-divider

config DT_HAS_VOLTAGE_DIVIDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VOLTAGE_DIVIDER))

DT_COMPAT_WCH_AFIO := wch,afio

config DT_HAS_WCH_AFIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_AFIO))

DT_COMPAT_WCH_CH32V00X_HSE_CLOCK := wch,ch32v00x-hse-clock

config DT_HAS_WCH_CH32V00X_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_CH32V00X_HSE_CLOCK))

DT_COMPAT_WCH_CH32V00X_HSI_CLOCK := wch,ch32v00x-hsi-clock

config DT_HAS_WCH_CH32V00X_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_CH32V00X_HSI_CLOCK))

DT_COMPAT_WCH_CH32V00X_PLL_CLOCK := wch,ch32v00x-pll-clock

config DT_HAS_WCH_CH32V00X_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_CH32V00X_PLL_CLOCK))

DT_COMPAT_WCH_GPIO := wch,gpio

config DT_HAS_WCH_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_GPIO))

DT_COMPAT_WCH_PFIC := wch,pfic

config DT_HAS_WCH_PFIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_PFIC))

DT_COMPAT_WCH_QINGKE_V2 := wch,qingke-v2

config DT_HAS_WCH_QINGKE_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_QINGKE_V2))

DT_COMPAT_WCH_RCC := wch,rcc

config DT_HAS_WCH_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_RCC))

DT_COMPAT_WCH_SYSTICK := wch,systick

config DT_HAS_WCH_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_SYSTICK))

DT_COMPAT_WCH_USART := wch,usart

config DT_HAS_WCH_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WCH_USART))

DT_COMPAT_WE_WSEN_HIDS_2525020210002 := we,wsen-hids-2525020210002

config DT_HAS_WE_WSEN_HIDS_2525020210002_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_HIDS_2525020210002))

DT_COMPAT_WEACT_DCMI_CAMERA_CONNECTOR := weact,dcmi-camera-connector

config DT_HAS_WEACT_DCMI_CAMERA_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WEACT_DCMI_CAMERA_CONNECTOR))

DT_COMPAT_WINSEN_MHZ19B := winsen,mhz19b

config DT_HAS_WINSEN_MHZ19B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WINSEN_MHZ19B))

DT_COMPAT_WIZNET_W5500 := wiznet,w5500

config DT_HAS_WIZNET_W5500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WIZNET_W5500))

DT_COMPAT_WNC_M14A2A := wnc,m14a2a

config DT_HAS_WNC_M14A2A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WNC_M14A2A))

DT_COMPAT_WOLFSON_WM8904 := wolfson,wm8904

config DT_HAS_WOLFSON_WM8904_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WOLFSON_WM8904))

DT_COMPAT_WORLDSEMI_WS2812_GPIO := worldsemi,ws2812-gpio

config DT_HAS_WORLDSEMI_WS2812_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_GPIO))

DT_COMPAT_WORLDSEMI_WS2812_I2S := worldsemi,ws2812-i2s

config DT_HAS_WORLDSEMI_WS2812_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_I2S))

DT_COMPAT_WORLDSEMI_WS2812_RPI_PICO_PIO := worldsemi,ws2812-rpi_pico-pio

config DT_HAS_WORLDSEMI_WS2812_RPI_PICO_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_RPI_PICO_PIO))

DT_COMPAT_WORLDSEMI_WS2812_SPI := worldsemi,ws2812-spi

config DT_HAS_WORLDSEMI_WS2812_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_SPI))

DT_COMPAT_X_POWERS_AXP192 := x-powers,axp192

config DT_HAS_X_POWERS_AXP192_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192))

DT_COMPAT_X_POWERS_AXP192_GPIO := x-powers,axp192-gpio

config DT_HAS_X_POWERS_AXP192_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192_GPIO))

DT_COMPAT_X_POWERS_AXP192_REGULATOR := x-powers,axp192-regulator

config DT_HAS_X_POWERS_AXP192_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192_REGULATOR))

DT_COMPAT_XEN_HVC_CONSOLEIO := xen,hvc-consoleio

config DT_HAS_XEN_HVC_CONSOLEIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XEN_HVC_CONSOLEIO))

DT_COMPAT_XEN_HVC_UART := xen,hvc-uart

config DT_HAS_XEN_HVC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XEN_HVC_UART))

DT_COMPAT_XEN_XEN := xen,xen

config DT_HAS_XEN_XEN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XEN_XEN))

DT_COMPAT_XLNX_AXI_DMA_1_00_A := xlnx,axi-dma-1.00.a

config DT_HAS_XLNX_AXI_DMA_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_AXI_DMA_1_00_A))

DT_COMPAT_XLNX_ETH_DMA := xlnx,eth-dma

config DT_HAS_XLNX_ETH_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_ETH_DMA))

DT_COMPAT_XLNX_FPGA := xlnx,fpga

config DT_HAS_XLNX_FPGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_FPGA))

DT_COMPAT_XLNX_GEM := xlnx,gem

config DT_HAS_XLNX_GEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_GEM))

DT_COMPAT_XLNX_PINCTRL_ZYNQ := xlnx,pinctrl-zynq

config DT_HAS_XLNX_PINCTRL_ZYNQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PINCTRL_ZYNQ))

DT_COMPAT_XLNX_PINCTRL_ZYNQMP := xlnx,pinctrl-zynqmp

config DT_HAS_XLNX_PINCTRL_ZYNQMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PINCTRL_ZYNQMP))

DT_COMPAT_XLNX_PS_GPIO := xlnx,ps-gpio

config DT_HAS_XLNX_PS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PS_GPIO))

DT_COMPAT_XLNX_PS_GPIO_BANK := xlnx,ps-gpio-bank

config DT_HAS_XLNX_PS_GPIO_BANK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PS_GPIO_BANK))

DT_COMPAT_XLNX_TTCPS := xlnx,ttcps

config DT_HAS_XLNX_TTCPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_TTCPS))

DT_COMPAT_XLNX_XPS_GPIO_1_00_A := xlnx,xps-gpio-1.00.a

config DT_HAS_XLNX_XPS_GPIO_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_GPIO_1_00_A))

DT_COMPAT_XLNX_XPS_GPIO_1_00_A_GPIO2 := xlnx,xps-gpio-1.00.a-gpio2

config DT_HAS_XLNX_XPS_GPIO_1_00_A_GPIO2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_GPIO_1_00_A_GPIO2))

DT_COMPAT_XLNX_XPS_IIC_2_00_A := xlnx,xps-iic-2.00.a

config DT_HAS_XLNX_XPS_IIC_2_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_IIC_2_00_A))

DT_COMPAT_XLNX_XPS_IIC_2_1 := xlnx,xps-iic-2.1

config DT_HAS_XLNX_XPS_IIC_2_1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_IIC_2_1))

DT_COMPAT_XLNX_XPS_SPI_2_00_A := xlnx,xps-spi-2.00.a

config DT_HAS_XLNX_XPS_SPI_2_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_SPI_2_00_A))

DT_COMPAT_XLNX_XPS_TIMEBASE_WDT_1_00_A := xlnx,xps-timebase-wdt-1.00.a

config DT_HAS_XLNX_XPS_TIMEBASE_WDT_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMEBASE_WDT_1_00_A))

DT_COMPAT_XLNX_XPS_TIMER_1_00_A := xlnx,xps-timer-1.00.a

config DT_HAS_XLNX_XPS_TIMER_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMER_1_00_A))

DT_COMPAT_XLNX_XPS_TIMER_1_00_A_PWM := xlnx,xps-timer-1.00.a-pwm

config DT_HAS_XLNX_XPS_TIMER_1_00_A_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMER_1_00_A_PWM))

DT_COMPAT_XLNX_XPS_UARTLITE_1_00_A := xlnx,xps-uartlite-1.00.a

config DT_HAS_XLNX_XPS_UARTLITE_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_UARTLITE_1_00_A))

DT_COMPAT_XLNX_XUARTPS := xlnx,xuartps

config DT_HAS_XLNX_XUARTPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XUARTPS))

DT_COMPAT_XLNX_ZYNQ_OCM := xlnx,zynq-ocm

config DT_HAS_XLNX_ZYNQ_OCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_ZYNQ_OCM))

DT_COMPAT_XLNX_ZYNQMP_IPI_MAILBOX := xlnx,zynqmp-ipi-mailbox

config DT_HAS_XLNX_ZYNQMP_IPI_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_ZYNQMP_IPI_MAILBOX))

DT_COMPAT_XPTEK_XPT2046 := xptek,xpt2046

config DT_HAS_XPTEK_XPT2046_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XPTEK_XPT2046))

DT_COMPAT_ZEPHYR_ADC_EMUL := zephyr,adc-emul

config DT_HAS_ZEPHYR_ADC_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_ADC_EMUL))

DT_COMPAT_ZEPHYR_BBRAM_EMUL := zephyr,bbram-emul

config DT_HAS_ZEPHYR_BBRAM_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BBRAM_EMUL))

DT_COMPAT_ZEPHYR_BT_HCI_3WIRE_UART := zephyr,bt-hci-3wire-uart

config DT_HAS_ZEPHYR_BT_HCI_3WIRE_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_3WIRE_UART))

DT_COMPAT_ZEPHYR_BT_HCI_ENTROPY := zephyr,bt-hci-entropy

config DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_ENTROPY))

DT_COMPAT_ZEPHYR_BT_HCI_IPC := zephyr,bt-hci-ipc

config DT_HAS_ZEPHYR_BT_HCI_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_IPC))

DT_COMPAT_ZEPHYR_BT_HCI_LL_SW_SPLIT := zephyr,bt-hci-ll-sw-split

config DT_HAS_ZEPHYR_BT_HCI_LL_SW_SPLIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_LL_SW_SPLIT))

DT_COMPAT_ZEPHYR_BT_HCI_SPI := zephyr,bt-hci-spi

config DT_HAS_ZEPHYR_BT_HCI_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_SPI))

DT_COMPAT_ZEPHYR_BT_HCI_SPI_SLAVE := zephyr,bt-hci-spi-slave

config DT_HAS_ZEPHYR_BT_HCI_SPI_SLAVE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_SPI_SLAVE))

DT_COMPAT_ZEPHYR_BT_HCI_UART := zephyr,bt-hci-uart

config DT_HAS_ZEPHYR_BT_HCI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_UART))

DT_COMPAT_ZEPHYR_BT_HCI_USERCHAN := zephyr,bt-hci-userchan

config DT_HAS_ZEPHYR_BT_HCI_USERCHAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_USERCHAN))

DT_COMPAT_ZEPHYR_CAN_LOOPBACK := zephyr,can-loopback

config DT_HAS_ZEPHYR_CAN_LOOPBACK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CAN_LOOPBACK))

DT_COMPAT_ZEPHYR_CDC_ACM_UART := zephyr,cdc-acm-uart

config DT_HAS_ZEPHYR_CDC_ACM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CDC_ACM_UART))

DT_COMPAT_ZEPHYR_CDC_ECM_ETHERNET := zephyr,cdc-ecm-ethernet

config DT_HAS_ZEPHYR_CDC_ECM_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CDC_ECM_ETHERNET))

DT_COMPAT_ZEPHYR_CDC_NCM_ETHERNET := zephyr,cdc-ncm-ethernet

config DT_HAS_ZEPHYR_CDC_NCM_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CDC_NCM_ETHERNET))

DT_COMPAT_ZEPHYR_COREDUMP := zephyr,coredump

config DT_HAS_ZEPHYR_COREDUMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_COREDUMP))

DT_COMPAT_ZEPHYR_COUNTER_WATCHDOG := zephyr,counter-watchdog

config DT_HAS_ZEPHYR_COUNTER_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_COUNTER_WATCHDOG))

DT_COMPAT_ZEPHYR_DEVMUX := zephyr,devmux

config DT_HAS_ZEPHYR_DEVMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_DEVMUX))

DT_COMPAT_ZEPHYR_DMA_EMUL := zephyr,dma-emul

config DT_HAS_ZEPHYR_DMA_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_DMA_EMUL))

DT_COMPAT_ZEPHYR_DUMMY_DC := zephyr,dummy-dc

config DT_HAS_ZEPHYR_DUMMY_DC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_DUMMY_DC))

DT_COMPAT_ZEPHYR_EMU_EEPROM := zephyr,emu-eeprom

config DT_HAS_ZEPHYR_EMU_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_EMU_EEPROM))

DT_COMPAT_ZEPHYR_ESPI_EMUL_CONTROLLER := zephyr,espi-emul-controller

config DT_HAS_ZEPHYR_ESPI_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_ESPI_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_FAKE_CAN := zephyr,fake-can

config DT_HAS_ZEPHYR_FAKE_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_CAN))

DT_COMPAT_ZEPHYR_FAKE_COMP := zephyr,fake-comp

config DT_HAS_ZEPHYR_FAKE_COMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_COMP))

DT_COMPAT_ZEPHYR_FAKE_EEPROM := zephyr,fake-eeprom

config DT_HAS_ZEPHYR_FAKE_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_EEPROM))

DT_COMPAT_ZEPHYR_FAKE_PWM := zephyr,fake-pwm

config DT_HAS_ZEPHYR_FAKE_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_PWM))

DT_COMPAT_ZEPHYR_FAKE_REGULATOR := zephyr,fake-regulator

config DT_HAS_ZEPHYR_FAKE_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_REGULATOR))

DT_COMPAT_ZEPHYR_FAKE_RTC := zephyr,fake-rtc

config DT_HAS_ZEPHYR_FAKE_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_RTC))

DT_COMPAT_ZEPHYR_FAKE_STEPPER := zephyr,fake-stepper

config DT_HAS_ZEPHYR_FAKE_STEPPER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_STEPPER))

DT_COMPAT_ZEPHYR_FLASH_DISK := zephyr,flash-disk

config DT_HAS_ZEPHYR_FLASH_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FLASH_DISK))

DT_COMPAT_ZEPHYR_FSTAB := zephyr,fstab

config DT_HAS_ZEPHYR_FSTAB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FSTAB))

DT_COMPAT_ZEPHYR_FSTAB_LITTLEFS := zephyr,fstab,littlefs

config DT_HAS_ZEPHYR_FSTAB_LITTLEFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FSTAB_LITTLEFS))

DT_COMPAT_ZEPHYR_FUEL_GAUGE_COMPOSITE := zephyr,fuel-gauge-composite

config DT_HAS_ZEPHYR_FUEL_GAUGE_COMPOSITE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FUEL_GAUGE_COMPOSITE))

DT_COMPAT_ZEPHYR_GNSS_EMUL := zephyr,gnss-emul

config DT_HAS_ZEPHYR_GNSS_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GNSS_EMUL))

DT_COMPAT_ZEPHYR_GPIO_EMUL := zephyr,gpio-emul

config DT_HAS_ZEPHYR_GPIO_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GPIO_EMUL))

DT_COMPAT_ZEPHYR_GPIO_EMUL_SDL := zephyr,gpio-emul-sdl

config DT_HAS_ZEPHYR_GPIO_EMUL_SDL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GPIO_EMUL_SDL))

DT_COMPAT_ZEPHYR_GPIO_STEPPER := zephyr,gpio-stepper

config DT_HAS_ZEPHYR_GPIO_STEPPER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GPIO_STEPPER))

DT_COMPAT_ZEPHYR_HID_DEVICE := zephyr,hid-device

config DT_HAS_ZEPHYR_HID_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_HID_DEVICE))

DT_COMPAT_ZEPHYR_I2C_DUMP_ALLOWLIST := zephyr,i2c-dump-allowlist

config DT_HAS_ZEPHYR_I2C_DUMP_ALLOWLIST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_I2C_DUMP_ALLOWLIST))

DT_COMPAT_ZEPHYR_I2C_EMUL_CONTROLLER := zephyr,i2c-emul-controller

config DT_HAS_ZEPHYR_I2C_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_I2C_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_I2C_TARGET_EEPROM := zephyr,i2c-target-eeprom

config DT_HAS_ZEPHYR_I2C_TARGET_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_I2C_TARGET_EEPROM))

DT_COMPAT_ZEPHYR_IEEE802154_UART_PIPE := zephyr,ieee802154-uart-pipe

config DT_HAS_ZEPHYR_IEEE802154_UART_PIPE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IEEE802154_UART_PIPE))

DT_COMPAT_ZEPHYR_INPUT_DOUBLE_TAP := zephyr,input-double-tap

config DT_HAS_ZEPHYR_INPUT_DOUBLE_TAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_INPUT_DOUBLE_TAP))

DT_COMPAT_ZEPHYR_INPUT_LONGPRESS := zephyr,input-longpress

config DT_HAS_ZEPHYR_INPUT_LONGPRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_INPUT_LONGPRESS))

DT_COMPAT_ZEPHYR_INPUT_SDL_TOUCH := zephyr,input-sdl-touch

config DT_HAS_ZEPHYR_INPUT_SDL_TOUCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_INPUT_SDL_TOUCH))

DT_COMPAT_ZEPHYR_IPC_ICBMSG := zephyr,ipc-icbmsg

config DT_HAS_ZEPHYR_IPC_ICBMSG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICBMSG))

DT_COMPAT_ZEPHYR_IPC_ICMSG := zephyr,ipc-icmsg

config DT_HAS_ZEPHYR_IPC_ICMSG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG))

DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_FOLLOWER := zephyr,ipc-icmsg-me-follower

config DT_HAS_ZEPHYR_IPC_ICMSG_ME_FOLLOWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_FOLLOWER))

DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_INITIATOR := zephyr,ipc-icmsg-me-initiator

config DT_HAS_ZEPHYR_IPC_ICMSG_ME_INITIATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_INITIATOR))

DT_COMPAT_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS := zephyr,ipc-openamp-static-vrings

config DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS))

DT_COMPAT_ZEPHYR_KSCAN_INPUT := zephyr,kscan-input

config DT_HAS_ZEPHYR_KSCAN_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_KSCAN_INPUT))

DT_COMPAT_ZEPHYR_LOG_UART := zephyr,log-uart

config DT_HAS_ZEPHYR_LOG_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LOG_UART))

DT_COMPAT_ZEPHYR_LVGL_BUTTON_INPUT := zephyr,lvgl-button-input

config DT_HAS_ZEPHYR_LVGL_BUTTON_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_BUTTON_INPUT))

DT_COMPAT_ZEPHYR_LVGL_ENCODER_INPUT := zephyr,lvgl-encoder-input

config DT_HAS_ZEPHYR_LVGL_ENCODER_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_ENCODER_INPUT))

DT_COMPAT_ZEPHYR_LVGL_KEYPAD_INPUT := zephyr,lvgl-keypad-input

config DT_HAS_ZEPHYR_LVGL_KEYPAD_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_KEYPAD_INPUT))

DT_COMPAT_ZEPHYR_LVGL_POINTER_INPUT := zephyr,lvgl-pointer-input

config DT_HAS_ZEPHYR_LVGL_POINTER_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_POINTER_INPUT))

DT_COMPAT_ZEPHYR_MBOX_IPM := zephyr,mbox-ipm

config DT_HAS_ZEPHYR_MBOX_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MBOX_IPM))

DT_COMPAT_ZEPHYR_MDIO_GPIO := zephyr,mdio-gpio

config DT_HAS_ZEPHYR_MDIO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MDIO_GPIO))

DT_COMPAT_ZEPHYR_MEMORY_REGION := zephyr,memory-region

config DT_HAS_ZEPHYR_MEMORY_REGION_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MEMORY_REGION))

DT_COMPAT_ZEPHYR_MIPI_DBI_BITBANG := zephyr,mipi-dbi-bitbang

config DT_HAS_ZEPHYR_MIPI_DBI_BITBANG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MIPI_DBI_BITBANG))

DT_COMPAT_ZEPHYR_MIPI_DBI_SPI := zephyr,mipi-dbi-spi

config DT_HAS_ZEPHYR_MIPI_DBI_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MIPI_DBI_SPI))

DT_COMPAT_ZEPHYR_MMC_DISK := zephyr,mmc-disk

config DT_HAS_ZEPHYR_MMC_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MMC_DISK))

DT_COMPAT_ZEPHYR_MODBUS_SERIAL := zephyr,modbus-serial

config DT_HAS_ZEPHYR_MODBUS_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MODBUS_SERIAL))

DT_COMPAT_ZEPHYR_MSPI_EMUL_CONTROLLER := zephyr,mspi-emul-controller

config DT_HAS_ZEPHYR_MSPI_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MSPI_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_MSPI_EMUL_DEVICE := zephyr,mspi-emul-device

config DT_HAS_ZEPHYR_MSPI_EMUL_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MSPI_EMUL_DEVICE))

DT_COMPAT_ZEPHYR_MSPI_EMUL_FLASH := zephyr,mspi-emul-flash

config DT_HAS_ZEPHYR_MSPI_EMUL_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MSPI_EMUL_FLASH))

DT_COMPAT_ZEPHYR_NATIVE_LINUX_CAN := zephyr,native-linux-can

config DT_HAS_ZEPHYR_NATIVE_LINUX_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_LINUX_CAN))

DT_COMPAT_ZEPHYR_NATIVE_LINUX_EVDEV := zephyr,native-linux-evdev

config DT_HAS_ZEPHYR_NATIVE_LINUX_EVDEV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_LINUX_EVDEV))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_COUNTER := zephyr,native-posix-counter

config DT_HAS_ZEPHYR_NATIVE_POSIX_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_COUNTER))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_CPU := zephyr,native-posix-cpu

config DT_HAS_ZEPHYR_NATIVE_POSIX_CPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_CPU))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_RNG := zephyr,native-posix-rng

config DT_HAS_ZEPHYR_NATIVE_POSIX_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_RNG))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_UART := zephyr,native-posix-uart

config DT_HAS_ZEPHYR_NATIVE_POSIX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_UART))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_UDC := zephyr,native-posix-udc

config DT_HAS_ZEPHYR_NATIVE_POSIX_UDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_UDC))

DT_COMPAT_ZEPHYR_NATIVE_TTY_UART := zephyr,native-tty-uart

config DT_HAS_ZEPHYR_NATIVE_TTY_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_TTY_UART))

DT_COMPAT_ZEPHYR_NUS_UART := zephyr,nus-uart

config DT_HAS_ZEPHYR_NUS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NUS_UART))

DT_COMPAT_ZEPHYR_PANEL_TIMING := zephyr,panel-timing

config DT_HAS_ZEPHYR_PANEL_TIMING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_PANEL_TIMING))

DT_COMPAT_ZEPHYR_POWER_STATE := zephyr,power-state

config DT_HAS_ZEPHYR_POWER_STATE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_POWER_STATE))

DT_COMPAT_ZEPHYR_PSA_CRYPTO_RNG := zephyr,psa-crypto-rng

config DT_HAS_ZEPHYR_PSA_CRYPTO_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_PSA_CRYPTO_RNG))

DT_COMPAT_ZEPHYR_RAM_DISK := zephyr,ram-disk

config DT_HAS_ZEPHYR_RAM_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RAM_DISK))

DT_COMPAT_ZEPHYR_RETAINED_RAM := zephyr,retained-ram

config DT_HAS_ZEPHYR_RETAINED_RAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RETAINED_RAM))

DT_COMPAT_ZEPHYR_RETAINED_REG := zephyr,retained-reg

config DT_HAS_ZEPHYR_RETAINED_REG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RETAINED_REG))

DT_COMPAT_ZEPHYR_RETENTION := zephyr,retention

config DT_HAS_ZEPHYR_RETENTION_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RETENTION))

DT_COMPAT_ZEPHYR_RTC_EMUL := zephyr,rtc-emul

config DT_HAS_ZEPHYR_RTC_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RTC_EMUL))

DT_COMPAT_ZEPHYR_SDHC_SPI_SLOT := zephyr,sdhc-spi-slot

config DT_HAS_ZEPHYR_SDHC_SPI_SLOT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDHC_SPI_SLOT))

DT_COMPAT_ZEPHYR_SDL_DC := zephyr,sdl-dc

config DT_HAS_ZEPHYR_SDL_DC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDL_DC))

DT_COMPAT_ZEPHYR_SDMMC_DISK := zephyr,sdmmc-disk

config DT_HAS_ZEPHYR_SDMMC_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDMMC_DISK))

DT_COMPAT_ZEPHYR_SENSING := zephyr,sensing

config DT_HAS_ZEPHYR_SENSING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SENSING))

DT_COMPAT_ZEPHYR_SENSING_HINGE_ANGLE := zephyr,sensing-hinge-angle

config DT_HAS_ZEPHYR_SENSING_HINGE_ANGLE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SENSING_HINGE_ANGLE))

DT_COMPAT_ZEPHYR_SENSING_PHY_3D_SENSOR := zephyr,sensing-phy-3d-sensor

config DT_HAS_ZEPHYR_SENSING_PHY_3D_SENSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SENSING_PHY_3D_SENSOR))

DT_COMPAT_ZEPHYR_SIM_EEPROM := zephyr,sim-eeprom

config DT_HAS_ZEPHYR_SIM_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SIM_EEPROM))

DT_COMPAT_ZEPHYR_SIM_FLASH := zephyr,sim-flash

config DT_HAS_ZEPHYR_SIM_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SIM_FLASH))

DT_COMPAT_ZEPHYR_SPI_BITBANG := zephyr,spi-bitbang

config DT_HAS_ZEPHYR_SPI_BITBANG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SPI_BITBANG))

DT_COMPAT_ZEPHYR_SPI_EMUL_CONTROLLER := zephyr,spi-emul-controller

config DT_HAS_ZEPHYR_SPI_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SPI_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_SWDP_GPIO := zephyr,swdp-gpio

config DT_HAS_ZEPHYR_SWDP_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SWDP_GPIO))

DT_COMPAT_ZEPHYR_UAC2 := zephyr,uac2

config DT_HAS_ZEPHYR_UAC2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2))

DT_COMPAT_ZEPHYR_UAC2_AUDIO_STREAMING := zephyr,uac2-audio-streaming

config DT_HAS_ZEPHYR_UAC2_AUDIO_STREAMING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2_AUDIO_STREAMING))

DT_COMPAT_ZEPHYR_UAC2_CLOCK_SOURCE := zephyr,uac2-clock-source

config DT_HAS_ZEPHYR_UAC2_CLOCK_SOURCE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2_CLOCK_SOURCE))

DT_COMPAT_ZEPHYR_UAC2_FEATURE_UNIT := zephyr,uac2-feature-unit

config DT_HAS_ZEPHYR_UAC2_FEATURE_UNIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2_FEATURE_UNIT))

DT_COMPAT_ZEPHYR_UAC2_INPUT_TERMINAL := zephyr,uac2-input-terminal

config DT_HAS_ZEPHYR_UAC2_INPUT_TERMINAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2_INPUT_TERMINAL))

DT_COMPAT_ZEPHYR_UAC2_OUTPUT_TERMINAL := zephyr,uac2-output-terminal

config DT_HAS_ZEPHYR_UAC2_OUTPUT_TERMINAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UAC2_OUTPUT_TERMINAL))

DT_COMPAT_ZEPHYR_UART_EMUL := zephyr,uart-emul

config DT_HAS_ZEPHYR_UART_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UART_EMUL))

DT_COMPAT_ZEPHYR_UDC_SKELETON := zephyr,udc-skeleton

config DT_HAS_ZEPHYR_UDC_SKELETON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UDC_SKELETON))

DT_COMPAT_ZEPHYR_UDC_VIRTUAL := zephyr,udc-virtual

config DT_HAS_ZEPHYR_UDC_VIRTUAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UDC_VIRTUAL))

DT_COMPAT_ZEPHYR_UHC_VIRTUAL := zephyr,uhc-virtual

config DT_HAS_ZEPHYR_UHC_VIRTUAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UHC_VIRTUAL))

DT_COMPAT_ZEPHYR_USB_C_VBUS_ADC := zephyr,usb-c-vbus-adc

config DT_HAS_ZEPHYR_USB_C_VBUS_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_USB_C_VBUS_ADC))

DT_COMPAT_ZEPHYR_USB_C_VBUS_TCPCI := zephyr,usb-c-vbus-tcpci

config DT_HAS_ZEPHYR_USB_C_VBUS_TCPCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_USB_C_VBUS_TCPCI))

DT_COMPAT_ZEPHYR_VIDEO_EMUL_IMAGER := zephyr,video-emul-imager

config DT_HAS_ZEPHYR_VIDEO_EMUL_IMAGER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_VIDEO_EMUL_IMAGER))

DT_COMPAT_ZEPHYR_VIDEO_EMUL_RX := zephyr,video-emul-rx

config DT_HAS_ZEPHYR_VIDEO_EMUL_RX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_VIDEO_EMUL_RX))

DT_COMPAT_ZEPHYR_W1_GPIO := zephyr,w1-gpio

config DT_HAS_ZEPHYR_W1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_W1_GPIO))

DT_COMPAT_ZEPHYR_W1_SERIAL := zephyr,w1-serial

config DT_HAS_ZEPHYR_W1_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_W1_SERIAL))
