# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file LICENSE.rst or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION ${CMAKE_VERSION}) # this file comes with cmake

# If CMAKE_DISABLE_SOURCE_CHANGES is set to true and the source directory is an
# existing directory in our source tree, calling file(MAKE_DIRECTORY) on it
# would cause a fatal error, even though it would be a no-op.
if(NOT EXISTS "/opt/nordic/ncs/v3.0.2/wifi_udp_latency")
  file(MAKE_DIRECTORY "/opt/nordic/ncs/v3.0.2/wifi_udp_latency")
endif()
file(MAKE_DIRECTORY
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/wifi_udp_latency"
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix"
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/tmp"
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp"
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src"
  "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp"
)

set(configSubDirs )
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "/opt/nordic/ncs/v3.0.2/wifi_udp_latency/build/_sysbuild/sysbuild/images/wifi_udp_latency-prefix/src/wifi_udp_latency-stamp${cfgdir}") # cfgdir has leading slash
endif()
