/*
 * Copyright (c) 2025 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>
#include <zephyr/net/wifi_mgmt.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/net_event.h>
#include <zephyr/net/conn_mgr_monitor.h>
#include <zephyr/net/conn_mgr_connectivity.h>

#include "wifi_utils.h"
#include "udp_utils.h"
#include "led_utils.h"

LOG_MODULE_REGISTER(main, CONFIG_LOG_DEFAULT_LEVEL);

/* Define event masks for different network layers */
#define L2_EVENT_MASK (NET_EVENT_WIFI_CONNECT_RESULT | NET_EVENT_WIFI_DISCONNECT_RESULT)
#define L3_EVENT_MASK NET_EVENT_IPV4_DHCP_BOUND

/* Global semaphore to wait for network connectivity */
K_SEM_DEFINE(network_connected, 0, 1);

/* Separate event callbacks for L2 and L3 events */
static struct net_mgmt_event_callback wifi_mgmt_cb;
static struct net_mgmt_event_callback net_mgmt_cb;

/* WiFi/L2 event handler */
static void wifi_mgmt_event_handler(struct net_mgmt_event_callback *cb,
                                   uint32_t mgmt_event, struct net_if *iface)
{
    switch (mgmt_event) {
    case NET_EVENT_WIFI_CONNECT_RESULT:
        LOG_INF("WiFi connected");
        /* Print detailed WiFi status when connected */
        wifi_print_status();
        break;
    case NET_EVENT_WIFI_DISCONNECT_RESULT:
        LOG_INF("WiFi disconnected");
        break;
    default:
        break;
    }
}

/* Network/L3 event handler */
static void net_mgmt_event_handler(struct net_mgmt_event_callback *cb,
                                  uint32_t mgmt_event, struct net_if *iface)
{
    if ((mgmt_event & L3_EVENT_MASK) != mgmt_event) {
        return;
    }

    if (mgmt_event == NET_EVENT_IPV4_DHCP_BOUND) {
        k_sem_give(&network_connected);
        LOG_INF("DHCP bound");
        /* Print IP address information */
        wifi_print_dhcp_ip(cb);
        return;
    }
}

static int init_network_events(void)
{
    /* Initialize and register L2/WiFi event callback */
    net_mgmt_init_event_callback(&wifi_mgmt_cb, wifi_mgmt_event_handler, L2_EVENT_MASK);
    net_mgmt_add_event_callback(&wifi_mgmt_cb);
    
    /* Initialize and register L3/Network event callback */
    net_mgmt_init_event_callback(&net_mgmt_cb, net_mgmt_event_handler, L3_EVENT_MASK);
    net_mgmt_add_event_callback(&net_mgmt_cb);
    
    return 0;
}

#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX) || \
    (IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX) && \
     IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_TEST_MODE_AP))
static int connect_to_network(void)
{
    int ret;

    LOG_INF("Bringing up network interface");
    ret = conn_mgr_all_if_up(true);
    if (ret) {
        LOG_ERR("Failed to bring up network interface: %d", ret);
        return ret;
    }

    LOG_INF("Connecting to network");
    ret = conn_mgr_all_if_connect(true);
    if (ret) {
        LOG_ERR("Failed to connect to network: %d", ret);
        return ret;
    }

    return 0;
}
#endif

#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX)
static void udp_tx_task(void)
{
    int ret;
    int udp_socket;
    struct sockaddr_in server_addr;
    uint32_t packet_count = 0;
    int64_t start_time;
    uint32_t test_duration = CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS;
    uint32_t packet_interval = CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS;

    /* Wait for network connection */
    ret = k_sem_take(&network_connected, K_SECONDS(30));
    if (ret) {
        LOG_ERR("Timeout waiting for network connection");
        return;
    }
    LOG_INF("Starting UDP TX");

    /* Create UDP socket */
    ret = udp_client_init(&udp_socket, &server_addr, 
                         CONFIG_WIFI_UDP_LATENCY_TARGET_IP,
                         CONFIG_WIFI_UDP_LATENCY_UDP_PORT);
    if (ret) {
        LOG_ERR("Failed to initialize UDP client: %d", ret);
        return;
    }

    start_time = k_uptime_get();

    /* Main transmission loop */
    while ((k_uptime_get() - start_time) < test_duration) {
        char payload[64];
        int64_t current_time = k_uptime_get();

        /* Prepare payload with timestamp and packet count */
        snprintf(payload, sizeof(payload), "Packet_%u_Time_%lld", 
                packet_count, current_time);

        /* Trigger LED before transmission */
        led_trigger_tx();

        /* Send UDP packet */
        ret = udp_send(udp_socket, &server_addr, payload, strlen(payload));
        if (ret < 0) {
            LOG_ERR("Failed to send UDP packet: %d", ret);
        } else {
            LOG_INF("Sent: UDP packet %u at %lld ms", packet_count, current_time);
            packet_count++;
        }

        /* Wait for next transmission */
        k_sleep(K_MSEC(packet_interval));
    }

    LOG_INF("TX task completed. Sent %u packets", packet_count);
    udp_client_cleanup(udp_socket);
}
#endif /* CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX */

#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX)
static void udp_rx_task(void)
{
    int ret;
    int udp_socket;
    uint32_t packet_count = 0;

#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP)
    /* Set up SoftAP */
    ret = wifi_setup_softap(CONFIG_WIFI_UDP_LATENCY_SOFTAP_SSID,
                           CONFIG_WIFI_UDP_LATENCY_SOFTAP_PSK);
    if (ret) {
        LOG_ERR("Failed to setup SoftAP: %d", ret);
        return;
    }
    
    /* For SoftAP mode, network is ready immediately */
    k_sem_give(&network_connected);
#endif

    /* Wait for network connection */
    ret = k_sem_take(&network_connected, K_SECONDS(30));
    if (ret) {
        LOG_ERR("Timeout waiting for network connection");
        return;
    }

    LOG_INF("Starting UDP RX");

    /* Create UDP socket for receiving */
    ret = udp_server_init(&udp_socket, CONFIG_WIFI_UDP_LATENCY_UDP_PORT);
    if (ret) {
        LOG_ERR("Failed to initialize UDP server: %d", ret);
        return;
    }

    LOG_INF("UDP server listening on port %d", CONFIG_WIFI_UDP_LATENCY_UDP_PORT);

    /* Main reception loop */
    while (1) {
        char buffer[256];
        int64_t current_time = k_uptime_get();

        ret = udp_receive(udp_socket, buffer, sizeof(buffer));
        if (ret > 0) {
            /* Trigger LED when packet received */
            led_trigger_rx();
            
            buffer[ret] = '\0'; /* Null-terminate */
            LOG_INF("Received: %s at %lld ms", buffer, current_time);
            packet_count++;
        } else if (ret < 0) {
            LOG_ERR("Failed to receive UDP packet: %d", ret);
            k_sleep(K_MSEC(100));
        }
    }

    LOG_INF("RX task completed. Received %u packets", packet_count);
    udp_server_cleanup(udp_socket);
}
#endif /* CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX */

int main(void)
{
    int ret;

    LOG_INF("Wi-Fi UDP Latency Test Application");
    LOG_INF("Device Role: %s", 
           IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX) ? "TX" : "RX");
    LOG_INF("Test Mode: %s",
           IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_TEST_MODE_AP) ? "External AP" : "SoftAP");

    /* Initialize LEDs */
    ret = led_init();
    if (ret) {
        LOG_ERR("Failed to initialize LEDs: %d", ret);
        return ret;
    }

    /* Initialize network events */
    ret = init_network_events();
    if (ret) {
        LOG_ERR("Failed to initialize network events: %d", ret);
        return ret;
    }

#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX)
    /* For TX device, always connect to network first */
    ret = connect_to_network();
    if (ret) {
        LOG_ERR("Failed to connect to network: %d", ret);
        return ret;
    }
    
    udp_tx_task();
#else
    /* For RX device */
#if IS_ENABLED(CONFIG_WIFI_UDP_LATENCY_TEST_MODE_AP)
    /* In AP mode, RX device connects to external AP */
    ret = connect_to_network();
    if (ret) {
        LOG_ERR("Failed to connect to network: %d", ret);
        return ret;
    }
#endif
    udp_rx_task();
#endif

    return 0;
} 