#
# Copyright (c) 2025 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

# General
CONFIG_ASSERT=y
CONFIG_LOG=y
CONFIG_MAIN_STACK_SIZE=6144
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=4096
CONFIG_POSIX_API=y

# Wi-Fi
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y
CONFIG_WIFI_CREDENTIALS=y
CONFIG_WIFI_NM_WPA_SUPPLICANT=y

# Networking
CONFIG_NETWORKING=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_IPV4=y
CONFIG_NET_UDP=y
CONFIG_NET_TCP=y
CONFIG_NET_DHCPV4=y
CONFIG_NET_L2_ETHERNET=y
CONFIG_NET_CONNECTION_MANAGER=y

# Network Management
CONFIG_NET_MGMT=y
CONFIG_NET_MGMT_EVENT=y
CONFIG_NET_MGMT_EVENT_INFO=y

# Zephyr NET Connection Manager Connectivity layer
CONFIG_L2_WIFI_CONNECTIVITY=y
CONFIG_L2_WIFI_CONNECTIVITY_AUTO_CONNECT=n
CONFIG_L2_WIFI_CONNECTIVITY_AUTO_DOWN=n

# Network buffers and memory
CONFIG_NET_PKT_RX_COUNT=8
CONFIG_NET_PKT_TX_COUNT=8
CONFIG_NET_BUF_RX_COUNT=8
CONFIG_NET_BUF_TX_COUNT=16
CONFIG_NRF70_RX_NUM_BUFS=16
CONFIG_NRF70_MAX_TX_AGGREGATION=4
CONFIG_NRF_WIFI_CTRL_HEAP_SIZE=20000
CONFIG_NRF_WIFI_DATA_HEAP_SIZE=53000

# GPIO and DK library for LED/button support
CONFIG_DK_LIBRARY=y
CONFIG_GPIO=y

# Network config
CONFIG_NET_CONFIG_INIT_TIMEOUT=0
CONFIG_NET_CONTEXT_SYNC_RECV=y
CONFIG_NET_MAX_CONTEXTS=5

# Debugging
CONFIG_STACK_SENTINEL=y
CONFIG_DEBUG_COREDUMP=y
CONFIG_DEBUG_COREDUMP_BACKEND_LOGGING=y
CONFIG_DEBUG_COREDUMP_MEMORY_DUMP_MIN=y

# Application-specific configs
CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345
CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS=10000
CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS=500 