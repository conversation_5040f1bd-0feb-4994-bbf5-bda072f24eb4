#
# Configuration overlay for Test 2 TX Device
# TX device connects to RX device's SoftAP
# This device transmits UDP packets
#

# Device role and test mode
CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_TX=y
CONFIG_WIFI_UDP_LATENCY_TEST_MODE_SOFTAP=y

# Target IP address of RX device (Soft<PERSON> gateway IP)
CONFIG_WIFI_UDP_LATENCY_TARGET_IP="***********"

# Wi-Fi credentials for RX device's SoftAP
CONFIG_WIFI_CREDENTIALS_STATIC=y
#CONFIG_WIFI_CREDENTIALS_STATIC_SSID="YourWiFiSSID"
#CONFIG_WIFI_CREDENTIALS_STATIC_PASSWORD="YourWiFiPassword"
CONFIG_WIFI_CREDENTIALS_STATIC_SSID="CS_5G"
CONFIG_WIFI_CREDENTIALS_STATIC_PASSWORD="Charlie40396615"

# Test parameters
CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345
CONFIG_WIFI_UDP_LATENCY_TEST_DURATION_MS=10000
CONFIG_WIFI_UDP_LATENCY_PACKET_INTERVAL_MS=500

# Optimize for TX operations
CONFIG_MAIN_STACK_SIZE=8192
CONFIG_NET_TX_STACK_SIZE=4096 