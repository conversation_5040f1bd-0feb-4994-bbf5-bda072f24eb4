#
# Configuration overlay for Test 1 RX Device
# Both devices connect to an external AP
# This device receives UDP packets
#

# Device role and test mode
CONFIG_WIFI_UDP_LATENCY_DEVICE_ROLE_RX=y
CONFIG_WIFI_UDP_LATENCY_TEST_MODE_AP=y

# Wi-Fi credentials for external AP (configure these for your network)
CONFIG_WIFI_CREDENTIALS_STATIC=y
#CONFIG_WIFI_CREDENTIALS_STATIC_SSID="YourWiFiSSID"
#CONFIG_WIFI_CREDENTIALS_STATIC_PASSWORD="YourWiFiPassword"
CONFIG_WIFI_CREDENTIALS_STATIC_SSID="CS_5G"
CONFIG_WIFI_CREDENTIALS_STATIC_PASSWORD="Charlie40396615"

# Test parameters
CONFIG_WIFI_UDP_LATENCY_UDP_PORT=12345

# Optimize for RX operations
CONFIG_MAIN_STACK_SIZE=6144
CONFIG_NET_RX_STACK_SIZE=4096

# Static IP configuration (optional, can use DHCP)
# Uncomment and configure if you want to set a static IP
# CONFIG_NET_CONFIG_MY_IPV4_ADDR="***********00"
# CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
# CONFIG_NET_CONFIG_MY_IPV4_GW="***********" 