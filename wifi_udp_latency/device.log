[m
[1;32muart:~$ [m[8D[J[00:00:00.311,828] [0m<inf> wifi_nrf: Configuring SLEEP CTRL GPIO control register
[0m
[1;32muart:~$ [m[8D[J[00:00:00.453,643] [0m<inf> fs_zms: 2 Sectors of 4096 bytes[0m
[1;32muart:~$ [m[8D[J[00:00:00.453,674] [0m<inf> fs_zms: alloc wra: 0, fb0[0m
[1;32muart:~$ [m[8D[J[00:00:00.453,674] [0m<inf> fs_zms: data wra: 0, 0[0m
[1;32muart:~$ [m[8D[J*** Booting nRF Connect SDK v3.0.2-89ba1294ac9b ***
[1;32muart:~$ [m[8D[J*** Using Zephyr OS v4.0.99-f791c49f492c ***
[1;32muart:~$ [m[8D[J[00:00:00.454,498] [0m<inf> main: Wi-Fi UDP Latency Test Application[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,498] [0m<inf> main: Device Role: RX[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,528] [0m<inf> main: Test Mode: External AP[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,528] [0m<inf> led_utils: Initializing LEDs[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,589] [0m<inf> led_utils: LEDs initialized successfully[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,620] [0m<inf> led_utils: LED1: TX trigger (flashes when transmitting packets)[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,620] [0m<inf> led_utils: LED2: RX trigger (flashes when receiving packets)[0m
[1;32muart:~$ [m[8D[J[00:00:00.454,620] [0m<inf> main: Bringing up network interface[0m
[1;32muart:~$ [m[8D[J[00:00:00.455,566] [0m<inf> wifi_supplicant: wpa_supplicant initialized[0m
[1;32muart:~$ [m[8D[J[00:00:00.495,056] [0m<inf> main: Connecting to network[0m
[1;32muart:~$ [m[8D[J[1;32muart:~$ [m[8D[J[1;32muart:~$ [m[8D[JConnected
[1;32muart:~$ [m[8D[J[00:00:09.077,911] [0m<inf> main: Network connected[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,021] [0m<inf> wifi_utils: Wi-Fi Status: successful[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,052] [0m<inf> wifi_utils: ==================[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,052] [0m<inf> wifi_utils: State: COMPLETED[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,083] [0m<inf> wifi_utils: Interface Mode: STATION[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,113] [0m<inf> wifi_utils: SSID: CS_5G[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,113] [0m<inf> wifi_utils: BSSID: 4c:ed:fb:82:84:b4[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,144] [0m<inf> wifi_utils: Band: 5GHz[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,144] [0m<inf> wifi_utils: Channel: 149[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,174] [0m<inf> wifi_utils: Security: WPA2-PSK[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,174] [0m<inf> wifi_utils: RSSI: -42 dBm[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,296] [0m<inf> main: Starting UDP RX[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,601] [1;33m<wrn> udp_utils: Failed to set socket timeout: 109[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,601] [0m<inf> udp_utils: UDP server initialized on port 12345[0m
[1;32muart:~$ [m[8D[J[00:00:09.085,601] [0m<inf> main: UDP server listening on port 12345[0m
[1;32muart:~$ [m[8D[J[1;32muart:~$ [m0~sleep 2 && nrfutil device reset --serial-numer 1050769271[201~
[1;31m0~sleep: command not found
[m[1;32muart:~$ [m
[1;32muart:~$ [m[200~sleep 12 && cat device_output.log[201~
[1;31m[200~sleep: command not found
[m[1;32muart:~$ [m
[1;32muart:~$ [m
[1;32muart:~$ [m